#See https://aka.ms/containerfastmode to understand how Visual Studio uses this Dockerfile to build your images for faster debugging.

#这种模式是直接在构建镜像的内部编译发布dotnet项目。
#注意下容器内输出端口是9291
#如果你想先手动dotnet build成可执行的二进制文件，然后再构建镜像，请看.Api层下的dockerfile。


FROM mcr.microsoft.com/dotnet/aspnet:6.0-bullseye-slim AS base
WORKDIR /app
EXPOSE 80

FROM mcr.microsoft.com/dotnet/sdk:6.0-bullseye-slim AS build
WORKDIR /src
COPY ["SEFA.DFM.Api/SEFA.DFM.Api.csproj", "SEFA.DFM.Api/"]
COPY ["SEFA.DFM.Extensions/SEFA.DFM.Extensions.csproj", "SEFA.DFM.Extensions/"]
COPY ["SEFA.DFM.Tasks/SEFA.DFM.Tasks.csproj", "SEFA.DFM.Tasks/"]
COPY ["SEFA.DFM.IServices/SEFA.DFM.IServices.csproj", "SEFA.DFM.IServices/"]
COPY ["SEFA.DFM.Model/SEFA.DFM.Model.csproj", "SEFA.DFM.Model/"]
COPY ["SEFA.DFM.Common/SEFA.DFM.Common.csproj", "SEFA.DFM.Common/"]
COPY ["SEFA.DFM.Services/SEFA.DFM.Services.csproj", "SEFA.DFM.Services/"]
COPY ["SEFA.DFM.Repository/SEFA.DFM.Repository.csproj", "SEFA.DFM.Repository/"]
COPY ["SEFA.DFM.EventBus/SEFA.DFM.EventBus.csproj", "SEFA.DFM.EventBus/"]
RUN dotnet restore "SEFA.DFM.Api/SEFA.DFM.Api.csproj"
COPY . .
WORKDIR "/src/SEFA.DFM.Api"
RUN dotnet build "SEFA.DFM.Api.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "SEFA.DFM.Api.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
EXPOSE 9292 
ENTRYPOINT ["dotnet", "SEFA.DFM.Api.dll"]
