using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;

namespace SEFA.DFMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [Authorize(Permissions.Name)]
    public class ActionController : BaseApiController
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly IActionServices _actionServices;
    
        public ActionController(IActionServices ActionServices)
        {
            _actionServices = ActionServices;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<ActionEntity>>> GetList(string key = "")
        {
            Expression<Func<ActionEntity, bool>> whereExpression = a => true;
            var data = await _actionServices.FindList(whereExpression);
            return Success(data, "获取成功");

        }

        [HttpPost]
        public async Task<MessageModel<PageModel<ActionEntity>>> GetPageList([FromBody] ActionRequestModel reqModel)
        {
             
            Expression<Func<ActionEntity, bool>> whereExpression = a => true;
            var data = await _actionServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return Success(data, "获取成功");

        }

        [HttpGet("{id}")]
        public async Task<MessageModel<ActionEntity>> GetEntity(string id)
        {
            var data = await _actionServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] ActionEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _actionServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                } 
                else
                {
                    return Failed( "添加失败");
                }
            }
            else
            {
                data.success = await _actionServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                } 
                else
                {
                    return Failed( "更新失败");
                }
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _actionServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed( "删除失败");
            }
        }
    }
    public class ActionRequestModel : RequestPageModelBase
    {
        public string key { get; set; }
    }
}