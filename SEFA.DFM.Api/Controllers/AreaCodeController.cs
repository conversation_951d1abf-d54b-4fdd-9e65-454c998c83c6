using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;

namespace SEFA.DFMApi.Controllers
{
    /// <summary>
    /// 区域编码
    /// </summary>
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class AreaCodeController : BaseApiController
    {

        private readonly IAreaCodeServices _areaCodeServices;

        public AreaCodeController(IAreaCodeServices AreaCodeServices)
        {
            _areaCodeServices = AreaCodeServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<AreaCodeEntity>>> GetList(string key = "")
        {
            Expression<Func<AreaCodeEntity, bool>> whereExpression = a => true;
            var data = await _areaCodeServices.FindList(whereExpression);
            return Success(data, "获取成功");

        }

        [HttpPost]
        public async Task<MessageModel<PageModel<AreaCodeEntity>>> GetPageList([FromBody] AreaCodeRequestModel reqModel)
        {

            Expression<Func<AreaCodeEntity, bool>> whereExpression = a => true;
            var data = await _areaCodeServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return Success(data, "获取成功");

        }

        [HttpGet("{id}")]
        public async Task<MessageModel<AreaCodeEntity>> GetEntity(string id)
        {
            var data = await _areaCodeServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] AreaCodeEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _areaCodeServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _areaCodeServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _areaCodeServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    public class AreaCodeRequestModel : RequestPageModelBase
    {
        public string key { get; set; }
    }
}