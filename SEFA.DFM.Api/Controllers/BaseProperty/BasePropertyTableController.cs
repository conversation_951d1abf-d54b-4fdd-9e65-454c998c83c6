using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;

namespace SEFA.DFMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [Authorize(Permissions.Name)]
    public class BasePropertyTableController : BaseApiController
    {
        /// <summary>
        /// BasePropertyTable
        /// </summary>
        private readonly IBasePropertyTableServices _basePropertyTableServices;
    
        public BasePropertyTableController(IBasePropertyTableServices BasePropertyTableServices)
        {
            _basePropertyTableServices = BasePropertyTableServices;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<BasePropertyTableEntity>>> GetList([FromBody] BasePropertyTableRequestModel reqModel)
        {
            var data = await _basePropertyTableServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<BasePropertyTableEntity>>> GetPageList([FromBody] BasePropertyTableRequestModel reqModel)
        {
            var data = await _basePropertyTableServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<BasePropertyTableEntity>> GetEntity(string id)
        {
            var data = await _basePropertyTableServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] BasePropertyTableEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _basePropertyTableServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _basePropertyTableServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> Insert([FromBody] BasePropertyTableEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _basePropertyTableServices.Add(request) > 0;
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }
        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _basePropertyTableServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed( "删除失败");
            }
        }
    }
    //public class BasePropertyTableRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}