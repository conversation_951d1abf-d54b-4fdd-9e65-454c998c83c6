using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;

namespace SEFA.DFMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [Authorize(Permissions.Name)]
    public class BasePropertyValueController : BaseApiController
    {
        /// <summary>
        /// BasePropertyValue
        /// </summary>
        private readonly IBasePropertyValueServices _basePropertyValueServices;
    
        public BasePropertyValueController(IBasePropertyValueServices BasePropertyValueServices)
        {
            _basePropertyValueServices = BasePropertyValueServices;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<MaterialGroupPropertyValueModel>>> GetMaterialGroupPropertyList ([FromBody] MaterialGroupRequestModel reqModel)
        {
            var data = await _basePropertyValueServices.GetMaterialGroupPropertyList(reqModel);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 保存物料组属性
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> SaveMaterialGroupPropertyList ([FromBody] SaveMaterialGroupPropertyValueModel request)
        {
            return await _basePropertyValueServices.SaveMaterialGroupPropertyList(request);
        }

        [HttpPost]
        public async Task<MessageModel<string>> Insert([FromBody] BasePropertyValueEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _basePropertyValueServices.Add(request) > 0;
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }
       
    }
 
}