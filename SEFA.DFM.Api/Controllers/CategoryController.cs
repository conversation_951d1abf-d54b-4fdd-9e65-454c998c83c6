using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.DFM.Controllers;
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SqlSugar;
using System.Linq.Expressions;

namespace SEFA.DFMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class CategoryController : BaseApiController
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly ICategoryServices _categoryServices;

        public CategoryController(ICategoryServices CategoryServices)
        {
            _categoryServices = CategoryServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<CategoryEntity>>> GetList(string Identities = "")
        {
            Expression<Func<CategoryEntity, bool>> whereExpression = a => a.Deleted == 0 && a.Identities.Equals(Identities);
            var data = await _categoryServices.FindList(whereExpression);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<CategoryEntity>>> GetPageList([FromBody] CategoryRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<CategoryEntity>().And(a => a.Deleted == 0)
                  .AndIF(!string.IsNullOrEmpty(reqModel.key), a => a.Code.Contains(reqModel.key) || a.Name.Contains(reqModel.key))
                  .AndIF(!string.IsNullOrEmpty(reqModel.identity), a => a.Identities.Equals(reqModel.identity))
                  .ToExpression();
            var data = await _categoryServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return Success(data, "获取成功");

        }

        [HttpGet("{id}")]
        public async Task<MessageModel<CategoryEntity>> GetEntity(string id)
        {
            var data = await _categoryServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] CategoryEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _categoryServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _categoryServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _categoryServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    public class CategoryRequestModel : RequestPageModelBase
    {
        public string key { get; set; }
        public string identity { get; set; }
    }
}