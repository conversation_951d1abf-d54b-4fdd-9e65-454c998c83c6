using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Controllers;
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using SqlSugar;

namespace SEFA.DFM.Api.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class DataItemDetailController : BaseApiController
    {

        /// <summary>
        /// 
        /// </summary>
        private readonly IDataItemDetailServices _dataItemDetailServices;
        private readonly IMinioFileService _minioFileServices;
        public DataItemDetailController(IDataItemDetailServices dataItemDetailServices, IMinioFileService minioFileServices)
        {
            _dataItemDetailServices = dataItemDetailServices;
            _minioFileServices = minioFileServices;
        }


        #region 字典明细
        /// <summary>
        /// 获取数据字典明显根据分类编号
        /// </summary>
        /// <param name="itemCode">分类</param>
        /// <param name="lang">语言</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<DataItemDetailEntity>>> GetList(string itemCode, string lang)
        {
            if (string.IsNullOrEmpty(lang))
            {
                lang = "cn";
            }
            var whereExpression = Expressionable.Create<DataItemDetailEntity>().And(a => a.Deleted == 0 && a.Enable == 1)
                .And(a => itemCode.Equals(a.ItemCode))
                .And(a => a.Lang.Equals(lang)).ToExpression();

            List<DataItemDetailEntity> detailList = await _dataItemDetailServices.FindList(whereExpression, a => a.SortCode);
            //var data = detailList.OrderBy(a => a.SortCode);
            return Success(detailList, "获取成功");
        }

        /// <summary>
        /// 获取数据字典分页明显根据分类编号
        /// </summary>
        /// <param name="request">分类,语言,明细编号或名称</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<PageModel<DataItemDetailEntity>>> GetPageList([FromBody] DataItemDetailRequestModel request)
        {
            if (string.IsNullOrEmpty(request.lang))
            {
                request.lang = "cn";
            }

            //不要擅自改动 m r,m代表dataitem r代表dataitemdetail
            var whereExpression = Expressionable.Create<DataItemDetailEntity, DataItemEntity>()
            //根节点显示所有的明细项
            .AndIF(!string.IsNullOrEmpty(request.RootId), (r, m) => m.ParentId == request.RootId)
            .AndIF(!string.IsNullOrEmpty(request.itemCode), (r, m) => m.ItemCode == request.itemCode)
            .AndIF(!string.IsNullOrEmpty(request.lang), (r, m) => m.Lang == request.lang)
            .AndIF(!string.IsNullOrEmpty(request.key), (r, m) => r.ItemName.Contains(request.key) || r.ItemValue.Contains(request.key))
            .ToExpression();
            var detailList = await _dataItemDetailServices.QueryPageList(whereExpression, request.pageIndex, request.pageSize);

            return Success(detailList, "获取成功");
        }

        /// <summary>
        /// 获取数据字典明显树形数据
        /// </summary>
        /// <param name="itemCode">分类编号</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<TreeModel>>> GetDetailTree(string itemCode)
        {
            var whereExpression = Expressionable.Create<DataItemDetailEntity>().And(a => a.Deleted == 0).And(a => itemCode.Equals(a.ItemCode)).ToExpression();

            List<DataItemDetailEntity> detailList = await _dataItemDetailServices.FindList(whereExpression);
            List<TreeModel> treeList = new List<TreeModel>();
            foreach (DataItemDetailEntity item in detailList)
            {
                TreeModel node = new TreeModel();
                node.id = item.ID;
                node.name = item.ItemName;
                node.value = item.ItemValue;
                node.showcheck = false;
                node.checkstate = 0;
                node.isexpand = true;
                node.parentId = ((item.ParentId == null) ? "0" : item.ParentId);
                treeList.Add(node);
            }
            return new MessageModel<List<TreeModel>>()
            {
                msg = "获取成功",
                success = true,
                response = treeList.ToTree()
            };
        }

        /// <summary>
        /// 保存明细数据实体
        /// </summary>
        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] DataItemDetailEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                if (await _dataItemDetailServices.isExistName(request, false))
                {
                    return Failed("项目名称不能重复");
                }
                if (await _dataItemDetailServices.isExistValue(request, false))
                {
                    return Failed("项目值不能重复");
                }
                data.success = await _dataItemDetailServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                if (await _dataItemDetailServices.isExistName(request, true))
                {
                    return Failed("项目名称不能重复");
                }
                if (await _dataItemDetailServices.isExistValue(request, true))
                {
                    return Failed("项目值不能重复");
                }
                data.success = await _dataItemDetailServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }

        }
        /// <summary>
        /// 删除明细数据
        /// </summary>
        /// <param name="ids">主键</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> Delete(string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _dataItemDetailServices.DeleteByIds(ids);
            if (data.success)
            {
                data.msg = "删除成功";
            }
            return data;
        }
        #endregion


        /// <summary>
        /// 获取PDA版本
        /// <returns></returns>
        [HttpGet]
        public async Task<MessageModel<double>> GetPdaVersion()
        {
            double version = 0;
            var whereExpression = Expressionable.Create<DataItemDetailEntity>().And(a => a.Deleted == 0 && a.Enable == 1)
                .And(a => a.ItemCode == "PDA_CONFIG")
                .And(a => a.ItemName == "APK_VERSION").ToExpression();

            List<DataItemDetailEntity> detailList = await _dataItemDetailServices.FindList(whereExpression, a => a.SortCode);
            if (detailList.Count <= 0)
            {
                return Failed(version, "未维护PDA版本");
            }
            var value = detailList.FirstOrDefault().ItemValue;

            if (double.TryParse(value, out version))
            {
                return Success(version, "获取PDA版本成功");
            }
            else
            {
                return Failed(version, "获取PDA版本失败");
            }
        }




        public class DataItemDetailRequestModel : RequestPageModelBase
        {
            public string RootId { get; set; }
            public string itemCode { get; set; }

            public string lang { get; set; }

            public string key { get; set; }
        }
    }
}