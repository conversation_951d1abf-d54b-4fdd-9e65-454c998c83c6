using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;
using SEFA.DFM.IServices.DATAWAREHOUSE;
using SEFA.DFM.Model.Models.DATAWAREHOUSE;
using Magicodes.ExporterAndImporter.Excel.AspNetCore;
using Magicodes.ExporterAndImporter.Excel;
using Newtonsoft.Json;
using SEFA.DFM.Model.Models.Models.ViewModel;
using SqlSugar;
using AutoMapper;

namespace SEFA.DFMApi.Controllers
{
    /// <summary>
    /// 报表基础模型配置
    /// </summary>
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class DatamodelController : BaseApiController
    {

        private readonly IDatamodelServices _datamodelServices;
        private readonly IMapper _mapper;

        public DatamodelController(IDatamodelServices DatamodelServices, IMapper mapper)
        {
            _datamodelServices = DatamodelServices;
            _mapper = mapper;
        }

        [HttpPost]
        public async Task<MessageModel<List<DatamodelEntity>>> GetList(string key = "")
        {
            Expression<Func<DatamodelEntity, bool>> whereExpression = a => true;
            var data = await _datamodelServices.FindList(whereExpression);
            return Success(data, "获取成功");

        }

        [HttpPost]
        public async Task<MessageModel<PageModel<DatamodelEntity>>> GetPageList([FromBody] DatamodelRequestModel reqModel)
        {

            var whereExpression = Expressionable.Create<DatamodelEntity>()
                 .AndIF(!string.IsNullOrEmpty(reqModel.productline), a => a.Productline.Contains(reqModel.productline))
                 .AndIF(!string.IsNullOrEmpty(reqModel.segment), a => a.Segmentcode.Contains(reqModel.segment))
                 .AndIF(!string.IsNullOrEmpty(reqModel.productcode), a => a.ProductCode.Contains(reqModel.productcode))
                 .AndIF(!string.IsNullOrEmpty(reqModel.inmaterialcode), a => a.Inmaterialcode.Contains(reqModel.inmaterialcode))
                 .AndIF(!string.IsNullOrEmpty(reqModel.outmaterialcode), a => a.Outmaterialcode.Contains(reqModel.outmaterialcode))
                 .AndIF(!string.IsNullOrEmpty(reqModel.devicecode), a => a.Devicecode.Contains(reqModel.devicecode))
                 .AndIF(!string.IsNullOrEmpty(reqModel.processcode), a => a.Processcode.Contains(reqModel.processcode))
                 .AndIF(reqModel.isenabled != null, a => a.Enable == reqModel.isenabled)
                 .ToExpression();
            var data = await _datamodelServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return Success(data, "获取成功");

        }

        [HttpGet("{id}")]
        public async Task<MessageModel<DatamodelEntity>> GetEntity(string id)
        {
            var data = await _datamodelServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] DatamodelEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _datamodelServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _datamodelServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _datamodelServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> UpdateIsenabled([FromBody] DatamodelRequestModel reqModel)
        {
            var data = new MessageModel<string>();
            data.success = await _datamodelServices.UpdateIsenabled(reqModel.ids, reqModel.isenabled==null?0:int.Parse(reqModel.isenabled.ToString())) > 0;
            if (data.success)
            {
                return Success("", "修改成功");
            }
            else
            {
                return Failed("修改失败");
            }
        }

        /// <summary>
        /// 导入模板下载
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> ImportExcelTemplates()
        {
            ExcelImporter Importer = new ExcelImporter();
            var result = await Importer.GenerateTemplateBytes<DatamodelDto>();
            var fs = new MemoryStream(result);
            return new XlsxFileResult(stream: fs, fileDownloadName: "导入模板");
        }
        /// <summary>
        /// 导出数据
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<ActionResult> ExportExcelTemplates([FromBody] DatamodelRequestModel reqModel)
        {
            ExcelExporter exporter = new ExcelExporter();
            var whereExpression = Expressionable.Create<DatamodelEntity>()
                .AndIF(!string.IsNullOrEmpty(reqModel.productline), a => a.Productline.Contains(reqModel.productline))
                .AndIF(!string.IsNullOrEmpty(reqModel.segment), a => a.Segmentcode.Contains(reqModel.segment))
                .AndIF(!string.IsNullOrEmpty(reqModel.productcode), a => a.ProductCode.Contains(reqModel.productcode))
                .AndIF(!string.IsNullOrEmpty(reqModel.inmaterialcode), a => a.Inmaterialcode.Contains(reqModel.inmaterialcode))
                .AndIF(!string.IsNullOrEmpty(reqModel.outmaterialcode), a => a.Outmaterialcode.Contains(reqModel.outmaterialcode))
                .AndIF(!string.IsNullOrEmpty(reqModel.devicecode), a => a.Devicecode.Contains(reqModel.devicecode))
                .AndIF(!string.IsNullOrEmpty(reqModel.processcode), a => a.Processcode.Contains(reqModel.processcode))
                .AndIF(reqModel.isenabled != null, a => a.Enable == reqModel.isenabled)
                .ToExpression();
            var data = await _datamodelServices.FindList(whereExpression);
            List<DatamodelDto> datamodels = _mapper.Map<List<DatamodelDto>>(data);
            var result = await exporter.ExportAsByteArray<DatamodelDto>(datamodels);
            var fs = new MemoryStream(result);
            return new XlsxFileResult(stream: fs, fileDownloadName: "报表模型数据_" + DateTime.Now.Ticks.ToString());

        }
        /// <summary>
        /// 批量导入
        /// </summary>
        /// <param name="file"></param>
        /// <returns></returns>
        [Consumes("multipart/form-data")]
        [HttpPost]
        public async Task<MessageModel<string>> ImportExcelAsync(IFormFile file)
        {
            ExcelImporter Importer = new ExcelImporter();
            var stream = file.OpenReadStream();
            var import = await Importer.Import<DatamodelDto>(stream);
            if (import.Data.Count() < 1)
                return Failed("请导入有内容的表格");
            // 返回 导入异常信息
            if (import.Exception != null)
                return Failed(import.Exception.ToString());
            //异常错误
            if (import.RowErrors.Count > 0)
                return Failed(JsonConvert.SerializeObject(import.RowErrors));
            var studentList = new List<DatamodelEntity>();
            List<DatamodelDto> datamodels = new List<DatamodelDto>();
            foreach (var item in import.Data)
            {
                datamodels.Add(item);
                //DatamodelEntity student = new DatamodelEntity()
                //{
                //    ID = String.IsNullOrEmpty(item.ID) ? Guid.NewGuid().ToString() : item.ID,
                //    Ct = item.Ct,
                //    CtTag = item.CtTag,
                //    Enable = item.Enable,
                //    Devicecode = item.Devicecode,
                //    Faulttag = item.Faulttag,
                //    Inmaterialcode = item.Inmaterialcode,
                //    Inmaterialname = item.Inmaterialname,
                //    Ischokepoint = item.Ischokepoint,
                //    Intag = item.Intag,
                //    Isenabled = item.Isenabled,
                //    Isfeed = item.Isfeed,
                //    Isproductlinetag = item.Isproductlinetag,
                //    Issegmentmaterial = item.Issegmentmaterial,
                //    Issegmenttag = item.Issegmenttag,
                //    Istestsegment = item.Istestsegment,
                //    Ngtag = item.Ngtag,
                //    Oktag = item.Oktag,
                //    OrderNum = item.OrderNum,
                //    Orgct = item.Orgct,
                //    Outmaterialcode = item.Outmaterialcode,
                //    Outmaterialname = item.Outmaterialname,
                //    Processname = item.Processname,
                //    Projectname = item.Projectname,
                //    Productline = item.Productline,
                //    Segmentcode = item.Segmentcode,
                //    Segmentname = item.Segmentname,
                //    SegmentSeq = item.SegmentSeq,
                //    Sfcdbname = item.Sfcdbname,
                //    Sfcmachineid = item.Sfcmachineid,
                //    Stopstatustag = item.Stopstatustag
                //};

                //student.CreateCustomGuid("System");
            }
            studentList = _mapper.Map<List<DatamodelEntity>>(datamodels);

            //数据库列名
            List<DisableCondition> condition = studentList.Where(c => c.Enable == 1).GroupBy(c => new { c.Productline, c.ProductCode }).Distinct().Select(t => new DisableCondition { Productline = t.Key.Productline, ProductCode = t.Key.ProductCode }).ToList();
            var whereExpression = Expressionable.Create<DatamodelEntity>().And(c=>string.IsNullOrEmpty(c.Productline));
            foreach (DisableCondition entity in condition)
            {
                whereExpression.Or(c => c.Productline == entity.Productline && c.ProductCode != entity.ProductCode);
            }

            await _datamodelServices.Update(new DatamodelEntity { Enable = 0 }, whereExpression.ToExpression(), new List<string>() { "Enable" }, null);
            await _datamodelServices.StorageBigData(studentList, null);
            //var Count = _datamodelServices.AddBigData(studentList);
            //导入记录
            /*
            var fileName = file.FileName;
            string fileExtension = file.FileName.Substring(file.FileName.LastIndexOf(".") + 1);//获取文件名后缀
                                               
            //把stream转换成byte[]
            byte[] bytes = new byte[stream.Length];
            stream.Read(bytes, 0, bytes.Length);
            //设置当前流的位置为流的开始
            stream.Seek(0, SeekOrigin.Begin);
            //把byte[]写入文件
            FileStream fs = new FileStream("C:\\" + file.FileName, FileMode.Create);
            BinaryWriter bw = new BinaryWriter(fs);
            bw.Write(bytes);
            bw.Close();
            fs.Close();
            */
            return Success("", "成功");
        }

    }
    public class DatamodelRequestModel : RequestPageModelBase
    {
        public string key { get; set; }
        public string productline { get; set; }
        public string segment { get; set; }
        public string union { get; set; }
        public string materialcode { get; set; }
        public string productcode { get; set; }
        public string inmaterialcode { get; set; }
        public string devicecode { get; set; }
        public string outmaterialcode { get; set; }
        public string processcode { get; set; }

        public string[] ids { get; set; }
        public int? isenabled { get; set; }


    }

    public class DisableCondition
    {
        public string Productline { get; set; }
        public string ProductCode { get; set; }
    }
}