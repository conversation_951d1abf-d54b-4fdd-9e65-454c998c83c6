using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;
using SEFA.DFM.Model.ViewModels;
using SqlSugar;
using SEFA.Base.Common.Helper;

namespace SEFA.DFMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class DepartmentController : BaseApiController
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly IDepartmentServices _departmentServices;
        private readonly IClassServices _classServices;
        private readonly IClassMappingServices _classMappingServices;
        private readonly IDataItemDetailServices _dataItemDetailServices;
        private readonly IDepartmentPropertyValueServices _departmentPropertyValueServices;
        private readonly IPropertyServices _propertyServices;

        public DepartmentController(IDepartmentServices DepartmentServices, IDataItemDetailServices dataItemDetailServices, IClassServices classServices, IClassMappingServices classMappingServices, IDepartmentPropertyValueServices DepartmentPropertyValueServices, IPropertyServices PropertyServices)
        {
            _classServices = classServices;
            _classMappingServices = classMappingServices;
            _departmentServices = DepartmentServices;
            _dataItemDetailServices = dataItemDetailServices;
            _departmentPropertyValueServices = DepartmentPropertyValueServices;
            _propertyServices = PropertyServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<DepartmentEntity>>> GetList(string key = "")
        {
            var whereExpression = Expressionable.Create<DepartmentEntity>().And(a => a.Deleted == 0)
                .AndIF(!string.IsNullOrEmpty(key), a => a.Parentid.Contains(key)).ToExpression();
            var data = await _departmentServices.FindList(whereExpression);
            return Success(data, "获取成功");

        }

        /// <summary>
        /// 根据等级获取
        /// </summary>
        /// <param name="Level">Group集团  Plant公司 Department部门 Post岗位 Team班组</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<DepartmentEntity>>> GetListByLevel(string Level = "")
        {
            var whereExpression = Expressionable.Create<DepartmentEntity>().And(a => a.Deleted == 0)
                .AndIF(!string.IsNullOrEmpty(Level), a => a.LEVEL.Equals(Level)).ToExpression();
            var data = await _departmentServices.FindList(whereExpression);
            return Success(data, "获取成功");

        }

        /// <summary>
        /// 查询组织模型属性值列表
        /// </summary>
        /// <param name="departmentid"></param>
        /// <param name="key"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<DepartmentPropertyValueEntity>>> GetDepartmentPropertyList(string departmentid, string key = "")
        {
            var whereExpression = Expressionable.Create<DepartmentPropertyValueEntity>().And(a => a.Deleted == 0 && a.DepartmentId == departmentid).AndIF(!string.IsNullOrEmpty(key), a => a.PropertyValue.Contains(key)).ToExpression();

            var data = await _departmentPropertyValueServices.FindList(whereExpression);

            return Success(data, "获取成功");
        }
        /// <summary>
        /// 部门查询物理模型
        /// </summary>
        /// <param name="requestModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<PageModel<DepartmentEntityView>>> GetPageList(DepartmentRequestModel requestModel)
        {
            var whereExpression = Expressionable.Create<DepartmentEntity>().And(a => a.Deleted == 0 && (a.Parentid == requestModel.ParentId || a.ID == requestModel.ParentId))
                           .AndIF(!string.IsNullOrEmpty(requestModel.Key), a => a.Fullname.Contains(requestModel.Key) || a.Shortname.Contains(requestModel.Key)).ToExpression();
            var data = await _departmentServices.QueryPage(whereExpression, requestModel.pageIndex, requestModel.pageSize);
            //查询所有组织架构 关联上级使用
            var depAllExpression = Expressionable.Create<DepartmentEntity>().And(a => a.Deleted == 0).ToExpression();
            var depAlldata = await _departmentServices.FindList(whereExpression);
            //查询数据字典类型
            var dataItemExpression = Expressionable.Create<DataItemDetailEntity>().And(a => a.Deleted == 0)
                .And(a => a.ItemCode == requestModel.DataItemCode)
                .And(a => a.Lang.Equals(requestModel.lang)).ToExpression();

            List<DataItemDetailEntity> detailList = await _dataItemDetailServices.FindList(dataItemExpression);

            List<DepartmentEntityView> view = new List<DepartmentEntityView>();
            foreach (var item in data.data)
            {
                view.Add(new DepartmentEntityView()
                {
                    ID = item.ID,
                    DepartmentId = item.ID,
                    Companyid = item.Companyid,
                    Createusername = item.Createusername,
                    Deleted = item.Deleted,
                    Description = item.Description,
                    Email = item.Email,
                    Enabledmark = item.Enabledmark,
                    Encode = item.Encode,
                    Fax = item.Fax,
                    Fullname = item.Fullname,
                    Innerphone = item.Innerphone,
                    LEVEL = item.LEVEL,
                    LevelName = detailList?.FirstOrDefault(g => g.ItemValue == item.LEVEL)?.ItemName,
                    Manager = item.Manager,
                    Modifyusername = item.Modifyusername,
                    Nature = item.Nature,
                    Outerphone = item.Outerphone,
                    Parentid = item.Parentid,
                    ParentName = depAlldata?.FirstOrDefault(g => g.ID == item.Parentid)?.Fullname,
                    Shortname = item.Shortname,
                    Sortcode = item.Sortcode
                });
            }
            if (view.Count > 1)
            {
                view.RemoveAll(t => t.ID == requestModel.ParentId);
            }
            return new MessageModel<PageModel<DepartmentEntityView>>()
            {
                msg = "获取成功",
                success = true,
                response = new PageModel<DepartmentEntityView>
                {
                    page = data.page,
                    dataCount = data.dataCount,
                    pageSize = data.pageSize,
                    data = view
                }
            };
        }


        /// <summary>
        /// 获取套件列表
        /// </summary>
        /// <param name="departmentid"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<DepartmentClassView>> GetDepartmentClassList(string departmentid)
        {
            var whereExpression = Expressionable.Create<ClassEntity>().And(a => a.Deleted == 0 && a.ClassType == ClassTypeEnum.DepartmentClass.ToString()).ToExpression();

            var data = await _classServices.FindList(whereExpression);

            var depExpression = Expressionable.Create<ClassMappingEntity>().And(a => a.Deleted == 0 && a.MappingId == departmentid).ToExpression();

            var mapdata = await _classMappingServices.FindList(depExpression);

            List<string> selectClassIds = mapdata.Select(g => g.ClassId).ToList();

            var selectClassList = data.Where(g => selectClassIds.Contains(g.ID)).ToList();

            DepartmentClassView equipmentClassView = new DepartmentClassView()
            {
                Item = data.Where(g => !selectClassIds.Contains(g.ID)).ToList(),
                SelectItem = selectClassList
            };
            return Success(equipmentClassView, "获取成功");
        }



        [HttpGet("{id}")]
        public async Task<MessageModel<DepartmentEntity>> GetEntity(string id)
        {
            var data = await _departmentServices.QueryById(id);
            return Success(data, "获取成功");
        }
        /// <summary>
        /// 获取组织架构树形结构
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<TreeModel>>> GetTree()
        {
            var whereExpression = Expressionable.Create<DepartmentEntity>().And(a => a.Deleted == 0).ToExpression();
            var list = await _departmentServices.FindList(whereExpression);

            //获取树形结构
            List<TreeModel> treeList = new List<TreeModel>();
            foreach (var item in list)
            {
                TreeModel node = new TreeModel
                {
                    id = item.ID,
                    name = item.Fullname,
                    value = item.LEVEL,
                    showcheck = false,
                    checkstate = 0,
                    isexpand = false,
                    parentId = item.Parentid
                };
                treeList.Add(node);
            }
            return Success(treeList.ToTree(), "获取成功");
        }

        /// <summary>
        /// 保存部门组织模型
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] DepartmentEntity request)
        {
            var data = new MessageModel<string>();


            if (string.IsNullOrEmpty(request.ID))
            {
                if (await _departmentServices.isExist(request, false))
                {
                    return Failed("生产性,部级单位" + request.Description + "已存在");
                }

                var encodecountwhereExpression = Expressionable.Create<DepartmentEntity>()
                    .And(a => a.Deleted == 0 && a.Encode == request.Encode).ToExpression();
                var encodecount = await _departmentServices.FindList(encodecountwhereExpression);

                if (encodecount.Count > 0)
                {
                    return Failed("生产性,部级编码" + request.Encode + "已存在");
                }

                data.success = await _departmentServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _departmentServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _departmentServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }

        /// <summary>
        /// 删除扩展属性
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> DeleteDepartmentProperty([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _departmentPropertyValueServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }

        /// <summary>
        /// 保存部门组织模型属性值
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> SaveDepartmentProperty([FromBody] DepartmentPropertyValueEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                var whereExpression = Expressionable.Create<DepartmentPropertyValueEntity>().And(a => a.Deleted == 0 && a.PropertyCode == request.PropertyCode).ToExpression();

                var list = await _departmentPropertyValueServices.FindList(whereExpression);
                if (list != null && list.Count > 0)
                {
                    return Failed("已存在的编码");
                }
                data.success = await _departmentPropertyValueServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _departmentPropertyValueServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }

        /// <summary>
        /// 保存部门组织模型套件关联关系
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> SaveDepartmentClassMapping([FromBody] RequestClassMappingModel request)
        {
            //查询所有同级别模型   包含它自己
            var departmentModel = await _departmentServices.QueryById(request.MappingId);
            var depModelExpression = Expressionable.Create<DepartmentEntity>().And(a => a.Deleted == 0 && a.Parentid == departmentModel.Parentid).ToExpression();
            var peerData = await _departmentServices.FindList(depModelExpression);
            if (peerData != null && peerData.Count == 0)
            {
                return Failed("保存失败,未查询到组织模型信息");
            }
            List<string> depIds = peerData.Select(g => g.ID).ToList();
            var data = new MessageModel<string>() { success = true };
            if (depIds != null && depIds.Count > 0)
            {
                var mapWhereExpression = Expressionable.Create<ClassMappingEntity>().And(a => depIds.Contains(a.MappingId)).ToExpression();
                var mapData = await _classMappingServices.FindList(mapWhereExpression);

                List<string> mapIds = mapData.Select(g => g.ID).ToList();
                //先删除关联关系、再保存
                data.success = await _classMappingServices.DeleteByIds(mapIds.ToArray());
            }
            if (request.MappingList.Count > 0)
            {
                List<ClassMappingEntity> classMappings = new List<ClassMappingEntity>();
                foreach (var item in request.MappingList)
                {
                    foreach (var peer in peerData)
                    {
                        ClassMappingEntity entity = new ClassMappingEntity() { };
                        entity.ClassId = item.ClassId;
                        entity.MappingId = peer.ID;
                        entity.Deleted = 0;
                        entity.CreateCustomGuid("System");
                        classMappings.Add(entity);
                    }
                }
                data.success = await _classMappingServices.StorageList(classMappings, new string[] { "CLASS_ID", "MAPPING_ID" });
            }

            List<string> classIds = request.MappingList?.Select(g => g.ClassId).ToList();
            //删除已存在的Class默认关联属性值
            var propertyValueExpression = Expressionable.Create<DepartmentPropertyValueEntity>().And(a => a.Deleted == 0 && classIds.Contains(a.ClassId)).ToExpression();
            var propertyValueData = await _departmentPropertyValueServices.FindList(propertyValueExpression);
            List<string> propertyValueIds = propertyValueData?.Select(g => g.ID).ToList();
            if (propertyValueIds.Count > 0)
                await _departmentPropertyValueServices.DeleteByIds(propertyValueIds.ToArray());

            var propertyExpression = Expressionable.Create<PropertyEntity>().And(a => a.Deleted == 0 && classIds.Contains(a.ClassId)).ToExpression();

            //新增套件默认属性值
            var propertyData = await _propertyServices.FindList(propertyExpression);
            List<DepartmentPropertyValueEntity> list = new List<DepartmentPropertyValueEntity>();
            foreach (var item in propertyData)
            {
                foreach (var peer in peerData)
                {
                    DepartmentPropertyValueEntity propertyValueEntity = new DepartmentPropertyValueEntity()
                    {

                        ClassId = item.ClassId,
                        DepartmentId = peer.ID,
                        PropertyCode = item.PropertyCode,
                        PropertyValue = item.DefaultValue,
                        Remark = item.PropertyType?.ToString(),
                    };
                    propertyValueEntity.CreateCustomGuid("System");
                    list.Add(propertyValueEntity);
                }
            }
            if (list.Count > 0)
            {
                data.success = await _departmentPropertyValueServices.StorageList(list, new string[] { "CLASS_ID", "PROPERTY_CODE" });
            }
            if (data.success)
            {
                return Success("", "保存成功");
            }
            else
            {
                return Failed("保存失败");
            }
        }
    }
    public class DepartmentRequestModel : RequestPageModelBase
    {
        public string CompanyId { get; set; }
        public string ParentId { get; set; }
        public string Key { get; set; }
        public string lang { get; set; }
        public string DataItemCode { get; set; }
    }
}