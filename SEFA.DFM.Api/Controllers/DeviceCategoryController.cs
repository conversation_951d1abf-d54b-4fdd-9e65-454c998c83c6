using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Linq.Expressions;
using SEFA.Base;
using SEFA.DFM.Controllers;
using SqlSugar;
using SEFA.DFM.Model.ViewModels;
using SEFA.Base.Common.Helper;

namespace SEFA.DFMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class DeviceCategoryController : BaseApiController
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly IDeviceCategoryServices _deviceCategoryServices;

        public DeviceCategoryController(IDeviceCategoryServices DeviceCategoryServices)
        {
            _deviceCategoryServices = DeviceCategoryServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<DeviceCategoryModel>>> GetList(string key = "")
        {
            var whereExpression = Expressionable.Create<DeviceCategoryEntity>().And(a => a.Deleted == 0)
            .AndIF(!string.IsNullOrEmpty(key), a => a.CategoryName.Contains(key)).ToExpression();

            var list = await _deviceCategoryServices.FindList(whereExpression);
            var categorylist = from Category in list
                               join CategoryD in list on Category.ParentId equals CategoryD.ID
                               into CategoryTD
                               from CategoryD in CategoryTD.DefaultIfEmpty()
                               join CategoryPD in list on CategoryD?.ParentId equals CategoryPD.ID
                               into CategoryPTD
                               from CategoryPD in CategoryPTD.DefaultIfEmpty()
                               select new DeviceCategoryModel()
                               {
                                   CATEGORY_ID = Category.ID,
                                   CATEGORY_NAME = Category.CategoryName,
                                   PARENT_ID = Category.ParentId,
                                   DESCRIBE = Category.Describe,
                                   PARENT_TEST = CategoryPD?.CategoryName,
                                   REMARK = Category.Remark,
                                   CREATEDATE = Category.CreateDate,
                                   CREATEUSERID = Category.CreateUserId,
                                   MODIFYDATE = Category.ModifyDate,
                                   MODIFYUSERID = Category.ModifyUserId,
                                   PARENT_NAME = CategoryD?.CategoryName
                               };
            var deviceCategoryModels = ToolsClass.Cast<DeviceCategoryModel>(categorylist).ToList();

            return new MessageModel<List<DeviceCategoryModel>>()
            {
                msg = "获取成功",
                success = true,
                response = deviceCategoryModels
            };
            //return Success(deviceCategoryModels, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<List<TreeModel>>> GetCategoryTree()
        {
            var whereExpression = Expressionable.Create<DeviceCategoryEntity>().And(a => a.Deleted == 0).ToExpression();

            List<DeviceCategoryEntity> data = await _deviceCategoryServices.FindList(whereExpression);
            List<TreeModel> treeList = new List<TreeModel>();
            foreach (DeviceCategoryEntity item in data)
            {
                TreeModel node = new TreeModel();
                node.id = item.ID;
                node.name = item.CategoryName;
                node.value = item.ID;
                node.showcheck = false;
                node.checkstate = 0;
                node.isexpand = true;
                node.parentId = item.ParentId;
                treeList.Add(node);
            }
            return new MessageModel<List<TreeModel>>()
            {
                msg = "获取成功",
                success = true,
                response = treeList.ToTree()
            };
        }

        /// <summary>
        /// 分页查询设备类型
        /// </summary>
        /// <param name="requestModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<PageModel<DeviceCategoryModel>>> GetPageList(DeviceCategoryRequestModel requestModel)
        {

            var whereExpression = Expressionable.Create<DeviceCategoryEntity>().And(a => a.Deleted == 0)
                .AndIF(!string.IsNullOrEmpty(requestModel.Key), a => a.CategoryName.Contains(requestModel.Key)).ToExpression();

            var list = await _deviceCategoryServices.QueryPage(whereExpression, requestModel.pageIndex, requestModel.pageSize);
            var categorylist = from Category in list.data
                               join CategoryD in list.data on Category.ParentId equals CategoryD.ID
                               into CategoryTD
                               from CategoryD in CategoryTD.DefaultIfEmpty()
                               join CategoryPD in list.data on CategoryD?.ParentId equals CategoryPD.ID
                               into CategoryPTD
                               from CategoryPD in CategoryPTD.DefaultIfEmpty()
                               select new DeviceCategoryModel()
                               {
                                   CATEGORY_ID = Category.ID,
                                   CATEGORY_NAME = Category.CategoryName,
                                   PARENT_ID = Category.ParentId,
                                   DESCRIBE = Category.Describe,
                                   PARENT_TEST = CategoryPD?.CategoryName,
                                   REMARK = Category.Remark,
                                   CREATEDATE = Category.CreateDate,
                                   CREATEUSERID = Category.CreateUserId,
                                   MODIFYDATE = Category.ModifyDate,
                                   MODIFYUSERID = Category.ModifyUserId,
                                   PARENT_NAME = CategoryD?.CategoryName,
                                   //CREATEDATE_NAME= LoginUserInfo.Get()
                               };

            var deviceCategoryModels = ToolsClass.Cast<DeviceCategoryModel>(categorylist).ToList();

            return new MessageModel<PageModel<DeviceCategoryModel>>()
            {
                msg = "获取成功",
                success = true,
                response = new PageModel<DeviceCategoryModel>
                {
                    page = list.page,
                    dataCount = list.dataCount,
                    pageSize = list.pageSize,
                    data = deviceCategoryModels
                }
            };
            //return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<DeviceCategoryEntity>> GetEntity(string id)
        {
            var data = await _deviceCategoryServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] DeviceCategoryEntity request)
        {
            var data = new MessageModel<string>();
            request.Deleted = 0;
            if (string.IsNullOrEmpty(request.ID))
            {
                var whereExpression = Expressionable.Create<DeviceCategoryEntity>().And(a => a.Deleted == 0 && a.CategoryName == request.CategoryName).ToExpression();

                List<DeviceCategoryEntity> list = await _deviceCategoryServices.FindList(whereExpression);
                if (list != null && list.Count > 0)
                {
                    return Failed("已存在的设备类型");
                }
                data.success = await _deviceCategoryServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _deviceCategoryServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _deviceCategoryServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    public class DeviceCategoryRequestModel : RequestPageModelBase
    {
        public string Key { get; set; }
    }
}