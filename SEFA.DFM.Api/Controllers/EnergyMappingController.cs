using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;
using SqlSugar;
using static SEFA.DFM.Model.ViewModels.EnergyDataType;
using SEFA.Base.Common.HttpContextUser;
using System.Reactive.Joins;
using System.Text.RegularExpressions;
using Minio.DataModel;

namespace SEFA.DFMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [Authorize(Permissions.Name)]
    public class EnergyMappingController : BaseApiController
    {
        /// <summary>
        /// EnergyMapping
        /// </summary>
        private readonly IEnergyMappingServices _energyMappingServices;
        private readonly IUser _user;
        public EnergyMappingController(IEnergyMappingServices EnergyMappingServices,IUser user)
        {
            _energyMappingServices = EnergyMappingServices;
            _user = user;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<EnergyMappingDataModel>>> GetList([FromBody] EnergyTagModel reqModel)
        {
            var data = await _energyMappingServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<EnergyMappingEntity>>> GetPageList([FromBody] EnergyTagModel reqModel)
        {
            var data = await _energyMappingServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<List<EnergyMappingDataModel>>> GetEquipmentTagValue ([FromBody] List<EnergyTagModel> reqModel) {
            var data = await _energyMappingServices.GetEquipmentTagValue(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<EnergyMappingEntity>> GetEntity(string id)
        {
            var data = await _energyMappingServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] EnergyMappingEntity request)
        {
            var data = new MessageModel<string>();

            if(string.IsNullOrWhiteSpace(request.Code))
            {
                return Failed("Code不可为空");
            }
            if (string.IsNullOrWhiteSpace(request.Name))
            {
                return Failed("Name不可为空");
            }
            if (string.IsNullOrWhiteSpace(request.Categroy))
            {
                return Failed("Categroy不可为空");
            }
            if (string.IsNullOrWhiteSpace(request.SumType))
            {
                return Failed("SumType不可为空");
            }
            if (string.IsNullOrWhiteSpace(request.SourceType))
            {
                return Failed("SourceType不可为空");
            }
            if (string.IsNullOrWhiteSpace(request.UnitId))
            {
                return Failed("计量单位不可为空");
            }
            if (string.IsNullOrWhiteSpace(request.Value))
            {
                return Failed("Value不可为空");
            }
            if (request.SourceType == EnergySourceType.Formula)
            {
                string pattern = @"\[([^\]]+)\]";
                //var str = request.Value.Replace("[", "").Replace("(", "").Replace(")", "").Replace("]", "").Replace(" ", "");
                //var valueNameList = str.Split(new char[] { '+' , '-', '*', '/' }).ToList();
                //for (int i = 0; i < valueNameList.Count ; i++)
                //{
                //    double num = 0;
                //    if(double.TryParse(valueNameList[i], out num))
                //    {
                //        valueNameList.RemoveAt(i);
                //        i--;
                //    }
                //}
                var matchCodeList = new List<string>();
                MatchCollection matchCollection = Regex.Matches(request.Value, pattern);

                // 遍历匹配结果
                foreach (Match match in matchCollection)
                {
                    // 获取捕获组中的内容
                    if (match.Groups.Count > 1)
                    {
                        matchCodeList.Add(match.Groups[1].Value);
                    }
                }

                var nameList = await _energyMappingServices.FindList(a => matchCodeList.Contains(a.Code));
                if(nameList.Count != matchCodeList.Count)
                {
                    return Failed($"公式存在未定义的Tag点Code[{string.Join(",",matchCodeList.Except(nameList.Select(a=>a.Code)))}]");
                }
                foreach(var item in nameList)
                {
                    if(item.UnitId != request.UnitId)
                    {
                        return Failed("公式定义Tag点位计量单位与当前计量单位不统一");
                    }
                }
                /*为了避免公式循环嵌套情况，公式最多只支持三级 一级公式二级公式三级接口*/
                foreach (var item in nameList)
                {
                    if (item.SourceType == EnergySourceType.Formula)
                    {
                        //var str2 = item.Value.Replace("[", "").Replace("]", "").Replace("(", "").Replace(")", "").Replace(" ", "");
                        //var valueNameList2 = str2.Split(new char[] { '+', '-', '*', '/' });

                        List<string> codeList2 = new List<string>();
                        MatchCollection matchCollection2 = Regex.Matches(item.Value, pattern);

                        // 遍历匹配结果
                        foreach (Match match in matchCollection2)
                        {
                            // 获取捕获组中的内容
                            if (match.Groups.Count > 1)
                            {
                                codeList2.Add(match.Groups[1].Value);
                            }
                        }

                        var nameList2 = await _energyMappingServices.FindList(a => codeList2.Contains(a.Code));
                        if(nameList2.Exists(a=>a.SourceType == "Formula"))
                        {
                            return Failed("公式定义不支持多级Tag点位公式");
                        }
                    }
                }
            }

            var sameCodeNameList = await _energyMappingServices.FindList(a => a.Code == request.Code  && a.ID != request.ID);
            if(sameCodeNameList.Count > 0)
            {
                return Failed("已存在相同Code");
            }
            var where = Expressionable.Create<EnergyMappingEntity>()
                .And(a => a.EquipmentId == request.EquipmentId)
                .And(a => a.Categroy == request.Categroy)
                .And(a => a.SourceType == request.SourceType)
                .And(a => a.SumType == request.SumType)
                .And(a => a.Value == request.Value).ToExpression();
            var sameDataList = await _energyMappingServices.FindList(where);

            sameDataList = sameDataList.Except(sameDataList.Where(a => a.ID == request.ID)).ToList();

            if (sameDataList.Count > 0)
            {
                return Failed("所选设备已存在相同配置数据");
            }

            if (string.IsNullOrEmpty(request.ID))
            {
                request.CreateCustomGuid(_user.Name);
                data.success = await _energyMappingServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                request.Modify(request.ID,_user.Name);
                data.success = await _energyMappingServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> Insert([FromBody] EnergyMappingEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _energyMappingServices.Add(request) > 0;
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }
        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _energyMappingServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed( "删除失败");
            }
        }
    }
    //public class EnergyMappingRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}