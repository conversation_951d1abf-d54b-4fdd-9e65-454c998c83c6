using LKK.Lib.Core;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using SEFA.Base.Common;
using SEFA.Base.Common.HttpContextUser;
using SEFA.Base.Common.LogHelper;
using SEFA.Base.ESB;
using SEFA.Base.Extensions;
using SEFA.Base.Model;
using SEFA.DFM.Common.EnterpriseWeChatCommon;
using SEFA.DFM.Controllers;
using SEFA.DFM.IServices;
using SEFA.DFM.Model.ViewModels;
using SqlSugar;
using StackExchange.Profiling.Internal;
using System.Xml.Serialization;

namespace SEFA.DFMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    //[AllowAnonymous]
    public class EnterpriseWeChatController : BaseApiController
    {
        /// <summary>
        /// EquipmentCapacity
        /// </summary>
        private readonly LKKESBHelper _iLkkEsbHelper;
        private readonly IUser _user;
        private readonly IUserinfoServices _userinfoServices;
        private readonly string AGENT_ID = Appsettings.app(new string[] { "EnterpriseWeChat", "AgentId" });
        private readonly IRedisBasketRepository _redisBasketRepository;
        private string CROP_ID;
        public EnterpriseWeChatController(LKKESBHelper lKKESBHelper, IUserinfoServices userinfoServices, IRedisBasketRepository redisBasketRepository)
        {
            _iLkkEsbHelper = lKKESBHelper;
            _userinfoServices = userinfoServices;
            _redisBasketRepository = redisBasketRepository;
        }

        /// <summary>
        /// 获取企业微信用户信息
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<MessageModel<EnterpriseWeChatUser>> GetEnterpriseWeChatUser(string code)
        {
            string getUserlogFileName = "EnterpriseWeChatUser ";
            SerilogServer.LogDebug("接收CODE:" + code, getUserlogFileName);
            string messageId = "EWX_Service";
            string request = $"<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns=\"http://tempuri.org/\"><soapenv:Header/><soapenv:Body><GetMESMessageAPPToken><agentid>{AGENT_ID}</agentid></GetMESMessageAPPToken></soapenv:Body></soapenv:Envelope>";

            string assess_token = string.Empty;

            string xpath = "/soap:Envelope/soap:Body/GetMESMessageAPPTokenResponse";
            var dic = new Dictionary<string, string>();
            dic.Add("soap", "http://schemas.xmlsoap.org/soap/envelope/");
            var replaceString = new List<string>();
            replaceString.Add("xmlns=\"http://tempuri.org/\"");

            try
            {
                //var responseResult = await _iLkkEsbHelper.PostXMLString(messageId, request);
                var responseResult = await _iLkkEsbHelper.PostXml2Model<GetMESMessageAPPTokenResponse>(messageId, request, xpath, null, dic, replaceString);
                SerilogServer.LogDebug("接口返回数据：" + responseResult.ToJson(), getUserlogFileName);

                if (responseResult.successed != true)
                {
                    SerilogServer.LogDebug("通过ESB接口获取企业微信Token失败：" + responseResult.msg, getUserlogFileName);
                    return Failed(new EnterpriseWeChatUser(), "获取企业微信Token失败:" + responseResult.msg);
                }
                if (responseResult.Response == null || responseResult.Response.Count == 0)
                {
                    SerilogServer.LogDebug("通过ESB接口获取企业微信Token失败,未返回token：" + responseResult.msg, getUserlogFileName);
                    return Failed(new EnterpriseWeChatUser(), "获取企业微信Token失败,未返回token:" + responseResult.msg);

                }
                assess_token = responseResult.Response[0].GetMESMessageAPPTokenResult;
                SerilogServer.LogDebug("获取企业微信Token成功,ASSESS_TOKEN:" + assess_token, getUserlogFileName);

            }
            catch (Exception ex)
            {
                SerilogServer.LogDebug("通过ESB接口获取企业微信Token异常:" + ex, getUserlogFileName);
                return Failed(new EnterpriseWeChatUser(), "获取企业微信Token异常");
            }


            // 使用访问令牌和用户 ID 获取用户信息
            var userInfoResponse = await new EnterpriseWeChatApiHelper().GetUserInfo(assess_token, code);
            if (userInfoResponse.success == false)
            {
                //无法获取用户信息
                SerilogServer.LogDebug("获取用户信息失败", getUserlogFileName);
                return Failed(new EnterpriseWeChatUser(), "获取用户信息失败:" + userInfoResponse.msg);
            }
            if (userInfoResponse.response == null)
            {
                //无法获取用户信息
                SerilogServer.LogDebug("无法获取用户信息:" + userInfoResponse.ToJson(), getUserlogFileName);
                return Failed(new EnterpriseWeChatUser(), "无法获取用户信息:" + userInfoResponse.response.errmsg);
            }

            string UserNo = string.Empty;
            if (userInfoResponse.response.errcode != 0)
            {
                /*如过发现Code重复消费，从redis中获取code*/
                if (userInfoResponse.response.errcode == 40029)
                {
                    SerilogServer.LogDebug("发现Code重复消费,从Redis缓存获取用户信息", getUserlogFileName);
                    if (await _redisBasketRepository.Exist(code))
                    {
                        UserNo = await _redisBasketRepository.GetValue(code);
                        SerilogServer.LogDebug("发现Code重复消费,在Redis缓存中获取用户工号:" + UserNo, getUserlogFileName);
                    }
                    else
                    {
                        SerilogServer.LogDebug("发现Code重复消费,在Redis缓存中未获取用户信息", getUserlogFileName);
                        return Failed(new EnterpriseWeChatUser(), "发现Code重复消费,在Redis缓存中未获取用户信息,无法获取用户信息:" + userInfoResponse.response.errmsg);
                    }
                }
                else
                {
                    //无法获取用户信息
                    SerilogServer.LogDebug("无法获取用户信息:" + userInfoResponse.ToJson(), getUserlogFileName);
                    return Failed(new EnterpriseWeChatUser(), "无法获取用户信息:" + userInfoResponse.response.errmsg);
                }
            }
            else
            {
                UserNo = userInfoResponse.response.userid;
                SerilogServer.LogDebug("获取用户信息成功:" + JsonConvert.SerializeObject(userInfoResponse), getUserlogFileName);
                await _redisBasketRepository.Set(code, UserNo, new TimeSpan(0, 0, 5, 0));
                SerilogServer.LogDebug($"设置用户信息到Redis[{code}][{UserNo}]" + userInfoResponse.ToJson(), getUserlogFileName);
            }

            var userInfo = await _userinfoServices.GetUserByUserNo(UserNo);
            if (userInfo == null)
            {
                SerilogServer.LogDebug("未维护工号:" + UserNo, getUserlogFileName);
                return Failed(new EnterpriseWeChatUser(), "未维护工号:" + UserNo);
            }
            userInfoResponse.response.userid = userInfo.LoginName;
            SerilogServer.LogDebug("获取MES用户信息成功:" + userInfo.LoginName, getUserlogFileName);
            return Success(userInfoResponse.response, "获取成功");
        }

        /// <summary>
        /// 向企业微信用户(可多个)发送消息
        /// </summary>
        /// <param name="info"></param>
        /// <returns></returns>
        [HttpPost]
        //[Route("SendMessageToEnterpriseWeChatUser")]
        public async Task<MessageModel<string>> SendMessageToEnterpriseWeChatUser([FromBody] MessageInfo info)
        {
            string logFileName = "sendMsglogFileName ";
            SerilogServer.LogDebug("接收信息:" + info.ToJson(), logFileName);
            string touser = string.Empty;
            if (info.isUserNo)
            {
                for (int i = 0; i < info.userList.Count; i++)
                {
                    info.userList[i] = info.userList[i].PadLeft(11, '0');
                }
                touser = string.Join('|', info.userList);
                SerilogServer.LogDebug($"直接传入工号：" + touser, logFileName);
            }
            else
            {
                var userlist =
                    await _userinfoServices.FindList(a => !string.IsNullOrEmpty(a.UserNo) && info.userList.Contains(a.LoginName));

                if (userlist.Count <= 0)
                {
                    return Failed("", "未获取到用户工号信息");
                }

                var userNoList = userlist.Select(a => a.UserNo).ToList();
                for (int i = 0; i < userNoList.Count; i++)
                {
                    userNoList[i] = userNoList[i].PadLeft(11, '0');
                }
                touser = string.Join('|', userNoList);

                SerilogServer.LogDebug($"查找获取用户工号：" + touser, logFileName);
            }
            if (string.IsNullOrEmpty(info.url) == false)
            {
                if (string.IsNullOrEmpty(CROP_ID))
                {
                    var response = await GetEnterpriseWeChatCorpId();
                    if (response.success == false)
                    {
                        return Failed("", response.msg);
                    }
                }
                info.SetRedirectUrl(CROP_ID, AGENT_ID);
            }


            string messageId = "EWX_Service";


            string request = $"<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns=\"http://tempuri.org/\"><soapenv:Header/><soapenv:Body><SendMESMessage><agentid>{AGENT_ID}</agentid><touser>{touser}</touser><message>{info.fullmessage}</message></SendMESMessage></soapenv:Body></soapenv:Envelope>";


            string xpath = "/soap:Envelope/soap:Body/SendMESMessageResponse/SendMESMessageResult/Data";
            var dic = new Dictionary<string, string>();
            dic.Add("soap", "http://schemas.xmlsoap.org/soap/envelope/");
            var replaceString = new List<string>();
            replaceString.Add("xmlns=\"http://tempuri.org/\"");

            //var esbResponse1 = await _iLkkEsbHelper.PostXMLString(messageId, request);

            try
            {
                SerilogServer.LogDebug($"发送信息messageId:{messageId},数据:" + info.ToJson(), logFileName);
                var esbResponse = await _iLkkEsbHelper.PostXml2Model<ResultData>(messageId, request, xpath, null, dic, replaceString);
                SerilogServer.LogDebug("接口返回数据：" + esbResponse.ToJson(), logFileName);

                if (esbResponse.successed == true)
                {
                    if (esbResponse.Response != null && esbResponse.Response[0].errcode == "0")
                    {
                        SerilogServer.LogDebug($"发送消息toUser{touser}成功:" + esbResponse.Response[0].ToJson(), logFileName);
                    }
                    else
                    {
                        SerilogServer.LogDebug($"发送消息toUser{touser}失败:" + esbResponse.Response?.ToJson(), logFileName);
                    }
                }
                else
                {
                    SerilogServer.LogDebug("发送消息失败::" + esbResponse.msg, logFileName);
                    return Failed("", "发送消息失败:" + esbResponse.msg);
                }
            }
            catch (Exception ex)
            {
                SerilogServer.LogDebug("发送消息异常:" + ex, logFileName);
                return Failed("", "发送消息异常");
            }
            return Success("", "发送成功");
        }



        /// <summary>
        /// 获取企业微信加密后的CorpId
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<MessageModel<string>> GetEnterpriseWeChatCorpId()
        {
            string getUserlogFileName = "EnterpriseWeChatCorpId ";
            string messageId = "EWX_Service";
            string encriptKey = Guid.NewGuid().ToString();
            string request = $"<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns=\"http://tempuri.org/\"><soapenv:Header/><soapenv:Body><GetCorpId><encriptKey>{encriptKey}</encriptKey></GetCorpId></soapenv:Body></soapenv:Envelope>";

            string CropId = string.Empty;

            string xpath = "/soap:Envelope/soap:Body/GetCorpIdResponse";
            var dic = new Dictionary<string, string>();
            dic.Add("soap", "http://schemas.xmlsoap.org/soap/envelope/");
            var replaceString = new List<string>();
            replaceString.Add("xmlns=\"http://tempuri.org/\"");

            try
            {
                var responseResult = await _iLkkEsbHelper.PostXml2Model<GetCorpIdResponse>(messageId, request, xpath, null, dic, replaceString);
                SerilogServer.LogDebug("接口返回数据：" + responseResult.ToJson(), getUserlogFileName);

                if (responseResult.successed != true)
                {
                    SerilogServer.LogDebug("通过ESB接口获取企业微信CorpId失败：" + responseResult.msg, getUserlogFileName);
                    return Failed("", "获取企业微信CorpId失败:" + responseResult.msg);
                }
                if (responseResult.Response == null || responseResult.Response.Count == 0)
                {
                    SerilogServer.LogDebug("通过ESB接口获取企业微信CorpId失败,未返回CorpId：" + responseResult.msg, getUserlogFileName);
                    return Failed("", "获取企业微信CorpId失败,未返回CorpId:" + responseResult.msg);

                }
                string result = responseResult.Response[0].GetCorpIdResult;
                SerilogServer.LogDebug("获取加密企业微信result,result:" + result, getUserlogFileName);
                CropId = Enigma.Decrypt(result, encriptKey);
                CROP_ID = CropId;
                SerilogServer.LogDebug("获取解密企业微信CorpId,CorpId:" + CropId, getUserlogFileName);

            }
            catch (Exception ex)
            {
                SerilogServer.LogDebug("通过ESB接口获取企业微信CorpId异常:" + ex, getUserlogFileName);
                return Failed("", "获取企业微信CorpId异常");
            }

            return Success(CropId, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> TestRedis(string findkey, string setValue)
        {
            if (await _redisBasketRepository.Exist(findkey))
            {
                var value = await _redisBasketRepository.GetValue(findkey);
                return Success(value, "成功");
            }
            else
            {
                await this._redisBasketRepository.Set(findkey, setValue, new TimeSpan(0, 0, 5, 0));
                return Success(setValue, "成功");
            }
        }
        public class MessageInfo
        {
            public MessageInfo()
            {

            }
            private string _cropId;
            private string _fullUrl;
            private string _fullmessage;
            /// <summary>
            /// 接收人的工号，多人可以用“|”隔开
            /// </summary>
            public List<string> userList { get; set; }
            /// <summary>
            /// 信息内容
            /// </summary>
            public string message { get; set; }
            /// <summary>
            /// 是否传入员工号
            /// </summary>
            public bool isUserNo { get; set; } = false;

            public string url { get; set; }

            public string urlName { get; set; }

            public string fullmessage
            {

                get
                {
                    _fullmessage = message;
                    if (!string.IsNullOrEmpty(url))
                    {
                        _fullmessage += "\n";

                        if (!string.IsNullOrEmpty(urlName))
                        {
                            _fullmessage += $"<a href=\"{_fullUrl}\">{urlName}</a>";
                        }
                        else
                        {
                            _fullmessage += $"<a href=\"{_fullUrl}\"</a>";
                        }
                    }
                    _fullmessage = _fullmessage
                        .Replace("&", "&amp;")
                        .Replace("<", "&lt;")
                        .Replace(">", "&gt;")
                        .Replace("\"", "&quot;")
                        .Replace("'", "&#39;");
                    return _fullmessage;
                }
            }

            public void SetRedirectUrl(string cropId, string agentId)
            {
                _fullUrl = "https://open.weixin.qq.com/connect/oauth2/authorize?appid={0}&redirect_uri={1}&response_type=code&scope=snsapi_base&state=STATE&agentid={2}#wechat_redirect";
                _fullUrl = string.Format(_fullUrl, cropId, url, agentId);
            }
        }

        /*
         * "你的快递已到，请携带工卡前往邮件中心领取。\n出发前可查看<a href=\"http://work.weixin.qq.com\">邮件中心视频实况</a>，聪明避开排队。"
         */

        public class GetCorpIdResponse
        {
            public string GetCorpIdResult { get; set; }
        }

        public static T XmlStringToObject<T>(string xmlString)
        {
            var serializer = new XmlSerializer(typeof(T));
            using var reader = new StringReader(xmlString);
            return (T)serializer.Deserialize(reader);
        }

    }
}