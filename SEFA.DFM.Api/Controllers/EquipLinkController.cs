using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.Base.Model;
using SEFA.DFM.Controllers;
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using System.Linq.Expressions;

namespace SEFA.DFMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class EquipLinkController : BaseApiController
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly IEquipLinkServices _equipLinkServices;
        private readonly IEquipNodeServices _equipNodeServices;
        private readonly IEquipmentServices _equipmentServices;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;

        public EquipLinkController(IEquipLinkServices EquipLinkServices, IUnitOfWork unitOfWork, IEquipmentServices equipmentServices, IEquipNodeServices equipNodeServices, IMapper mapper)
        {
            _equipLinkServices = EquipLinkServices;
            _unitOfWork = unitOfWork;
            _equipmentServices = equipmentServices;
            _equipNodeServices = equipNodeServices;
            _mapper = mapper;
        }

        [HttpPost]
        public async Task<MessageModel<List<EquipLinkEntity>>> GetList(string key = "")
        {
            Expression<Func<EquipLinkEntity, bool>> whereExpression = a => true;
            var data = await _equipLinkServices.FindList(whereExpression);
            return Success(data, "获取成功");

        }

        [HttpPost]
        public async Task<MessageModel<PageModel<EquipLinkEntity>>> GetPageList([FromBody] EquipLinkRequestModel reqModel)
        {

            Expression<Func<EquipLinkEntity, bool>> whereExpression = a => true;
            var data = await _equipLinkServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return Success(data, "获取成功");

        }

        [HttpGet("{id}")]
        public async Task<MessageModel<EquipLinkEntity>> GetEntity(string id)
        {
            var data = await _equipLinkServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] EquipLinkEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _equipLinkServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _equipLinkServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }
        /// <summary>
        /// 工艺路线明细保存
        /// link中 groupid 为 父节点id
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> SaveAll([FromBody] EquipGraphSaveModel request)
        {
            _unitOfWork.BeginTran();

            var data = new MessageModel<string>();
            try
            {
                await _equipNodeServices.Delete(a => a.GroupId.Equals(request.GroupId));

                await _equipLinkServices.Delete(a => a.GroupId.Equals(request.GroupId));

                if (request.nodeList != null && request.nodeList.Any())
                {
                    request.nodeList.ForEach(a => a.CreateCustomGuid("system"));
                    data.success = await _equipNodeServices.Add(request.nodeList) > 0;
                }
                if (request.linkList != null && request.linkList.Any())
                {
                    request.linkList.ForEach(a => a.CreateCustomGuid("system"));
                    data.success = await _equipLinkServices.Add(request.linkList) > 0;
                }
                data.success = true;
                _unitOfWork.CommitTran();

            }
            catch (Exception e)
            {
                _unitOfWork.RollbackTran();
                e.ToString();
            }
            if (data.success)
            {
                return Success("", "保存成功");
            }
            else
            {
                return Failed("保存失败");
            }
        }

        /// <summary>
        /// 获取设备路径图形
        /// </summary>
        /// <param name="GroupId">设备模型id</param>
        /// <returns></returns>
        [HttpGet]
        public async Task<MessageModel<EquipGraphDrawModel>> GetGraphList(string GroupId = "")
        {
            if (string.IsNullOrEmpty(GroupId))
            {
                return null;
            }

            var list = await _equipNodeServices.FindList(a => a.GroupId.Equals(GroupId));
            if (!list.Any())
            {
                return Failed(new EquipGraphDrawModel { }, "获取失败");
            }
            var eqs = list.Select(a => a.EquipId).ToList();

            var data = await _equipmentServices.FindList(a => eqs.Contains(a.ID));
            var dlist = _mapper.Map<List<EquipmentView>>(data);

            foreach (var item in dlist)
            {
                var s = list.Where(a => a.EquipId.Equals(item.ID)).FirstOrDefault();
                item.Coordinate = s.Coordinate;
                item.Width = s.Width;
                item.Height = s.Height;
            }


            var data1 = await _equipLinkServices.FindList(a => a.GroupId.Equals(GroupId));

            var datax = new EquipGraphDrawModel { nodeList = dlist, linkList = data1, GroupId = GroupId };
            return Success(datax, "获取成功");

        }

        /// <summary>
        /// 获取设备路径图形
        /// </summary>
        /// <param name="GroupId">设备模型id</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<EquipGraphDrawModel>> PostGraphList(string GroupId = "")
        {
            if (string.IsNullOrEmpty(GroupId))
            {
                return null;
            }

            var list = await _equipNodeServices.FindList(a => a.GroupId.Equals(GroupId));
            if (!list.Any())
            {
                return Failed(new EquipGraphDrawModel { }, "获取失败");
            }
            var eqs = list.Select(a => a.EquipId).ToList();

            var data = await _equipmentServices.FindList(a => eqs.Contains(a.ID));
            var dlist = _mapper.Map<List<EquipmentView>>(data);

            foreach (var item in dlist)
            {
                var s = list.Where(a => a.EquipId.Equals(item.ID)).FirstOrDefault();
                item.Coordinate = s.Coordinate;
                item.Width = s.Width;
                item.Height = s.Height;
            }


            var data1 = await _equipLinkServices.FindList(a => a.GroupId.Equals(GroupId));

            var datax = new EquipGraphDrawModel { nodeList = dlist, linkList = data1, GroupId = GroupId };
            return Success(datax, "获取成功");

        }
        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _equipLinkServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    public class EquipLinkRequestModel : RequestPageModelBase
    {
        public string key { get; set; }
    }

    public class EquipGraphDrawModel
    {
        public List<EquipmentView> nodeList { get; set; }
        public List<EquipLinkEntity> linkList { get; set; }
        public string GroupId { get; set; }

    }

    public class EquipGraphSaveModel
    {
        public List<EquipNodeEntity> nodeList { get; set; }
        public List<EquipLinkEntity> linkList { get; set; }
        public string GroupId { get; set; }

    }

}