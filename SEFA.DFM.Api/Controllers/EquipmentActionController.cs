using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;
using SEFA.DFM.Model.ViewModels;

namespace SEFA.DFMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [Authorize(Permissions.Name)]
    public class EquipmentActionController : BaseApiController
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly IEquipmentActionServices _equipmentActionServices;
    
        public EquipmentActionController(IEquipmentActionServices EquipmentActionServices)
        {
            _equipmentActionServices = EquipmentActionServices;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<EquipmentActionEntity>>> GetList(string key = "")
        {
            Expression<Func<EquipmentActionEntity, bool>> whereExpression = a => true;
            var data = await _equipmentActionServices.FindList(whereExpression);
            return Success(data, "获取成功");

        }

        [HttpPost]
        public async Task<MessageModel<PageModel<EquipmentActionEntity>>> GetPageList([FromBody] EquipmentActionRequestModel reqModel)
        {
             
            Expression<Func<EquipmentActionEntity, bool>> whereExpression = a => true;
            var data = await _equipmentActionServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return Success(data, "获取成功");

        }

        [HttpGet("{id}")]
        public async Task<MessageModel<EquipmentActionEntity>> GetEntity(string id)
        {
            var data = await _equipmentActionServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] EquipmentActionEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _equipmentActionServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                } 
                else
                {
                    return Failed( "添加失败");
                }
            }
            else
            {
                data.success = await _equipmentActionServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                } 
                else
                {
                    return Failed( "更新失败");
                }
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _equipmentActionServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed( "删除失败");
            }
        }

        /// <summary>
        /// 获取设备已激活Function下的可选Action
        /// 传 EquipmentId 
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<EquipmentActionModel>>> GetEquipmentActiveFunctionActionList (EquipmentActionModel model) {
            var data = await _equipmentActionServices.GetEquipmentActiveFunctionActionList(model.EquipmentId);
            return Success(data);
        }


        /// <summary>
        /// 获取设备已配置的Action
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<EquipmentActionModel>>> GetEquipmentAction (EquipmentActionModel model) {
            var data = await _equipmentActionServices.GetEquipmentAction(model.EquipmentId);
            return Success(data);
        }
        /// <summary>
        /// 保存设备已激活的Action
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> SaveEquipmentAction (EquActionRequestModel model) {
            return await _equipmentActionServices.SaveEquipmentAction(model.EquipmentId, model.ActiveActionList);
        }

        /// <summary>
        /// 新增设备Action
        /// </summary>
        /// <param name="model"></param>
        /// 传 EquipmentId 和 选择的 ActionId
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> AddEquipmentAction (EquipmentActionModel model) {
            return await _equipmentActionServices.AddEquipmentAction(model.EquipmentId, model.ActionId);
        }

        /// <summary>
        /// 删除设备Action
        /// </summary>
        /// <param name="model"></param>
        /// 传 EquipmentActionId
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> DelEquipmentAction (EquipmentActionModel model) {
            return await _equipmentActionServices.DelEquipmentAction(model.EquipmentActionId);
        }

    }
    public class EquipmentActionRequestModel : RequestPageModelBase
    {
        public string key { get; set; }
    }
}