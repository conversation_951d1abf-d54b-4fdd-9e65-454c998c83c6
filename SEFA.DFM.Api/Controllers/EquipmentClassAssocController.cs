using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;
using SEFA.DFM.Services;

namespace SEFA.DFMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [Authorize(Permissions.Name)]
    public class EquipmentClassAssocController : BaseApiController
    {
        /// <summary>
        /// EquipmentClassAssoc
        /// </summary>
        private readonly IEquipmentClassAssocServices _equipmentClassAssocServices;
    
        public EquipmentClassAssocController(IEquipmentClassAssocServices EquipmentClassAssocServices)
        {
            _equipmentClassAssocServices = EquipmentClassAssocServices;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<EquipmentClassAssocEntity>>> GetList([FromBody] EquipmentClassAssocRequestModel reqModel)
        {
            var data = await _equipmentClassAssocServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<EquipmentClassAssocEntity>>> GetPageList([FromBody] EquipmentClassAssocRequestModel reqModel)
        {
            Expression<Func<EquipmentClassAssocEntity, bool>> whereExpression = a => true;
            var data = await _equipmentClassAssocServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<EquipmentClassAssocEntity>> GetEntity(string id)
        {
            var data = await _equipmentClassAssocServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] EquipmentClassAssocEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _equipmentClassAssocServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _equipmentClassAssocServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _equipmentClassAssocServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed( "删除失败");
            }
        }
    }
    //public class EquipmentClassAssocRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}