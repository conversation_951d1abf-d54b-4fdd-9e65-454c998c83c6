using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;
using SEFA.DFM.Model.ViewModels;
using SEFA.DFM.Services;

namespace SEFA.DFMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [Authorize(Permissions.Name)]
    public class EquipmentFunctionController : BaseApiController
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly IEquipmentFunctionServices _equipmentFunctionServices;
    
        public EquipmentFunctionController(IEquipmentFunctionServices EquipmentFunctionServices)
        {
            _equipmentFunctionServices = EquipmentFunctionServices;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<EquipmentFunctionEntity>>> GetList(string key = "")
        {
            Expression<Func<EquipmentFunctionEntity, bool>> whereExpression = a => true;
            var data = await _equipmentFunctionServices.FindList(whereExpression);
            return Success(data, "获取成功");

        }

        [HttpPost]
        public async Task<MessageModel<PageModel<EquipmentFunctionEntity>>> GetPageList([FromBody] EquipmentFunctionRequestModel reqModel)
        {
             
            Expression<Func<EquipmentFunctionEntity, bool>> whereExpression = a => true;
            var data = await _equipmentFunctionServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return Success(data, "获取成功");

        }

        [HttpGet("{id}")]
        public async Task<MessageModel<EquipmentFunctionEntity>> GetEntity(string id)
        {
            var data = await _equipmentFunctionServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] EquipmentFunctionEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _equipmentFunctionServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                } 
                else
                {
                    return Failed( "添加失败");
                }
            }
            else
            {
                data.success = await _equipmentFunctionServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                } 
                else
                {
                    return Failed( "更新失败");
                }
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _equipmentFunctionServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed( "删除失败");
            }
        }

        /// <summary>
        /// 获取模型已激活Function
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<EquipmentFunctionModel>>> GetEquipmentActiveFunction (EquipmentFunctionModel model) {
            var data = new MessageModel<List<EquipmentFunctionModel>>();
            if (string.IsNullOrEmpty(model.EquipmentId))
            {
                data.msg = "设备ID不可为空";
                return data;
            }
            var list = await _equipmentFunctionServices.GetEquipmentActiveFunction(model.EquipmentId);
            return Success(list);
        }
        /// <summary>
        /// 获取模型可激活及已激活Function
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<EquipmentFunctionModel>>> GetEquipmentFunction (EquipmentFunctionModel model) {
            var data = await _equipmentFunctionServices.GetEquipmentFunction(model.EquipmentId);
            return Success(data);
        }
        /// <summary>
        /// 保存模型激活Function
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> SaveEquipmentFunction (EquFunctionRequestModel model) {
            return await _equipmentFunctionServices.SaveEquipmentFunction(model.EquipmentId,model.ActiveFunctionList);
        }
    }
    public class EquipmentFunctionRequestModel : RequestPageModelBase
    {
        public string key { get; set; }
    }
}