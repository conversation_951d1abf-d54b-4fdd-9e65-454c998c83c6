using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;
using SEFA.DFM.Services;
using SEFA.Base.Common.HttpContextUser;

namespace SEFA.DFMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [Authorize(Permissions.Name)]
    public class EquipmentGroupController : BaseApiController
    {
        /// <summary>
        /// EquipmentGroup
        /// </summary>
        private readonly IEquipmentGroupServices _equipmentGroupServices;
        private readonly IEquipmentGroupEquipServices _equipmentGroupEquipServices;
        private readonly IUser _user;

        public EquipmentGroupController(IEquipmentGroupServices EquipmentGroupServices,IUser user, IEquipmentGroupEquipServices equipmentGroupEquipServices)
        {
            _equipmentGroupServices = EquipmentGroupServices;
            _user = user;
            _equipmentGroupEquipServices = equipmentGroupEquipServices;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<EquipmentGroupEntity>>> GetList([FromBody] EquipmentGroupRequestModel reqModel)
        {
            var data = await _equipmentGroupServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<EquipmentGroupModel>>> GetPageList ([FromBody] EquipmentGroupRequestModel reqModel) {
            var data = await _equipmentGroupServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }
        [HttpGet("{id}")]
        public async Task<MessageModel<EquipmentGroupEntity>> GetEntity(string id)
        {
            var data = await _equipmentGroupServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] EquipmentGroupEntity request)
        {
            var data = new MessageModel<string>();


            if (string.IsNullOrEmpty(request.Description))
            {
                return Failed($"请录入组名称");
            }
            if (string.IsNullOrEmpty(request.Type))
            {
                return Failed($"请录入组类型");
            }

            var list = await _equipmentGroupServices.FindList(a => a.Description == request.Description && a.ID!= request.ID);

            if (list.Any())
            {
                return Failed($"已存在组名[{request.Description}]");
            }

            if (string.IsNullOrEmpty(request.ID))
            {
                request.CreateCustomGuid(_user.Name);
                data.success = await _equipmentGroupServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                var entity = await _equipmentGroupServices.FindEntity(request.ID);
                if(entity == null)
                {
                    return Failed("数据不存在");
                }

                request.Modify(request.ID,_user.Name);
                data.success = await _equipmentGroupServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();

            var list =await _equipmentGroupEquipServices.FindList(a => ids.Contains(a.EquipmentGroupRowId));
            if(list.Any())
            {
                return Failed("选中组已绑定设备,无法删除");
            }

            data.success = await _equipmentGroupServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed( "删除失败");
            }
        }
    }
    //public class EquipmentGroupRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}