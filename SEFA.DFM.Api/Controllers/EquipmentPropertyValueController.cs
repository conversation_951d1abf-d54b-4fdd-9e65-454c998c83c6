using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;
using SEFA.Base.Common.HttpContextUser;
using SEFA.Base.Common.HttpRestSharp;

namespace SEFA.DFMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class EquipmentPropertyValueController : BaseApiController
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly IEquipmentPropertyValueServices _equipmentPropertyValueServices;
        private readonly IUser _user;

        public EquipmentPropertyValueController(IEquipmentPropertyValueServices EquipmentPropertyValueServices, IUser user)
        {
            _equipmentPropertyValueServices = EquipmentPropertyValueServices;
            _user = user;
        }

        [HttpPost]
        public async Task<MessageModel<List<EquipmentPropertyValueEntity>>> GetList(string key = "")
        {
            Expression<Func<EquipmentPropertyValueEntity, bool>> whereExpression = a => true;
            var data = await _equipmentPropertyValueServices.FindList(whereExpression);
            return Success(data, "获取成功");

        }
        /// <summary>
        /// Andon 用
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<EquipmentPropertyValueEntity>>> AndonGetList(string key = "")
        {
            Expression<Func<EquipmentPropertyValueEntity, bool>> whereExpression = a => a.PropertyCode == "AndonPostTag";
            var data = await _equipmentPropertyValueServices.FindList(whereExpression);
            return Success(data, "获取成功");
        }
        /// <summary>
        /// 设备模块使用
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<EquipmentPropertyValueEntity>>> TPMGetList(string key = "")
        {
            Expression<Func<EquipmentPropertyValueEntity, bool>> whereExpression = a => a.PropertyCode == "是否接收ANDON数据";
            var data = await _equipmentPropertyValueServices.FindList(whereExpression);
            return Success(data, "获取成功");

        }

        [HttpPost]
        public async Task<MessageModel<PageModel<EquipmentPropertyValueEntity>>> GetPageList(int page = 1, string key = "", int intPageSize = 50)
        {

            Expression<Func<EquipmentPropertyValueEntity, bool>> whereExpression = a => true;
            var data = await _equipmentPropertyValueServices.QueryPage(whereExpression, page, intPageSize);
            return Success(data, "获取成功");

        }

        [HttpGet("{id}")]
        public async Task<MessageModel<EquipmentPropertyValueEntity>> GetEntity(string id)
        {
            var data = await _equipmentPropertyValueServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] EquipmentPropertyValueEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _equipmentPropertyValueServices.Add(request) > 0;
                if (data.success)
                {
                    var resdata = await HttpHelper.PostAsync<bool>("ANDON", "andon/AlarmRecord/GetAllAndonPostTag", _user.GetToken());
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _equipmentPropertyValueServices.Update(request);
                if (data.success)
                {
                    var resdata = await HttpHelper.PostAsync<bool>("ANDON", "andon/AlarmRecord/GetAllAndonPostTag", _user.GetToken());
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _equipmentPropertyValueServices.DeleteByIds(ids);
            if (data.success)
            {
                var resdata = await HttpHelper.PostAsync<bool>("ANDON", "andon/AlarmRecord/GetAllAndonPostTag", _user.GetToken());
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
}