using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;
using SEFA.DFM.Services;
using SqlSugar;

namespace SEFA.DFMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [Authorize(Permissions.Name)]
    public class EquipmentRequirementController : BaseApiController
    {
        /// <summary>
        /// EquipmentRequirement
        /// </summary>
        private readonly IEquipmentRequirementServices _equipmentRequirementServices;
    
        public EquipmentRequirementController(IEquipmentRequirementServices EquipmentRequirementServices)
        {
            _equipmentRequirementServices = EquipmentRequirementServices;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<EquipmentRequirementEntity>>> GetList([FromBody] EquipmentRequirementRequestModel reqModel)
        {
            var data = await _equipmentRequirementServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<EquipmentRequirementEntity>>> GetPageList([FromBody] EquipmentRequirementRequestModel reqModel)
        {
            Expression<Func<EquipmentRequirementEntity, bool>> whereExpression = a => true;
            var data = await _equipmentRequirementServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<EquipmentRequirementEntity>> GetEntity(string id)
        {
            var data = await _equipmentRequirementServices.QueryById(id);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 获取物理模型能源专用
        /// </summary>
        /// <param name="level"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<TreeModel>>> GetEquipmentRequirementTree (string level = "") {
            var whereExpression = Expressionable.Create<EquipmentRequirementEntity>()
                .AndIF(!string.IsNullOrEmpty(level), a => a.Level == level).ToExpression();
            var data = await _equipmentRequirementServices.FindList(whereExpression);
            List<TreeModel> treeList = new List<TreeModel>();
            foreach (var item in data)
            {
                TreeModel node = new TreeModel();
                node.id = item.ID;
                node.name = item.Code;
                node.showcheck = false;
                node.checkstate = 0;
                node.isexpand = true;
                node.parentId = item.ParentId ?? "0";
                treeList.Add(node);
            }

            return new MessageModel<List<TreeModel>>()
            {
                msg = "获取成功",
                success = true,
                response = treeList.OrderBy(it => it.name).ToList().ToTree()
            };
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] EquipmentRequirementEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _equipmentRequirementServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _equipmentRequirementServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _equipmentRequirementServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed( "删除失败");
            }
        }
    }
    //public class EquipmentRequirementRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}