using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;

namespace SEFA.DFMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [Authorize(Permissions.Name)]
     public class EquipmentattrController : BaseApiController
    {
            /// <summary>
            /// 
            /// </summary>
            private readonly IEquipmentattrServices _equipmentattrServices;
    
            public EquipmentattrController(IEquipmentattrServices EquipmentattrServices)
            {
                _equipmentattrServices = EquipmentattrServices;
            }
    
            [HttpPost]
            public async Task<MessageModel<List<EquipmentattrEntity>>> GetList(string key = "")
            {
                Expression<Func<EquipmentattrEntity, bool>> whereExpression = a => true;
                var data = await _equipmentattrServices.FindList(whereExpression);
                return Success(data, "获取成功");

            }

            [HttpPost]
            public async Task<MessageModel<PageModel<EquipmentattrEntity>>> GetPageList(int page = 1, string key = "",int intPageSize = 50)
            {
             
                Expression<Func<EquipmentattrEntity, bool>> whereExpression = a => true;
                var data = await _equipmentattrServices.QueryPage(whereExpression, page, intPageSize);
                return Success(data, "获取成功");

            }

            [HttpGet("{id}")]
            public async Task<MessageModel<EquipmentattrEntity>> GetEntity(string id)
            {
                var data = await _equipmentattrServices.QueryById(id);
                return Success(data, "获取成功");
            }

            [HttpPost]
            public async Task<MessageModel<string>> SaveForm([FromBody] EquipmentattrEntity request)
            {
                var data = new MessageModel<string>();
                if (string.IsNullOrEmpty(request.ID))
                {
                    data.success = await _equipmentattrServices.Add(request) > 0;
                    if (data.success)
                    {
                        return Success("", "添加成功");
                    } 
                    else
                    {
                        return Failed( "添加失败");
                    }
                }
                else
                {
                    data.success = await _equipmentattrServices.Update(request);
                    if (data.success)
                    {
                        return Success("", "更新成功");
                    } 
                    else
                    {
                        return Failed( "更新失败");
                    }
                }
            }


            [HttpPost]
            public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
            {
                var data = new MessageModel<string>();
                data.success = await _equipmentattrServices.DeleteByIds(ids);
                if (data.success)
                {
                    return Success("", "删除成功");
                }
                else
                {
                    return Failed( "删除失败");
                }
            }
    }
}