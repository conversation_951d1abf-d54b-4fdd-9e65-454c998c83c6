using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.DFM.Controllers;
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SqlSugar;
using System.Linq.Expressions;

namespace SEFA.DFMApi.Controllers
{
    /// <summary>
    /// 工作地点（厂房、楼层）
    /// </summary>
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class FactorySiteController : BaseApiController
    {


        private readonly IFactorySiteServices _factorySiteServices;

        private readonly IMapper _mapper;


        public FactorySiteController(IFactorySiteServices FactorySiteServices, IMapper mapper)
        {
            _mapper = mapper;
            _factorySiteServices = FactorySiteServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<FactorySiteEntity>>> GetList(string key = "")
        {
            Expression<Func<FactorySiteEntity, bool>> whereExpression = a => true;
            var data = await _factorySiteServices.FindList(whereExpression);
            return Success(data, "获取成功");

        }

        [HttpPost]
        public async Task<MessageModel<PageModel<FactorySiteEntity>>> GetPageList([FromBody] FactorySiteRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<FactorySiteEntity>().And(a => a.Deleted == 0)
                .AndIF(reqModel.key.IsNotEmptyOrNull(), x => x.Name.Contains(reqModel.key) || x.Code.Contains(reqModel.key))
                .AndIF(reqModel.ParentId.IsNotEmptyOrNull(), x => x.Parentid.Equals(reqModel.ParentId))
                .ToExpression();
            var data = await _factorySiteServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            if (data.data.Count == 0)
            {
                data = await _factorySiteServices.QueryPage(x => x.ID.Equals(reqModel.ParentId), reqModel.pageIndex, reqModel.pageSize);
            }
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<FactorySiteEntity>> GetEntity(string id)
        {
            var data = await _factorySiteServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] FactorySiteEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _factorySiteServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _factorySiteServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }


        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _factorySiteServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }

        /// <summary>
        /// 获取树结构数据
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<FactorySiteTreeDto>>> GetTree()
        {
            Expression<Func<FactorySiteEntity, bool>> whereExpression = a => true;
            var data = await _factorySiteServices.FindList(whereExpression);
            var result = GetChildren(data, "0");
            return Success(result, "获取成功");
        }

        List<FactorySiteTreeDto> GetChildren(List<FactorySiteEntity> allData, string parentid)
        {
            var result = new List<FactorySiteTreeDto>();
            var children = allData.Where(x => x.Parentid == parentid).ToList();
            foreach (var item in children)
            {
                var dto = _mapper.Map<FactorySiteTreeDto>(item);
                dto.children = GetChildren(allData, item.ID);
                result.Add(dto);
            }
            return result;
        }
    }
    public class FactorySiteRequestModel : RequestPageModelBase
    {
        public string key { get; set; }

        /// <summary>
        /// 父级ID
        /// </summary>
        public string ParentId { get; set; }

    }
}