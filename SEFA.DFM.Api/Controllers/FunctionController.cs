using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;

namespace SEFA.DFMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [Authorize(Permissions.Name)]
    public class FunctionController : BaseApiController
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly IFunctionServices _functionServices;
    
        public FunctionController(IFunctionServices FunctionServices)
        {
            _functionServices = FunctionServices;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<FunctionEntity>>> GetList(string key = "")
        {
            Expression<Func<FunctionEntity, bool>> whereExpression = a => true;
            var data = await _functionServices.FindList(whereExpression);
            return Success(data, "获取成功");

        }

        [HttpPost]
        public async Task<MessageModel<PageModel<FunctionEntity>>> GetPageList([FromBody] FunctionRequestModel reqModel)
        {
             
            Expression<Func<FunctionEntity, bool>> whereExpression = a => true;
            var data = await _functionServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return Success(data, "获取成功");

        }

        [HttpGet("{id}")]
        public async Task<MessageModel<FunctionEntity>> GetEntity(string id)
        {
            var data = await _functionServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] FunctionEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _functionServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                } 
                else
                {
                    return Failed( "添加失败");
                }
            }
            else
            {
                data.success = await _functionServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                } 
                else
                {
                    return Failed( "更新失败");
                }
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _functionServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed( "删除失败");
            }
        }
    }
    public class FunctionRequestModel : RequestPageModelBase
    {
        public string key { get; set; }
    }
}