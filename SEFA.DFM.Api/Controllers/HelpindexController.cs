using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;
using SEFA.DFM.Model.ViewModels;

namespace SEFA.DFMApi.Controllers
{
    /// <summary>
    /// 菜单 - SYS
    /// </summary>
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class HelpindexController : BaseApiController
    {

        private readonly IHelpindexServices _helpindexServices;

        public HelpindexController(IHelpindexServices HelpindexServices)
        {
            _helpindexServices = HelpindexServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<HelpindexEntity>>> GetList(string key = "")
        {
            Expression<Func<HelpindexEntity, bool>> whereExpression = a => true;
            var data = await _helpindexServices.FindList(whereExpression);
            return Success(data, "获取成功");

        }

        [HttpPost]
        public async Task<MessageModel<List<TreeModel>>> GetTreeList(string key = "")
        {
            var data = (await _helpindexServices.GetHelpindexTree()).OrderBy(it => it.name).ToList();
            return Success(data.ToTree(), "获取成功");

        }

        [HttpPost]
        public async Task<MessageModel<PageModel<HelpindexEntity>>> GetPageList([FromBody] HelpindexRequestModel reqModel)
        {

            Expression<Func<HelpindexEntity, bool>> whereExpression = a => true;
            var data = await _helpindexServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return Success(data, "获取成功");

        }

        [HttpGet("{id}")]
        public async Task<MessageModel<HelpindexEntity>> GetEntity(string id)
        {
            var data = await _helpindexServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] HelpindexEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _helpindexServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _helpindexServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _helpindexServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    public class HelpindexRequestModel : RequestPageModelBase
    {
        public string key { get; set; }
    }
}