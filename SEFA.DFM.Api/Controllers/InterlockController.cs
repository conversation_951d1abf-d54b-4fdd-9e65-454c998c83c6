using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;

namespace SEFA.DFMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [Authorize(Permissions.Name)]
    public class InterlockController : BaseApiController
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly IInterlockServices _interlockServices;
    
        public InterlockController(IInterlockServices InterlockServices)
        {
            _interlockServices = InterlockServices;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<InterlockEntity>>> GetList(string key = "")
        {
            Expression<Func<InterlockEntity, bool>> whereExpression = a => true;
            var data = await _interlockServices.FindList(whereExpression);
            return Success(data, "获取成功");

        }

        [HttpPost]
        public async Task<MessageModel<PageModel<InterlockEntity>>> GetPageList([FromBody] InterlockRequestModel reqModel)
        {
             
            Expression<Func<InterlockEntity, bool>> whereExpression = a => true;
            var data = await _interlockServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return Success(data, "获取成功");

        }

        [HttpGet("{id}")]
        public async Task<MessageModel<InterlockEntity>> GetEntity(string id)
        {
            var data = await _interlockServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] InterlockEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _interlockServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                } 
                else
                {
                    return Failed( "添加失败");
                }
            }
            else
            {
                data.success = await _interlockServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                } 
                else
                {
                    return Failed( "更新失败");
                }
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _interlockServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed( "删除失败");
            }
        }
    }
    public class InterlockRequestModel : RequestPageModelBase
    {
        public string key { get; set; }
    }
}