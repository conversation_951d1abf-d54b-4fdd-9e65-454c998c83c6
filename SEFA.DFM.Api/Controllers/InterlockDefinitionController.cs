using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;
using SEFA.DFM.Services;

namespace SEFA.DFMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [Authorize(Permissions.Name)]
    public class InterlockDefinitionController : BaseApiController
    {
        /// <summary>
        /// InterlockDefinition
        /// </summary>
        private readonly IInterlockDefinitionServices _interlockDefinitionServices;
    
        public InterlockDefinitionController(IInterlockDefinitionServices InterlockDefinitionServices)
        {
            _interlockDefinitionServices = InterlockDefinitionServices;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<InterlockDefinitionEntity>>> GetList([FromBody] InterlockDefinitionRequestModel reqModel)
        {
            var data = await _interlockDefinitionServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<InterlockDefinitionEntity>>> GetPageList([FromBody] InterlockDefinitionRequestModel reqModel)
        {
            Expression<Func<InterlockDefinitionEntity, bool>> whereExpression = a => true;
            var data = await _interlockDefinitionServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<InterlockDefinitionEntity>> GetEntity(string id)
        {
            var data = await _interlockDefinitionServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] InterlockDefinitionEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _interlockDefinitionServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _interlockDefinitionServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _interlockDefinitionServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed( "删除失败");
            }
        }
    }
    //public class InterlockDefinitionRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}