using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.DFM.Controllers;
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SqlSugar;
using System.Linq.Expressions;

namespace SEFA.DFMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class KpiTargetController : BaseApiController
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly IKpiTargetServices _kpiTargetServices;

        public KpiTargetController(IKpiTargetServices KpiTargetServices)
        {
            _kpiTargetServices = KpiTargetServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<KpiTargetEntity>>> GetList(string key = "")
        {
            Expression<Func<KpiTargetEntity, bool>> whereExpression = Expressionable.Create<KpiTargetEntity>().And(a => true)
                    .AndIF(!string.IsNullOrEmpty(key), a => a.Productline.Contains(key))
                    .ToExpression();
            var data = await _kpiTargetServices.FindList(whereExpression);
            return Success(data, "获取成功");

        }

        [HttpPost]
        public async Task<MessageModel<PageModel<KpiTargetEntity>>> GetPageList([FromBody] KpiTargetRequestModel reqModel)
        {

            Expression<Func<KpiTargetEntity, bool>> whereExpression = Expressionable.Create<KpiTargetEntity>().And(a => true)
                    .AndIF(!string.IsNullOrEmpty(reqModel.key), a => a.Productline.Contains(reqModel.key))
                    .ToExpression();
            var data = await _kpiTargetServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return Success(data, "获取成功");

        }

        [HttpGet("{id}")]
        public async Task<MessageModel<KpiTargetEntity>> GetEntity(string id)
        {
            var data = await _kpiTargetServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] KpiTargetEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _kpiTargetServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _kpiTargetServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _kpiTargetServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    public class KpiTargetRequestModel : RequestPageModelBase
    {
        public string key { get; set; }
    }
}