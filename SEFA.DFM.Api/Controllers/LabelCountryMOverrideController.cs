using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;

namespace SEFA.DFMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [Authorize(Permissions.Name)]
    public class LabelCountryMOverrideController : BaseApiController
    {
        /// <summary>
        /// LabelCountryMOverride
        /// </summary>
        private readonly ILabelCountryMOverrideServices _labelCountryMOverrideServices;
    
        public LabelCountryMOverrideController(ILabelCountryMOverrideServices LabelCountryMOverrideServices)
        {
            _labelCountryMOverrideServices = LabelCountryMOverrideServices;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<LabelCountryMOverrideRequestModel>>> GetList([FromBody] LabelCountryMOverrideRequestModel reqModel)
        {
            var data = await _labelCountryMOverrideServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<LabelCountryMOverrideRequestModel>>> GetPageList([FromBody] LabelCountryMOverrideRequestModel reqModel)
        {
            var data = await _labelCountryMOverrideServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<LabelCountryMOverrideEntity>> GetEntity(string id)
        {
            var data = await _labelCountryMOverrideServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] LabelCountryMOverrideEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success =  await _labelCountryMOverrideServices.Add(request) > 0;
            }
            else
            {
                data.success = await _labelCountryMOverrideServices.Update(request);
            }
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _labelCountryMOverrideServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed( "删除失败");
            }
        }
    }
    //public class LabelCountryMOverrideRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}