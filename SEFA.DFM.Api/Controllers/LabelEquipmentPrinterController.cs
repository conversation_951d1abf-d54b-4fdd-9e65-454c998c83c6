using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;

namespace SEFA.DFMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [Authorize(Permissions.Name)]
    public class LabelEquipmentPrinterController : BaseApiController
    {
        /// <summary>
        /// LabelEquipmentPrinter
        /// </summary>
        private readonly ILabelEquipmentPrinterServices _labelEquipmentPrinterServices;
    
        public LabelEquipmentPrinterController(ILabelEquipmentPrinterServices LabelEquipmentPrinterServices)
        {
            _labelEquipmentPrinterServices = LabelEquipmentPrinterServices;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<LabelEquipmentPrinterRequestModel>>> GetList([FromBody] LabelEquipmentPrinterRequestModel reqModel)
        {
            var data = await _labelEquipmentPrinterServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<LabelEquipmentPrinterRequestModel>>> GetPageList([FromBody] LabelEquipmentPrinterRequestModel reqModel)
        {
            var data = await _labelEquipmentPrinterServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<LabelEquipmentPrinterEntity>> GetEntity(string id)
        {
            var data = await _labelEquipmentPrinterServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] LabelEquipmentPrinterEntity request)
        {
            var data = new MessageModel<string>();
            
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success =  await _labelEquipmentPrinterServices.Add(request) > 0;
            }
            else
            {
                data.success = await _labelEquipmentPrinterServices.Update(request);
            }
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _labelEquipmentPrinterServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed( "删除失败");
            }
        }
    }
    //public class LabelEquipmentPrinterRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}