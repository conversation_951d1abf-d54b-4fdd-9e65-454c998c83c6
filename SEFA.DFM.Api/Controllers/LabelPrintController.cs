using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.DFM.Controllers;
using SEFA.DFM.IServices;
using SEFA.DFM.Model.ViewModels;

namespace SEFA.DFM.Api.Controllers;

[Route("api/[controller]/[action]")]
[ApiController]
//[AllowAnonymous]
[Authorize(Permissions.Name)]
public class LabelPrintController : BaseApiController
{
    private readonly ILabelPrintServices _labelPrintServices;
    public LabelPrintController(ILabelPrintServices LabelPrintServices)
    {
        _labelPrintServices = LabelPrintServices;
    }

    [HttpPost]
    public async Task<MessageModel<string>> Print([FromBody] LabelPrinterParamModel labelPrinterParamModel)
    {
        var data = new MessageModel<string>();
        data.success = await _labelPrintServices.Print(labelPrinterParamModel);
        if (data.success)
        {
            return Success("", "打印成功");
        }
        else
        {
            return Failed("打印失败");
        }
    }
}