using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;
using Castle.MicroKernel.Registration;
using SqlSugar;

namespace SEFA.DFMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [Authorize(Permissions.Name)]
    public class LabelPrinterClassPropController : BaseApiController
    {
        /// <summary>
        /// LabelPrinterClassProp
        /// </summary>
        private readonly ILabelPrinterClassPropServices _labelPrinterClassPropServices;
        private readonly IBasePropertyServices _basePropertyServices;

        public LabelPrinterClassPropController(ILabelPrinterClassPropServices LabelPrinterClassPropServices, IBasePropertyServices basePropertyServices)
        {
            _labelPrinterClassPropServices = LabelPrinterClassPropServices;
            _basePropertyServices = basePropertyServices;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<LabelPrinterClassPropRequestModel>>> GetList([FromBody] LabelPrinterClassPropRequestModel reqModel)
        {
            var data = await _labelPrinterClassPropServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 根据PrinterClassId获取属性列表
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<BasePropertyEntity>>> GetListForProperty([FromBody] LabelPrinterClassPropRequestModel reqModel)
        {
            var classPropList = await _labelPrinterClassPropServices.GetList(reqModel);
            var definistionList = classPropList.Select(x => x.PropertyDefinitionId).ToList();
            if(definistionList.Count == 0)
                return Success(new List<BasePropertyEntity>(), "获取成功");

            var data = await _basePropertyServices.GetListByIdList(definistionList);

            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<LabelPrinterClassPropRequestModel>>> GetPageList([FromBody] LabelPrinterClassPropRequestModel reqModel)
        {
            var data = await _labelPrinterClassPropServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<LabelPrinterClassPropEntity>> GetEntity(string id)
        {
            var data = await _labelPrinterClassPropServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] LabelPrinterClassPropEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success =  await _labelPrinterClassPropServices.Add(request) > 0;
            }
            else
            {
                data.success = await _labelPrinterClassPropServices.Update(request);
            }
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _labelPrinterClassPropServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed( "删除失败");
            }
        }
    }
    //public class LabelPrinterClassPropRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}