using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;

namespace SEFA.DFMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [Authorize(Permissions.Name)]
    public class LabelPrinterSizeController : BaseApiController
    {
        /// <summary>
        /// LabelPrinterSize
        /// </summary>
        private readonly ILabelPrinterSizeServices _labelPrinterSizeServices;
    
        public LabelPrinterSizeController(ILabelPrinterSizeServices LabelPrinterSizeServices)
        {
            _labelPrinterSizeServices = LabelPrinterSizeServices;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<LabelPrinterSizeEntity>>> GetList([FromBody] LabelPrinterSizeRequestModel reqModel)
        {
            var data = await _labelPrinterSizeServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<LabelPrinterSizeEntity>>> GetPageList([FromBody] LabelPrinterSizeRequestModel reqModel)
        {
            var data = await _labelPrinterSizeServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<LabelPrinterSizeEntity>> GetEntity(string id)
        {
            var data = await _labelPrinterSizeServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] LabelPrinterSizeEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success =  await _labelPrinterSizeServices.Add(request) > 0;
            }
            else
            {
                data.success = await _labelPrinterSizeServices.Update(request);
            }
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _labelPrinterSizeServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed( "删除失败");
            }
        }
    }
    //public class LabelPrinterSizeRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}