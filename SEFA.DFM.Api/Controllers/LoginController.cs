using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.AuthHelper;
using SEFA.Base.Common;
using SEFA.Base.Common.HttpRestSharp;
using SEFA.Base.Common.LogHelper;
using SEFA.Base.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Common.AdCommon;
using SEFA.DFM.Controllers;
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.Models.Models.ViewModel;
using SEFA.DFM.Services;
using SqlSugar;
using System.Security.Cryptography;
using System.Text;

namespace SEFA.DFM.Api.Controllers
{
    [Produces("application/json")]
    [Route("api/Login")]
    //[Authorize(Permissions.Name)]
    public class LoginController : BaseApiController
    {
        readonly ISysUserInfoServices _sysUserInfoServices;
        readonly PermissionRequirement _requirement;
        private readonly IRoleModulePermissionServices _roleModulePermissionServices;
        private readonly IUserinfoServices _userInfoServices;
        readonly IBaseUserRoleServices _userRoleServices;
        private readonly IEquipmentViewServices _equipmentViewServices;
        private readonly IEquipmentServices _equipmentServices;
        private readonly IRoleServices _roleServices;

        private static byte[] aesKey = Encoding.UTF8.GetBytes("M7WNkwb8NRPytd13"); // 16字节的密钥
        private static byte[] aesIV = Encoding.UTF8.GetBytes("6KcAHww40ydj4jxX"); // 16字节的初始化向量

        /// <summary>
        /// 构造函数注入
        /// </summary>
        /// <param name="sysUserInfoServices"></param>
        /// <param name="userRoleServices"></param>
        /// <param name="roleServices"></param>
        /// <param name="requirement"></param>
        /// <param name="roleModulePermissionServices"></param>
        /// <param name="equipmentServices"></param>
        public LoginController(ISysUserInfoServices sysUserInfoServices, PermissionRequirement requirement,
            IRoleModulePermissionServices roleModulePermissionServices, IUserinfoServices userinfoServices,
            IBaseUserRoleServices userRoleServices, IEquipmentViewServices equipmentViewServices,
            IEquipmentServices equipmentServices, IRoleServices roleServices)
        {
            this._sysUserInfoServices = sysUserInfoServices;
            _requirement = requirement;
            _roleModulePermissionServices = roleModulePermissionServices;
            _userInfoServices = userinfoServices;
            _userRoleServices = userRoleServices;
            _equipmentViewServices = equipmentViewServices;
            _equipmentServices = equipmentServices;
            _roleServices = roleServices;
        }

        [HttpPost]
        [Route("/api/Login/swgLogin")]
        [AllowAnonymous]
        public dynamic SwgLogin([FromBody] SwaggerLoginRequest loginRequest)
        {
            byte[] decodedBytes = Convert.FromBase64String(loginRequest?.pwd);
            var pwd = Encoding.UTF8.GetString(decodedBytes);
            // 这里可以查询数据库等各种校验
            if (loginRequest?.name == "admin" && pwd == "lkkxh123B")
            {
                HttpContext.Session.SetString("swagger-code", "success");
                return new { result = true };
            }

            return new { result = false };
        }

        /// <summary>
        /// 系统登录
        /// </summary>
        /// <returns></returns>
        //[HttpPost]
        //[Route("Login")]
        //public async Task<MessageModel<TokenInfoViewModel>> Login([FromBody] LoginRequest request)
        //{
        //    if (request.isEncryp == 1)
        //    {
        //        request.pwd = request.pwd.ToUpper();
        //    }
        //    if (request.isDomainUser == 1)
        //    {
        //        SSOLogin.UsersServiceSoapClient sso = new SSOLogin.UsersServiceSoapClient(SSOLogin.UsersServiceSoapClient.EndpointConfiguration.UsersServiceSoap);
        //        sso.ClientCredentials.ServiceCertificate.SslCertificateAuthentication = new System.ServiceModel.Security.X509ServiceCertificateAuthentication()
        //        {
        //            CertificateValidationMode = X509CertificateValidationMode.None,
        //            RevocationMode = System.Security.Cryptography.X509Certificates.X509RevocationMode.NoCheck
        //        };
        //        var result = await sso.GetUserInfoAsync(request.SSOToken);
        //        SSOUserInfo userInfo = JsonConvert.DeserializeObject<SSOUserInfo>(result.Body.GetUserInfoResult);
        //        if (userInfo.Status == "0")
        //        {
        //            return Failed<TokenInfoViewModel>(userInfo.Messages);
        //        }
        //        else
        //        {
        //            request.name = userInfo.ADUser;
        //        }
        //    }
        //    var token = await _userRoleServices.GetTokenInfoByPolicy(request.name, request.pwd, _requirement, request.isEncryp,request.isDomainUser);
        //    if (token.success == true)
        //    {
        //        return Success(token, "获取成功");
        //    }
        //    else
        //    {
        //        return Failed<TokenInfoViewModel>(token.message);
        //    }

        //}
        [HttpPost]
        [Route("Login")]
        [AllowAnonymous]
        public async Task<MessageModel<UserEquipmentDto>> Login([FromBody] LoginRequest request)
        {
            bool externalSystemValidateSuccess = false;
            if (request.IsValidateSuccess == "1")
            {
                externalSystemValidateSuccess = true;
            }
            if (request.isEncryp == 1)
            {
                request.pwd = request.pwd.ToUpper();
            }
            #region  增加AD认证 Start

            if (!externalSystemValidateSuccess)
            {
                try
                {
                    //var pwd = Decrypt(request.txtpwd).response;
                    var str = string.Empty;
                    using (Aes aes = Aes.Create())
                    {
                        aes.Key = aesKey;
                        aes.IV = aesIV;

                        ICryptoTransform decryptor = aes.CreateDecryptor(aes.Key, aes.IV);

                        byte[] bytes = Convert.FromBase64String(request.txtpwd);

                        using (MemoryStream ms = new MemoryStream(bytes))
                        {
                            using (CryptoStream cs = new CryptoStream(ms, decryptor, CryptoStreamMode.Read))
                            {
                                using (StreamReader sr = new StreamReader(cs))
                                {
                                    //return sr.ReadToEnd();
                                    str = sr.ReadToEnd();
                                }
                            }
                        }
                    }

                    var result = ActiveDirectoryHelper.Validate(request.name, str);
                    if (result == 1)
                    {
                        return Failed<UserEquipmentDto>("用户名或密码错误！");
                    }
                    if (result == 100)
                    {
                        externalSystemValidateSuccess = true;
                    }
                }
                catch (Exception)
                {
                    //return Failed<UserEquipmentDto>(ex.Message);
                }
            }
            #endregion  增加AD认证 End

            string userName = "";
            if (request.isDomainUser == 1)
            {
                //SSOLogin.UsersServiceSoapClient sso = new SSOLogin.UsersServiceSoapClient(SSOLogin.UsersServiceSoapClient.EndpointConfiguration.UsersServiceSoap);
                //sso.ClientCredentials.ServiceCertificate.SslCertificateAuthentication = new System.ServiceModel.Security.X509ServiceCertificateAuthentication()
                //{
                //    CertificateValidationMode = X509CertificateValidationMode.None,
                //    RevocationMode = System.Security.Cryptography.X509Certificates.X509RevocationMode.NoCheck
                //};
                //var result = await sso.GetUserInfoAsync(request.SSOToken);
                //SSOUserInfo userInfo = JsonConvert.DeserializeObject<SSOUserInfo>(result.Body.GetUserInfoResult);

                //if (userInfo.Status == "0")
                //{
                //    return Failed<UserEquipmentDto>(userInfo.Messages);
                //}
                //else
                //{
                //    request.name = userInfo.ADUser;
                //}

                string sso_code = request.SSOToken;
                string getTokenUri = Appsettings.app(new string[] { "SSO", "GetTokenUri" }).ObjToString();
                string getUserInfoUri = Appsettings.app(new string[] { "SSO", "GetUserInfoUri" }).ObjToString();

                var ssoToken =
                    await HttpHelper.GetExternalApiAsync<SSOUserInfo>("SSO", getTokenUri + sso_code, "", null);
                if (!ssoToken.success)
                    return Failed<UserEquipmentDto>("单点登录异常");
                string access_token = ssoToken.response.access_token;
                var ssoUser =
                    await HttpHelper.GetExternalApiAsync<SSOUserInfo>("SSO", getUserInfoUri + access_token, "", null);
                if (!ssoUser.success)
                    return Failed<UserEquipmentDto>("单点登录异常");
                request.name = ssoUser.response.accountId;
                userName = ssoUser.response.UserName;
                SerilogServer.LogDebug("getTokenUri:" + getTokenUri + sso_code + "    getUserInfoUri:" +
                                       getUserInfoUri + access_token + "   ssoToken:" + ssoToken + "  access_token:" +
                                       access_token + "  name:" + request.name);
                SerilogServer.LogDebug("NAME:" + request.name + "    pwd:" + request.pwd + "   _requirement:" +
                                       _requirement.ToString() + "  isEncryp:" + request.isEncryp + "  name:" +
                                       request.isDomainUser);
            }

            var token = await _userRoleServices.GetTokenInfoByPolicy(request.name, request.pwd, _requirement,
                request.isEncryp, request.isDomainUser, userName, externalSystemValidateSuccess);

            #region 测试数据

            UserEquipmentDto dto = new UserEquipmentDto();
            dto.token = token.token;
            dto.token_type = token.token_type;
            dto.expires_in = token.expires_in;
            dto.success = token.success;
            dto.message = token.message;

            UserinfoEntity userinfoEntity = await _userInfoServices.FindEntity(a => a.LoginName == request.name);
            if (userinfoEntity != null)
            {
                var whereExpression = Expressionable.Create<EquipmentViewEntity>().AndIF(
                        !string.IsNullOrEmpty(userinfoEntity.Departmentid),
                        a => (a.AreaCode == userinfoEntity.Departmentid) ||
                             (a.ProductLineCode == userinfoEntity.Departmentid) ||
                             (a.SegmentCode == userinfoEntity.Departmentid) ||
                             (a.UnitId == userinfoEntity.Departmentid))
                    .ToExpression();
                var equipmentViewEntities = await _equipmentViewServices.FindList(whereExpression);
                EquipmentViewEntity equipmentViewEntity = equipmentViewEntities.FirstOrDefault();
                if (equipmentViewEntity != null)
                {
                    EquipmentModel areaEquipmentModel = new EquipmentModel
                    {
                        EquipmentId = equipmentViewEntity.AreaCode,
                        Level = "Area"
                    };
                    EquipmentModel productEquipmentModel = new EquipmentModel
                    {
                        EquipmentId = equipmentViewEntity.ProductLineCode,
                        Level = "ProductLine"
                    };
                    EquipmentModel segmentEquipmentModel = new EquipmentModel
                    {
                        EquipmentId = equipmentViewEntity.SegmentCode,
                        Level = "Segment"
                    };
                    EquipmentModel teamEquipmentModel = new EquipmentModel
                    {
                        EquipmentId = userinfoEntity.Teamid,
                        Level = "Team"
                    };
                    dto.UserEquipments.Add(areaEquipmentModel);
                    dto.UserEquipments.Add(productEquipmentModel);
                    dto.UserEquipments.Add(segmentEquipmentModel);
                    dto.UserEquipments.Add(teamEquipmentModel);
                }

                EquipmentEntity equipmentEntity =
                    await _equipmentServices.FindEntity(a => a.EquipmentCode == userinfoEntity.Departmentid);
                if (equipmentEntity != null)
                {
                    dto.DepartmentLeader = equipmentEntity.LineId;
                    // List<UserinfoEntity> userInfoList =
                    //     await _userInfoServices.FindList(a => a.Departmentid == equipmentEntity.EquipmentCode);
                    // if (userInfoList.Count > 0)
                    // {
                    //     dto.DepartmentUserList = userInfoList.Select(a => a.LoginName).ToList();
                    // }
                }
            }
            // EquipmentModel equipmentModel = new EquipmentModel();
            // equipmentModel.EquipmentId = "02308281-5132-0767-163e-0370f6000000";
            // equipmentModel.Level = "Area";
            // dto.UserEquipments.Add(equipmentModel);
            // equipmentModel = new EquipmentModel();
            // equipmentModel.EquipmentId = "02403131-3575-0422-163e-0370f6000000";
            // equipmentModel.Level = "ProductLine";
            // dto.UserEquipments.Add(equipmentModel);
            // equipmentModel = new EquipmentModel();
            // equipmentModel.EquipmentId = "02308281-5141-4964-163e-0370f6000000";
            // equipmentModel.Level = "Segment";
            // dto.UserEquipments.Add(equipmentModel);
            // equipmentModel = new EquipmentModel();
            // equipmentModel.EquipmentId = "02403201-5364-2616-163e-0370f6000000";
            // equipmentModel.Level = "Team";
            // dto.UserEquipments.Add(equipmentModel);

            #endregion

            if (token.success == true)
            {
                return Success(dto, "获取成功");
            }
            else
            {
                return Failed<UserEquipmentDto>(dto.message);
            }
        }

        [HttpPost]
        [Route("QAPermissionCheck")]
        [AllowAnonymous]
        public async Task<MessageModel<bool>> QAPermissionCheck ([FromBody] LoginRequest request) {
          
            try
            {
                var str = string.Empty;
                using (Aes aes = Aes.Create())
                {
                    aes.Key = aesKey;
                    aes.IV = aesIV;

                    ICryptoTransform decryptor = aes.CreateDecryptor(aes.Key, aes.IV);

                    byte[] bytes = Convert.FromBase64String(request.txtpwd);

                    using (MemoryStream ms = new MemoryStream(bytes))
                    {
                        using (CryptoStream cs = new CryptoStream(ms, decryptor, CryptoStreamMode.Read))
                        {
                            using (StreamReader sr = new StreamReader(cs))
                            {
                                //return sr.ReadToEnd();
                                str = sr.ReadToEnd();
                            }
                        }
                    }
                }

                var result = ActiveDirectoryHelper.Validate(request.name, str);
                if (result == 0)
                {
                    return Failed<bool>("AD账户不存在!");
                }
                if (result == 1)
                {
                    return Failed<bool>("AD用户名或密码错误！");
                }
                var user = (await _userInfoServices.FindList(a => a.LoginName == request.name)).FirstOrDefault();
                if (user == null)
                {
                    return Failed<bool>("用户未在系统注册！");
                }
                var role = (await _roleServices.FindList(a => a.Name == "QA审批权限")).FirstOrDefault();
                if(role == null)
                {
                    return Failed<bool>("【QA审批权限】角色不存在！");
                }
               
                var list = await _userRoleServices.FindList(a=>a.RoleId == role.ID && a.UserId == user.ID);
                if(list.Count == 0)
                {
                    return Failed<bool>("账号无QA权限！");
                }
                return Success<bool>(true);
            }
            catch (Exception ex)
            {
                return Failed<bool>(ex.ToString());
            }
          
        }


        //[HttpPost]
        //public MessageModel<string> Encrypt (string plainText) {

        //    using (Aes aes = Aes.Create())
        //    {
        //        aes.Key = aesKey;
        //        aes.IV = aesIV;

        //        ICryptoTransform encryptor = aes.CreateEncryptor(aes.Key, aes.IV);

        //        using (MemoryStream ms = new MemoryStream())
        //        {
        //            using (CryptoStream cs = new CryptoStream(ms, encryptor, CryptoStreamMode.Write))
        //            {
        //                using (StreamWriter sw = new StreamWriter(cs))
        //                {
        //                    sw.Write(plainText);
        //                }
        //            }

        //            //return  Convert.ToBase64String(ms.ToArray());
        //            return Success(Convert.ToBase64String(ms.ToArray()), "OK");
        //        }
        //    }
        //}

        //[HttpPost]
        //public MessageModel<string> Decrypt (string cipherText) {
        //    string str = "";
        //    using (Aes aes = Aes.Create())
        //    {
        //        aes.Key = aesKey;
        //        aes.IV = aesIV;

        //        ICryptoTransform decryptor = aes.CreateDecryptor(aes.Key, aes.IV);

        //        byte[] bytes = Convert.FromBase64String(cipherText);

        //        using (MemoryStream ms = new MemoryStream(bytes))
        //        {
        //            using (CryptoStream cs = new CryptoStream(ms, decryptor, CryptoStreamMode.Read))
        //            {
        //                using (StreamReader sr = new StreamReader(cs))
        //                {
        //                    //return sr.ReadToEnd();
        //                    str = sr.ReadToEnd();
        //                }
        //            }
        //        }
        //    }
        //    return Success(str, "OK");
        //}


        public class SwaggerLoginRequest
        {
            public string name { get; set; }
            public string pwd { get; set; }
        }

        public class LoginRequest
        {
            /// <summary>
            /// 用户名
            /// </summary>
            public string name { get; set; }

            /// <summary>
            /// 密码
            /// </summary>
            public string pwd { get; set; }
            /// <summary>
            /// txt密码
            /// </summary>
            public string txtpwd { get; set; }

            /// <summary>
            /// 是否加密码传输，1是，0否
            /// </summary>
            public int isEncryp { get; set; } = 0;

            /// <summary>
            /// 是否域账号,1是，0否
            /// </summary>
            public int isDomainUser { get; set; } = 0;

            /// <summary>
            /// 域账号登录的token
            /// </summary>
            public string SSOToken { get; set; }
            /// <summary>
            /// 外部系统认证成功标识
            /// </summary>
            public string IsValidateSuccess { get; set; } = "0";
        }

        public class SSOUserInfo
        {
            public string Status { get; set; }
            public string ADUser { get; set; }
            public string UserName { get; set; }
            public string Messages { get; set; }
            public string access_token { get; set; }
            public string expires { get; set; }
            public string accountId { get; set; }
        }

        public class SSOUserInfo_ZT
        {
        }
    }
}