using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Linq.Expressions;
using SEFA.Base;
using SqlSugar;
using SEFA.DFM.Model.ViewModels;
namespace SEFA.DFM.Api.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class MRackingController : ControllerBase
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly IMRackingServices _mrackingServices;
        private readonly ISeriesCategoryServices _SeriesCategoryServices;
        private readonly ISeriesServices _SeriesServices;
        public MRackingController(IMRackingServices MRackingServices, ISeriesCategoryServices SeriesCategoryServices, ISeriesServices SeriesServices)
        {
            _mrackingServices = MRackingServices;
            _SeriesCategoryServices = SeriesCategoryServices;
            _SeriesServices = SeriesServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<MRackingEntity>>> GetList(string key = "")
        {

            var whereExpression = Expressionable.Create<MRackingEntity>().ToExpression();

            return new MessageModel<List<MRackingEntity>>()
            {
                msg = "获取成功",
                success = true,
                response = await _mrackingServices.FindList(whereExpression)
            };
        }
        [HttpPost]
        public async Task<MessageModel<PageModel<MRackingEntity>>> GetPageList(int page = 1, string key = "", int intPageSize = 50)
        {

            var whereExpression = Expressionable.Create<MRackingEntity>().And(a => a.STATUS != null )
                      .AndIF(!string.IsNullOrEmpty(key), a => a.REMARK.Contains(key) || a.REMARK.Contains(key)).ToExpression();

            return new MessageModel<PageModel<MRackingEntity>>()
            {
                msg = "获取成功",
                success = true,
                response = await _mrackingServices.QueryPage(whereExpression, page, intPageSize)
            };

        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] MRackingEntity request)
        {
            var data = new MessageModel<string>();
            request.CreateCustomGuid(request.CreateUserId);
            //request.CreateCustomGuid(request.CreateDate.ToString());
            var id = await _mrackingServices.Add(request);
            if (data.success)
            {
                data.response = id.ObjToString();
                data.msg = "添加成功";
            }
            return data;
        }
        [HttpPost]
        public async Task<MessageModel<List<TreeModel>>> GetTree()
        {
            var whereExpression = Expressionable.Create<MRackingEntity>().ToExpression();
            var list = await _mrackingServices.FindList(whereExpression);

            //获取树形结构
            List<TreeModel> treeList = new List<TreeModel>();
            foreach (var item in list)
            {

                TreeModel node = new TreeModel
                {
                    id = item.ID,

                    value = item.ID,
                    showcheck = false,
                    checkstate = 0,
                    isexpand = false

                };
                treeList.Add(node);
            }
            return new MessageModel<List<TreeModel>>()
            {
                msg = "获取成功",
                success = true,
                response = treeList
            };
        }

        [HttpDelete("{id}")]
        public async Task<MessageModel<string>> DeleteForm(string id)
        {
            var data = new MessageModel<string>();
            data.success = await _mrackingServices.DeleteById(id);
            if (data.success)
            {
                data.msg = "删除成功";
                data.response = id;
            }
            else
            {
                data.msg = "删除失败";
                data.response = "";
            }
            return data;
        }

    }
}
