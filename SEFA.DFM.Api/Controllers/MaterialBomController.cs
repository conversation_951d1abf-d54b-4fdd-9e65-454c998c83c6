using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SapNwRfc;
using SEFA.Base;
using SEFA.Base.Common;
using SEFA.Base.Common.LogHelper;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.Base.Model;
using SEFA.DFM.Controllers;
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using SqlSugar;
using System.Globalization;
using System.Linq.Expressions;

namespace SEFA.DFMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class MaterialBomController : BaseApiController
    {
        /// <summary>
        /// 主BOM
        /// </summary>
        private readonly IMaterialBomServices _materialBomServices;
        private readonly IMapper _mapper;
        private readonly IMaterialBomDetailServices _materialBomDetailServices;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMaterialServices _materialServices;
        private readonly IMaterialTypeRelServices _materialTypeRelServices;
        public MaterialBomController(IMaterialBomServices MaterialBomServices, IMapper mapper, IMaterialBomDetailServices materialBomDetailServices,
            IUnitOfWork unitOfWork, IMaterialServices materialServices, IMaterialTypeRelServices materialTypeRelServices)
        {
            _materialBomServices = MaterialBomServices;
            _mapper = mapper;
            _materialBomDetailServices = materialBomDetailServices;
            _unitOfWork = unitOfWork;
            _materialServices = materialServices;
            _materialTypeRelServices = materialTypeRelServices;
        }

        /// <summary>
        /// 获取全部
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<MaterialBomEntity>>> GetList(string key = "")
        {
            Expression<Func<MaterialBomEntity, bool>> whereExpression = a => true;
            var data = await _materialBomServices.FindList(whereExpression);
            return Success(data, "获取成功");

        }
        /// <summary>
        /// 分页
        /// </summary>
        /// <param name="model">物料号</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<PageModel<MaterialBomEntity>>> GetPageList([FromBody] MaterialBomModel model)
        {

            var whereExpression = Expressionable.Create<MaterialBomEntity>().And(a => a.Deleted == 0)
                  .AndIF(!string.IsNullOrEmpty(model.materialCode), a => (a.MaterialCode.Equals(model.materialCode) || a.MaterialName.Contains(model.materialCode)))
                  .ToExpression();
            var data = await _materialBomServices.QueryPage(whereExpression, model.pageIndex, model.pageSize);
            return Success(data, "获取成功");

        }

        [HttpGet("{id}")]
        public async Task<MessageModel<MaterialBomEntity>> GetEntity(string id)
        {
            var data = await _materialBomServices.QueryById(id);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 修改状态
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> UpdateStuts([FromBody] MaterialBomEntity request)
        {
            var data = new MessageModel<string>();

            data.success = (await _materialBomServices.UpdateStuts(request)) > 0;

            if (data.success)
            {
                return Success("", "更新成功");
            }
            else
            {
                return Failed("更新失败");
            }

        }

        /// <summary>
        /// 保存或修改
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] MaterialBomEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                if (await _materialBomServices.isExist(request, false))
                {
                    return Failed("当前的BOM信息已经存在。");
                }
                data.success = await _materialBomServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                if (await _materialBomServices.isExist(request, true))
                {
                    return Failed("当前的BOM信息已经存在。");
                }
                data.success = await _materialBomServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }
        /// <summary>
        /// 提交 审核 作废
        /// </summary>
        /// <param name="id"></param>
        /// <param name="status">PendingSubmission待提交  PendingApproval待审核  Approved已通过   Unapproved未通过  Void作废</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> ChangeStatus(string id, string status)
        {
            var msg = new MessageModel<string>();
            var data = await _materialBomServices.QueryById(id);

            if (AuditStatus.PendingSubmission.ToString().Equals(data.Status))
            {
                if (!AuditStatus.PendingApproval.ToString().Equals(status))
                {
                    return Failed("未提交");
                }
            }
            else if (AuditStatus.PendingApproval.ToString().Equals(data.Status))
            {
                if ((!AuditStatus.Approved.ToString().Equals(status)) || !AuditStatus.Unapproved.ToString().Equals(status))
                {
                    return Failed("待审核");
                }
            }
            else if (AuditStatus.Approved.ToString().Equals(data.Status))
            {
                return Failed("已核准");
            }
            data.Status = status;
            msg.success = await _materialBomServices.Update(data);
            if (msg.success)
            {
                return Success("", "成功");
            }
            else
            {
                return Failed("失败");
            }
        }
        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _materialBomServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
        /// <summary>
        /// 同步bom数据
        /// </summary>
        /// <param name="materialCode">需要同步的物料号</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> SyncBom(string materialCode, string bVersion, string sVersion)
        {
            string errorMaterial = "";
            List<string> materialList = new List<string>();//所有成品物料,自动同步时同步所有成品物料
            List<MaterialEntity> cpList = new List<MaterialEntity>();
            List<MaterialEntity> newMaterials = new List<MaterialEntity>();//BOM中包含的所有物料
            List<RelMaterialTypeCalssModel> relTypeCalsses = await _materialTypeRelServices.GetMaterialTypeRels();
            cpList = await _materialServices.FindList(c => c.Type == "CPWL" && (c.Code == materialCode || string.IsNullOrEmpty(materialCode)) && c.Deleted == 0);
            if (cpList.Count() > 0)
            {
                materialList = cpList.Select(t => t.Code).ToList();
                bVersion = "1";
            }
            else
            {
                materialList.Add(materialCode);
                bVersion = "1";
            }
            string connectionString = Appsettings.app(new string[] { "SapConnection", "ConnectionString" }).ObjToString();
            using var connection = new SapConnection(connectionString);
            connection.Connect();
            string factoryCode = Appsettings.app(new string[] { "SapConnection", "FactoryCode" }).ObjToString();
            foreach (string material in materialList)
            {
                materialCode = material;
                using var someFunction = connection.CreateFunction("Z_MES_GET_BOM");

                var result = someFunction.Invoke<OutResult>(new InputParameters
                {
                    FactoryCode = factoryCode,
                    IsFind = "X",
                    UnitQty = "1000",
                    MaterialCode = materialCode
                });
                List<SomeFunctionResultItem> list = result.Items.ToList();
                List<BomHeadInfo> bomHeadInfos = result.BomHeads.Where(c => (c.BVersion == bVersion || string.IsNullOrEmpty(bVersion)) && (c.SVerson == sVersion || string.IsNullOrEmpty(sVersion))).OrderBy(c => c.UpdateDate).OrderBy(c => c.SVerson).ToList();
                List<MaterialInfo> materials = list.GroupBy(c => new { c.CompoentCode, c.CompoentName }).Distinct().Select(c => new MaterialInfo { MaterialCode = c.Key.CompoentCode, MaterialName = c.Key.CompoentName }).ToList();
                List<BomVersion> bomVersions = list.Where(c => (c.Version1 == bVersion || string.IsNullOrEmpty(bVersion)) && (c.Version2 == sVersion || string.IsNullOrEmpty(sVersion))).GroupBy(c => new { c.Version1, c.Version2 }).Distinct().Select(c => new BomVersion { Version1 = c.Key.Version1, Version2 = c.Key.Version2 }).ToList();
                List<MaterialBomEntity> oldBoms = new List<MaterialBomEntity>();
                List<MaterialBomEntity> materialBoms = new List<MaterialBomEntity>();
                List<MaterialBomDetailEntity> bomDetails = new List<MaterialBomDetailEntity>();
                #region
                //foreach (SomeFunctionResultItem level in list)
                //{
                //    MaterialBomDetailEntity bomDetailEntity = new MaterialBomDetailEntity();
                //    bomDetailEntity.BomLevel = int.Parse(level.BomLevel);
                //    bomDetailEntity.CreateCustomGuid("SAP");
                //    bomDetailEntity.MaterialBomId = level.Version1+level.Version2;
                //    if (bomDetailEntity.BomLevel == 1)
                //    {
                //        bomDetailEntity.ParentCode = materialCode;
                //        bomDetailEntity.ParentId = "";
                //    }
                //    else
                //    {

                //        bomDetailEntity.ParentCode = materials.Where(c => c.MaterialName == level.ParentName).FirstOrDefault().MaterialCode;
                //        bomDetailEntity.ParentId = "";
                //    }

                //    bomDetailEntity.ParentName = level.ParentName;
                //    bomDetailEntity.CompoentCode = level.CompoentCode;
                //    bomDetailEntity.CompoentName = level.CompoentName;
                //    bomDetailEntity.ParentUom = "PCS";
                //    bomDetailEntity.CompoentUom = level.CompoentUom;
                //    bomDetailEntity.Deleted = 0;
                //    bomDetailEntity.ParentQuantity = 1000;
                //    decimal qty;
                //    decimal.TryParse(level.CompoentQuantity, out qty);
                //    bomDetailEntity.CompoentQuantity = qty;
                //    bomDetailEntity.Conversionrate = level.ConvertionRate;
                //    bomDetails.Add(bomDetailEntity);
                //}
                #endregion
                if (bomHeadInfos.Count > 0)
                {

                    BomHeadInfo headInfo = bomHeadInfos[bomHeadInfos.Count - 1];
                    oldBoms = await _materialBomServices.FindList(c => c.MaterialCode == materialCode && c.Deleted == 0);
                    int ii = 0;
                    if (oldBoms.Count > 0)
                    {
                        foreach (MaterialBomEntity entity in oldBoms)
                        {
                            if (entity.ModifyDate <= DateTime.ParseExact(headInfo.UpdateDate, "yyyyMMdd", CultureInfo.InvariantCulture))
                            {
                                entity.Deleted = 1;
                                entity.Status = "0";
                                entity.Enable = 0;
                                ii++;
                            }
                        }
                    }
                    else
                    {
                        ii++;
                    }
                    if (ii == 0)
                    {
                        continue;
                        //return Success("", "BOM已是最新，无需更新");
                    }
                    MaterialBomEntity bomEntity = new MaterialBomEntity();
                    bomEntity.CreateCustomGuid("SAP");
                    bomEntity.Version = "V0" + headInfo.SVerson;
                    bomEntity.MaterialCode = materialCode;
                    bomEntity.MaterialName = headInfo.MaterialName;
                    bomEntity.Enable = 1;
                    bomEntity.Status = "1";
                    bomEntity.Uom = "PCS";
                    bomEntity.Quantity = (int)decimal.Parse(headInfo.Qty);
                    bomEntity.BomType = "PBOM";
                    bomEntity.Factory = factoryCode;
                    if (!headInfo.EffecStart.Equals("0000-00-00"))
                    {
                        // DateTime.TryParse(headInfo.EffecStart, out effectStart);
                        bomEntity.EffectStart = DateTime.ParseExact(headInfo.EffecStart, "yyyyMMdd", CultureInfo.InvariantCulture);
                        // bomEntity.EffectStart = effectStart;
                    }
                    if (!headInfo.EffectEnd.Equals("0000-00-00"))
                    {
                        // DateTime.TryParse(headInfo.EffectEnd, out effectEnd);
                        // bomEntity.EffectEnd = effectEnd;
                        bomEntity.EffectEnd = DateTime.ParseExact(headInfo.EffectEnd, "yyyyMMdd", CultureInfo.InvariantCulture);
                    }
                    materialBoms.Add(bomEntity);
                    MaterialEntity materialEntity = materialEntity = cpList.Where(c => c.Code == materialCode).FirstOrDefault(); //成品物料

                    if (materialEntity == null)
                    {
                        materialEntity = new MaterialEntity();
                    }
                    materialEntity.CreateCustomGuid("SAP");
                    materialEntity.Code = materialCode;
                    materialEntity.NAME = headInfo.MaterialName;
                    materialEntity.Type = "CPWL";
                    materialEntity.Plant = "2050";
                    materialEntity.Deleted = 0;
                    newMaterials.Add(materialEntity);
                    for (int i = 1; i <= 10; i++)
                    {
                        List<SomeFunctionResultItem> levelInfo = list.Where(c => c.Version1 == bVersion && c.BomLevel == i.ToString()).OrderByDescending(c => c.Version2).ToList();
                        foreach (SomeFunctionResultItem level in levelInfo)
                        {
                            MaterialBomDetailEntity bomDetailEntity = new MaterialBomDetailEntity();
                            bomDetailEntity.BomLevel = i;
                            bomDetailEntity.CreateCustomGuid("SAP");
                            bomDetailEntity.MaterialBomId = bomEntity.ID;
                            if (i == 1)
                            {
                                bomDetailEntity.ParentCode = materialCode;
                                bomDetailEntity.ParentId = "";
                            }
                            else
                            {

                                bomDetailEntity.ParentCode = materials.Where(c => c.MaterialName == level.ParentName).FirstOrDefault().MaterialCode;
                                if (bomDetails.Where(c => c.BomLevel + 1 == i && c.CompoentCode == bomDetailEntity.ParentCode && c.MaterialBomId == bomEntity.ID).Count() > 0)
                                    bomDetailEntity.ParentId = bomDetails.Where(c => c.CompoentCode == bomDetailEntity.ParentCode && c.MaterialBomId == bomEntity.ID).FirstOrDefault().ID;
                                else
                                    bomDetailEntity.ParentId = "";
                            }
                            if (bomDetails.Where(c => c.CompoentCode == level.CompoentCode && c.ParentName == level.ParentName && c.MaterialBomId == bomDetailEntity.MaterialBomId).Count() > 0)
                            {
                                continue;
                            }
                            bomDetailEntity.ParentName = level.ParentName;
                            bomDetailEntity.CompoentCode = level.CompoentCode;
                            bomDetailEntity.CompoentName = level.CompoentName;
                            bomDetailEntity.ParentUom = "PCS";
                            bomDetailEntity.CompoentUom = level.CompoentUom;
                            bomDetailEntity.Deleted = 0;
                            bomDetailEntity.ParentQuantity = 1000;
                            decimal qty;
                            decimal.TryParse(level.CompoentQuantity, out qty);
                            bomDetailEntity.CompoentQuantity = qty;
                            bomDetailEntity.Conversionrate = ((1 - decimal.Parse(level.ConvertionRate) / 100) * 100).ToString();
                            bomDetailEntity.MaterialGroup = level.MaterialGroup;
                            bomDetails.Add(bomDetailEntity);
                            MaterialEntity compoentEntity = new MaterialEntity();
                            if (newMaterials.Where(c => c.Code == level.CompoentCode).Count() <= 0)
                            {
                                compoentEntity.CreateCustomGuid("SAP");
                                compoentEntity.Plant = "2050";
                                compoentEntity.Code = level.CompoentCode;
                                compoentEntity.NAME = level.CompoentName;
                                compoentEntity.Deleted = 0;
                                compoentEntity.Categorycode = level.MaterialGroup;
                                RelMaterialTypeCalssModel relMaterialType = relTypeCalsses.Where(c => c.Categorycode == level.CompoentCode).FirstOrDefault();
                                if (relMaterialType != null)
                                {
                                    compoentEntity.CategoryName = relMaterialType.CategoryName;
                                    compoentEntity.Type = relMaterialType.Type;

                                }
                            }
                        }
                    }
                }
                #region
                //foreach (BomVersion version in bomVersions)
                //{
                //    MaterialBomEntity bomEntity = new MaterialBomEntity();
                //    SomeFunctionResultItem resultItem = list.Where(c => c.Version1 == version.Version1 && c.Version2 == version.Version2 && c.BomLevel == "1").FirstOrDefault();
                //    bomEntity.CreateCustomGuid("SAP");
                //    bomEntity.Version = version.Version1 + "." + version.Version2;
                //    bomEntity.MaterialCode = materialCode;
                //    bomEntity.MaterialName = resultItem.ParentName;
                //    bomEntity.Enable = 1;
                //    bomEntity.Status = "1";
                //    bomEntity.Uom = "PCS";
                //    bomEntity.Quantity = 1000;
                //    bomEntity.BomType = "PBOM";
                //    bomEntity.Factory = factoryCode;
                //    DateTime effectStart;
                //    DateTime effectEnd;
                //    if (!resultItem.EffecStart.Equals("0000-00-00"))
                //    {
                //        DateTime.TryParse(resultItem.EffecStart, out effectStart);
                //        bomEntity.EffectStart = effectStart;
                //    }
                //    if (!resultItem.EffectEnd.Equals("0000-00-00"))
                //    {
                //        DateTime.TryParse(resultItem.EffectEnd, out effectEnd);
                //        bomEntity.EffectEnd = effectEnd;
                //    }
                //    materialBoms.Add(bomEntity);
                //    for (int i = 1; i <= 10; i++)
                //    {
                //        List<SomeFunctionResultItem> levelInfo = list.Where(c => c.Version1 == version.Version1 && c.Version2 == version.Version2 && c.BomLevel == i.ToString()).ToList();
                //        foreach (SomeFunctionResultItem level in levelInfo)
                //        {
                //            MaterialBomDetailEntity bomDetailEntity = new MaterialBomDetailEntity();
                //            bomDetailEntity.BomLevel = i;
                //            bomDetailEntity.CreateCustomGuid("SAP");
                //            bomDetailEntity.MaterialBomId = bomEntity.ID;
                //            if (i == 1)
                //            {
                //                bomDetailEntity.ParentCode = materialCode;
                //                bomDetailEntity.ParentId = "";
                //            }
                //            else
                //            {

                //                bomDetailEntity.ParentCode = materials.Where(c => c.MaterialName == level.ParentName).FirstOrDefault().MaterialCode;
                //                if (bomDetails.Where(c => c.BomLevel + 1 == i && c.CompoentCode == bomDetailEntity.ParentCode && c.MaterialBomId == bomEntity.ID).Count() > 0)
                //                    bomDetailEntity.ParentId = bomDetails.Where(c => c.CompoentCode == bomDetailEntity.ParentCode && c.MaterialBomId == bomEntity.ID).FirstOrDefault().ID;
                //                else
                //                    bomDetailEntity.ParentId = "";
                //            }
                //            if (bomDetails.Where(c => c.CompoentCode == level.CompoentCode && c.ParentName == level.ParentName && c.MaterialBomId == bomDetailEntity.MaterialBomId).Count() > 0)
                //            {
                //                continue;
                //            }
                //            bomDetailEntity.ParentName = level.ParentName;
                //            bomDetailEntity.CompoentCode = level.CompoentCode;
                //            bomDetailEntity.CompoentName = level.CompoentName;
                //            bomDetailEntity.ParentUom = "PCS";
                //            bomDetailEntity.CompoentUom = level.CompoentUom;
                //            bomDetailEntity.Deleted = 0;
                //            bomDetailEntity.ParentQuantity = 1000;
                //            decimal qty;
                //            decimal.TryParse(level.CompoentQuantity, out qty);
                //            bomDetailEntity.CompoentQuantity = qty;
                //            bomDetailEntity.Conversionrate = level.ConvertionRate;
                //            bomDetails.Add(bomDetailEntity);
                //        }
                //    }
                //}
                #endregion
                if (materialBoms.Count > 0)
                {
                    try
                    {
                        _unitOfWork.BeginTran();
                        int x = await _materialBomServices.Add(materialBoms);
                        int y = await _materialBomDetailServices.Add(bomDetails);
                        bool s = await _materialServices.StorageList(newMaterials, new string[] { "Code" });
                        foreach (MaterialBomEntity oldEntity in oldBoms)
                        {
                            var whereExpression = Expressionable.Create<MaterialBomDetailEntity>().And(c => c.MaterialBomId == oldEntity.ID).ToExpression();
                            bool z = await _materialBomServices.Update(oldEntity);
                            await _materialBomDetailServices.Update(new MaterialBomDetailEntity() { Deleted = 0 }, whereExpression);
                        }
                        _unitOfWork.CommitTran();
                    }
                    catch (Exception ex)
                    {
                        _unitOfWork.RollbackTran();
                        SerilogServer.LogError(ex, "同步物料【" + materialCode + "】BOM失败!");
                        errorMaterial += materialCode + ",";
                    }
                }
                else
                    continue;
            }
            if (!string.IsNullOrEmpty(errorMaterial))
            {
                return Failed("", "同步物料【" + errorMaterial + "】BOM失败!");
            }
            else
                return Success("", "同步成功!");

        }
    }
    /// <summary>
    /// 查询条件
    /// </summary>
    public class MaterialBomModel : RequestPageModelBase
    {

        public string materialCode { get; set; }

    }

    internal class OutResult
    {
        [SapName("ET_TOPMAT_CHGINFO")]

        public BomHeadInfo[] BomHeads { get; set; }
        [SapName("ET_STB")]

        public SomeFunctionResultItem[] Items { get; set; }
    }
    internal class MaterialInfo
    {
        public string MaterialCode { get; set; }
        public string MaterialName { get; set; }

    }
    internal class BomVersion
    {
        public string Version1 { get; set; }
        public string Version2 { get; set; }

    }
    public class BomHeadInfo
    {
        [SapName("MATNR")]
        public string MaterialCode { get; set; }
        [SapName("MAKTX")]
        public string MaterialName { get; set; }
        [SapName("WERKS")]
        public string FactoryCode { get; set; }
        [SapName("EMMBM")]
        public string Qty { get; set; }
        [SapName("BMEIN")]
        public string Uom { get; set; }
        [SapName("STLAN")]
        public string BVersion { get; set; }
        [SapName("STLST")]
        public string SVerson { get; set; }
        [SapName("DATUV")]
        public string EffecStart { get; set; }
        [SapName("DATUB")]
        public string EffectEnd { get; set; }
        [SapName("UDATE")]
        public string UpdateDate { get; set; }
        [SapName("UTIME")]
        public string UpdateTime { get; set; }

    }
    public class SomeFunctionResultItem
    {
        //public string MaterialBomId { get; set; }
        //public string ParentId { get; set; }
        //public string ParentCode { get; set; }

        [SapName("STUFE")]
        public string BomLevel { get; set; }
        [SapName("STLAN")]
        public string Version1 { get; set; }

        [SapName("STLAL")]
        public string Version2 { get; set; }

        [SapName("OJTXB")]
        public string ParentName { get; set; }
        [SapName("IDNRK")]
        public string CompoentCode { get; set; }

        [SapName("OJTXP")]
        public string CompoentName { get; set; }
        //public string ParentUom { get; set; }

        [SapName("MEINS")]
        public string CompoentUom { get; set; }
        // public decimal? ParentQuantity { get; set; }

        [SapName("MENGE")]
        public string CompoentQuantity { get; set; }
        [SapName("ANDAT")]
        public string EffecStart { get; set; }
        [SapName("AEDAT")]
        public string EffectEnd { get; set; }
        [SapName("AUSCH")]
        public string ConvertionRate { get; set; }
        [SapName("MATMK")]
        public string MaterialGroup { get; set; }
    }

    internal class InputParameters
    {
        [SapName("IM_MATNR")]
        public string MaterialCode { get; set; }
        [SapName("IM_WERKS")]
        public string FactoryCode { get; set; }
        [SapName("IM_MEHRS")]
        public string IsFind { get; set; }
        [SapName("IM_EMENG")]
        public string UnitQty { get; set; }
    }
}