using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.Base.IServices;
using SEFA.DFM.Model.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Linq.Expressions;
using SqlSugar;
using SEFA.DFM.Model.ViewModels;
using SEFA.DFM.Controllers;
using Abp.Application.Services.Dto;
using Magicodes.ExporterAndImporter.Excel;
using Magicodes.ExporterAndImporter.Excel.AspNetCore;
using Newtonsoft.Json;
using AutoMapper;
using SEFA.Base.Model.Models;
using Elasticsearch.Net;
using SEFA.Base;

namespace SEFA.DFMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class MaterialController : BaseApiController
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly IMaterialServices _materialServices;
        private readonly ISeriesCategoryServices _SeriesCategoryServices;
        private readonly ISeriesServices _SeriesServices;
        //private readonly IExcelExporter _excelExporter;
        private readonly IMapper _mapper;
        private readonly IClassMappingServices _classMappingServices;
        private readonly IMaterialPropertyValueServices _propertyValueServices;
        private readonly IPropertyServices _propertyServices;
        private readonly IClassServices _classServices;
        private readonly ILogServices _logServices;

        public MaterialController(IMaterialServices MaterialServices, ISeriesCategoryServices SeriesCategoryServices, ISeriesServices SeriesServices, IMapper Mapper, IClassMappingServices classMapping, IMaterialPropertyValueServices propertyValueServices, IPropertyServices PropertyServices, IClassServices classServices, ILogServices logServices)
        {
            _materialServices = MaterialServices;
            _SeriesCategoryServices = SeriesCategoryServices;
            _SeriesServices = SeriesServices;
            //_excelExporter = excelExporter;
            _mapper = Mapper;
            _classMappingServices = classMapping;
            _propertyValueServices = propertyValueServices;
            _propertyServices = PropertyServices;
            _classServices = classServices;
            _logServices = logServices;
        }
        /// <summary>
        /// 获取top n 数据
        /// </summary>
        /// <param name="materialRequest"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<MaterialEntity>>> GetTopNList([FromBody] MaterialRequestModel materialRequest)
        {

            var whereExpression = Expressionable.Create<MaterialEntity>().And(a => a.Deleted == 0)
              .AndIF(!string.IsNullOrEmpty(materialRequest.type), a => a.Type == materialRequest.type)
                .AndIF(!string.IsNullOrEmpty(materialRequest.key), a => a.NAME.Contains(materialRequest.key) || a.Code.Contains(materialRequest.key)).ToExpression();
            var data = await _materialServices.FindList(whereExpression, materialRequest.Count, "");
            return Success(data, "获取成功");
        }
        [HttpPost]
        public async Task<bool> OperationLogTest(LogEntity logEntity)
        {
            return await _logServices.AddOperationLog(logEntity);
        }
        /// <summary>
        /// 获取List
        /// </summary>
        /// <param name="materialRequest"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<MaterialEntity>>> GetList([FromBody] MaterialUnitRequestModel materialRequest)
        {
            var whereExpression = Expressionable.Create<MaterialEntity>().And(a => a.Deleted == 0)
              .AndIF(!string.IsNullOrEmpty(materialRequest.type), a => a.Type == materialRequest.type)
              .AndIF(!string.IsNullOrEmpty(materialRequest.key), 
              a =>(a.Code != null && a.Code.Contains(materialRequest.key))
               || (a.NAME != null && a.NAME.Contains(materialRequest.key))
               || (a.CustomerMaterialcode != null && a.CustomerMaterialcode.Contains(materialRequest.key))
               || (a.Description != null && a.Description.Contains(materialRequest.key)))
              .AndIF(!string.IsNullOrEmpty(materialRequest.CategoryCode), a => a.Categorycode.Equals(materialRequest.CategoryCode))
             .ToExpression();
            if(materialRequest.Count > 0)
            {
                var data = await _materialServices.FindList(whereExpression, materialRequest.Count, " CODE asc");
                return Success(data, "获取成功");
            }
            else
            {
                var data = await _materialServices.FindList(whereExpression);
                return Success(data, "获取成功");
            }
           
        }
        /// <summary>
        /// 获取List 根据 MaterialCode List
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<MaterialViewModels>>> GetListByMaterialCodeList([FromBody] List<MaterialRequestModel> materialRequest)
        {
            var clist = materialRequest.Select(it => it.Code).ToArray();
            var whereExpression = Expressionable.Create<MaterialEntity>().And(a => a.Deleted == 0 && clist.Contains(a.Code))
                .ToExpression();
            var data = await _materialServices.FindList(whereExpression);

            List<MaterialViewModels> materials = new List<MaterialViewModels>();

            foreach (var item in data)
            {
                MaterialViewModels materialViewModels = new MaterialViewModels();
                materialViewModels.Code = item.Code;
                materials.Add(materialViewModels);
            }

            return Success(materials, "获取成功");
        }
        [HttpPost]
        public async Task<MessageModel<PageModel<MaterialUnitModel>>> GetPageList([FromBody] MaterialUnitRequestModel materialRequest)
        {
            var data = await _materialServices.GetMaterialPageList(materialRequest);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<MaterialEntity>> GetEntity(string id)
        {
            var data = await _materialServices.QueryById(id);
            return Success(data, "获取成功");
        }


        [HttpPost]
        public async Task<MessageModel<List<MaterialEntity>>> GetListByType(string code, string type)
        {
            var whereExpression = Expressionable.Create<MaterialEntity>().And(t => t.Code == code && t.Type == type).ToExpression();
            var data = await _materialServices.FindList(whereExpression);
            return Success(data, "获取成功");
        }
        /// <summary>
        /// 获取物料树形结构
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<TreeModel>>> GetTree()
        {
            var whereExpression = Expressionable.Create<MaterialEntity>().And(a => a.ParentId != null && a.Type != "M").ToExpression();
            var list = await _materialServices.FindList(whereExpression);

            //获取树形结构
            List<TreeModel> treeList = new List<TreeModel>();
            foreach (var item in list)
            {
                // var Code = item.Code + (item.Type == "S" ? "(系列)" : "(机种)");
                var Code = item.Code;// ;
                TreeModel node = new TreeModel
                {
                    id = item.ID,
                    name = Code.ToUpper(),
                    value = item.ID,
                    showcheck = false,
                    checkstate = 0,
                    isexpand = false,
                    parentId = item.ParentId
                };
                treeList.Add(node);
            }
            return Success(treeList, "获取成功");
        }
        /// <summary>
        /// 获取套件列表
        /// </summary>
        /// <param name="materialid">物料ID</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<MaterialClassView>> GetMaterialClassList([FromBody] MaterialClassRequestModel model)
        {
            var whereExpression = Expressionable.Create<ClassEntity>().And(a => a.Deleted == 0 && a.ClassType == ClassTypeEnum.MaterialClass.ToString()).ToExpression();

            var data = await _classServices.FindList(whereExpression);

            var equmapExpression = Expressionable.Create<ClassMappingEntity>().And(a => a.Deleted == 0 && a.MappingId == model.materialId).ToExpression();

            var mapdata = await _classMappingServices.FindList(equmapExpression);

            List<string> selectClassIds = mapdata.Select(g => g.ClassId).ToList();

            var selectClassList = data.Where(g => selectClassIds.Contains(g.ID)).ToList();

            MaterialClassView materialClassView = new MaterialClassView()
            {
                Item = data.Where(g => !selectClassIds.Contains(g.ID)).ToList(),
                SelectItem = selectClassList
            };
            return Success(materialClassView, "获取成功");
        }
        /// <summary>
        /// 获取物料属性值
        /// </summary>
        [HttpPost]
        public async Task<MessageModel<List<MaterialPropertyValueEntity>>> GetPropertyValuePageList(string materialid, string key = "")
        {
            var classWhere = Expressionable.Create<MaterialPropertyValueEntity>().And(a => a.Deleted == 0 && a.MaterialId == materialid).ToExpression();

            var data = await _propertyValueServices.FindList(classWhere);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 更新物料套件
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> SaveMaterialMapping([FromBody] RequestClassMappingModel request)
        {
            var material = await _materialServices.QueryById(request.MappingId);
            //var peerwhereExpression = Expressionable.Create<MaterialEntity>().And(a => a.ParentId == material.ParentId && a.Deleted == 0).ToExpression();
            //var peerData = await _materialServices.FindList(peerwhereExpression);
            if (material == null)
            {
                return Failed("保存失败,未查询到物料信息");
            }
            //List<string> materialIds = peerData.Select(g => g.ID).ToList();
            var data = new MessageModel<string>() { success = true };
            List<string> oldClassId = new List<string>();
            if (material != null)
            {
                var mapWhereExpression = Expressionable.Create<ClassMappingEntity>().And(a => a.MappingId == material.ID).ToExpression();
                var mapData = await _classMappingServices.FindList(mapWhereExpression);

                List<string> mapIds = mapData.Select(g => g.ID).ToList();
                oldClassId = mapData.Select(g => g.ClassId).Distinct().ToList();
                data.success = await _classMappingServices.DeleteByIds(mapIds.ToArray());
            }
            if (request.MappingList?.Count > 0)
            {
                List<ClassMappingEntity> classMappings = new List<ClassMappingEntity>();
                foreach (var item in request.MappingList)
                {
                    ClassMappingEntity entity = new ClassMappingEntity() { };
                    entity.ClassId = item.ClassId;
                    entity.MappingId = item.MappingId;
                    entity.Deleted = 0;
                    entity.CreateCustomGuid("System");
                    classMappings.Add(entity);
                }
                //data.success = await _classMappingServices.StorageList(classMappings, new string[] { "CLASS_ID", "MAPPING_ID" });
                data.success = await _classMappingServices.Add(classMappings) > 0;
            }
            List<string> classIds = request.MappingList?.Select(g => g.ClassId).ToList();

            oldClassId = oldClassId.Except(classIds).ToList();
            if (oldClassId.Count > 0)
            {
                //删除已存在的Class默认关联属性值
                var propertyValueExpression = Expressionable.Create<MaterialPropertyValueEntity>().And(a => a.Deleted == 0
                && a.MaterialId == material.ID
                && oldClassId.Contains(a.ClassId)).ToExpression();
                var propertyValueData = await _propertyValueServices.FindList(propertyValueExpression);
                List<string> propertyValueIds = propertyValueData?.Select(g => g.ID).ToList();
                if (propertyValueIds.Count > 0)
                    await _propertyValueServices.DeleteByIds(propertyValueIds.ToArray());
            }
            if(classIds.Count > 0)
            {
                //新增套件默认属性值
                var propertyExpression = Expressionable.Create<PropertyEntity>().And(a => a.Deleted == 0 && classIds.Contains(a.ClassId)).ToExpression();

                var propertyData = await _propertyServices.FindList(propertyExpression);

                //新增套件默认属性值
                var existPropertyValueExpression = Expressionable.Create<MaterialPropertyValueEntity>().And(a => a.Deleted == 0
                && a.MaterialId == material.ID
                && classIds.Contains(a.ClassId)).ToExpression();
                var existPropertyValueList = await _propertyValueServices.FindList(existPropertyValueExpression);

                List<MaterialPropertyValueEntity> list = new List<MaterialPropertyValueEntity>();
                foreach (var item in propertyData)
                {
                    if(existPropertyValueList.Exists(a=> a.ClassId == item.ClassId && a.PropertyCode == item.PropertyCode))
                    {
                        continue;
                    }

                    MaterialPropertyValueEntity propertyValueEntity = new MaterialPropertyValueEntity()
                    {
                        ClassId = item.ClassId,
                        MaterialId = request.MappingId,
                        PropertyCode = item.PropertyCode,
                        PropertyValue = item.DefaultValue,
                        Remark = item.Remark?.ToString(),
                    };
                    propertyValueEntity.CreateCustomGuid("System");
                    list.Add(propertyValueEntity);
                }
                if (list.Count > 0)
                {
                    await _propertyValueServices.Add(list);
                }
                data.success = true;
            }
            
            if (data.success)
            {
                return Success("", "保存成功");
            }
            else
            {
                return Failed("保存失败");
            }
        }

        /// <summary>
        /// 获取物料数据（下级）
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<MaterialEntity>>> GetChirdList(string companyId, string key)
        {
            var whereExpression = Expressionable.Create<MaterialEntity>().And(a => a.CompanyId == companyId).ToExpression();
            //查询公司下所有得物料
            var materialList = await _materialServices.FindList(whereExpression);
            //获取当前输入得key节点物料
            MaterialEntity materialEntity = materialList.Where(x => x.Code.Contains(key)).FirstOrDefault();
            var list = new List<MaterialEntity>();
            if (materialList.Any() && materialEntity != null)
            {
                list = _materialServices.GetChildMaterial(materialList, materialEntity, list);
            }
            return Success(list, "获取成功");
        }

        /// <summary>
        ///  获取物料数据（上级信息，不能是系列）
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<MaterialEntity>>> GetParentList(string companyId, string key)
        {
            var whereExpression = Expressionable.Create<MaterialEntity>().And(a => a.CompanyId == companyId).ToExpression();
            //查询公司下所有得物料
            var materialList = await _materialServices.FindList(whereExpression);
            //获取当前输入得key节点物料
            MaterialEntity materialEntity = materialList.Where(x => x.Code.Contains(key)).FirstOrDefault();

            var list = new List<MaterialEntity>();
            if (materialList.Any() && materialEntity != null)
            {
                list = _materialServices.GetParentMaterial(materialList, materialEntity, list);
            }
            return Success(list, "获取成功");

        }

        /// <summary>
        ///  获取物料数据（上级信息，不能是系列）
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<PageModel<MaterialEntity>>> GetPageSelectList([FromBody] MaterialRequestModel materialRequest)
        {
            var whereExpression = Expressionable.Create<MaterialEntity>().AndIF(string.IsNullOrEmpty(materialRequest.SearchType),a => a.Deleted == 0)
                    .AndIF(!string.IsNullOrEmpty(materialRequest.Description), a => a.Description.Contains(materialRequest.Description))
                    .AndIF(!string.IsNullOrEmpty(materialRequest.type), a => a.Plant.Contains(materialRequest.type))

                    .AndIF(!string.IsNullOrEmpty(materialRequest.key), a => a.CategoryName.Contains(materialRequest.key)
                    ).ToExpression();


            var data = await _materialServices.QueryPage(whereExpression, materialRequest.pageIndex, materialRequest.pageSize);
            return Success(data, "获取成功");

        }

        /// <summary>
        ///  根据厂别(制程)获取物料信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<MaterialEntity>>> GetListByPlant(string plant, string type)
        {
            var whereExpression = Expressionable.Create<MaterialEntity>().And(a => a.Deleted == 0 && a.Type == type)
                    .AndIF(!string.IsNullOrEmpty(plant), a => a.Plant.Contains(plant)).ToExpression();
            var data = await _materialServices.FindList(whereExpression);
            return Success(data, "获取成功");

        }

        /// <summary>
        ///  根据厂别(制程)获取物料信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<MaterialEntity>>> GetCategoryListBySeries(string seriesCode)
        {
            Expression<Func<MaterialEntity, bool>> whereExpression = a => true;

            var list = await _materialServices.FindList(whereExpression);

            var seriesMaterial = list.Where(t => t.Code == seriesCode && t.Type == "S").FirstOrDefault();
            if (seriesMaterial != null)
            {
                list = list.FindAll(t => t.ParentId == seriesMaterial.ID);
            }
            return Success(list, "获取成功");

        }


        /// <summary>
        /// 根据厂别(制程)获取系列机种树
        /// </summary>
        /// <param name="plant">可能是多个得情况逗号分割的情况(,)</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<TreeModel>>> GetSeriesCategoryTree(string plant)
        {
            var whereExpressionSeries = Expressionable.Create<SeriesEntity>().And(a => a.Deleted == 0)
                .AndIF(!string.IsNullOrEmpty(plant), a => plant.Contains(a.Plant)).ToExpression();
            var seriesList = (await _SeriesServices.FindList(whereExpressionSeries)).OrderBy(t => t.Code);

            Expression<Func<SeriesCategoryEntity, bool>> whereExpression = a => true;
            var mapList = await _SeriesCategoryServices.FindList(whereExpression);

            var treeList = new List<TreeModel>();
            treeList.Add(new TreeModel()
            {
                id = "0",
                name = "全部",
                value = "",
                showcheck = false,
                checkstate = 1,
                isexpand = false,
                parentId = ""
            });
            foreach (var item in seriesList)
            {
                var node = new TreeModel
                {
                    id = item.ID,
                    name = item.Code.ToUpper(),
                    value = item.ID,
                    showcheck = false,
                    icon = "fa fa-cubes",
                    checkstate = 0,
                    isexpand = false,
                    title = DFMCommonModel.SeriesType,
                    parentId = "0"
                };

                var categoryList = mapList.Where(t => t.Seriesid == item.ID);
                foreach (var category in categoryList)
                {
                    var categoryNode = new TreeModel
                    {
                        id = category.ID,
                        name = category.Category?.Code.ToUpper(),
                        value = category.Category?.ID,
                        showcheck = false,
                        checkstate = 1,
                        icon = "fa fa-cube",
                        isexpand = false,
                        title = item.Code,
                        parentId = item.ID
                    };
                    treeList.Add(categoryNode);
                }
                treeList.Add(node);
            }
            return Success(treeList, "获取成功");

        }




        /// <summary>
        ///  根据机种字符串获取物料
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<MaterialEntity>>> GetListByCategory(string categoryStrs)
        {
            var whereExpression = Expressionable.Create<MaterialEntity>().And(a => a.Deleted == 0)
                    .AndIF(!string.IsNullOrEmpty(categoryStrs), a => categoryStrs.Contains(a.Categorycode)).ToExpression();
            var list = await _materialServices.FindList(whereExpression);
            foreach (var material in list)
            {
                material.Code = material.Code.ToUpper();
                material.Version = material.Version?.ToUpper();
            }
            return Success(list, "获取成功");

        }

        /// <summary>
        ///  根据物料编号,物料群组字符串获取物料信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<MaterialEntity>>> GetListByCodeByGroup(string keyValueStr)
        {
            var whereExpression = Expressionable.Create<MaterialEntity>().And(a => a.Deleted == 0)
                    .AndIF(!string.IsNullOrEmpty(keyValueStr), a => keyValueStr.Contains(a.Code) || keyValueStr.Contains(a.CategoryName)).ToExpression();
            var list = await _materialServices.FindList(whereExpression);

            foreach (var material in list)
            {
                material.Code = material.Code.ToUpper();
                material.Version = material.Version?.ToUpper();
            }
            return Success(list, "获取成功");

        }

        /// <summary>
        /// 根据编码列表获取物料基本信息
        /// </summary>
        /// <param name="model"></param>
        /// 专用，误删
        [HttpPost]
        public async Task<MessageModel<MaterialEntity>> GetListModel(MaterialEntity model)
        {
            var data = await _materialServices.FindEntity(model);
            return Success(data, "获取成功");
        }
        /// <summary>
        /// 根据code获取物料信息
        /// </summary>
        /// <param name="materialRequest">系列,机种,物料编号</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<PageModel<MaterialEntity>>> GetPageListByCode([FromBody] MaterialRequestModel materialRequest)
        {
            var whereExpression = Expressionable.Create<MaterialEntity>().And(a => a.Deleted == 0)
                    .AndIF(!string.IsNullOrEmpty(materialRequest.id), a => a.Categorycode == materialRequest.id || a.Code.Contains(materialRequest.key)).ToExpression();

            var data = await _materialServices.QueryPage(whereExpression, materialRequest.pageIndex, materialRequest.pageSize);
            return Success(data, "获取成功");

        }

        /// <summary>
        /// 根据机种Code字符串获取物料
        /// </summary>
        /// <param name="categoryCodes">机种Code字符串</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<MaterialEntity>>> GetListByCategoryCodes(string categoryCodes)
        {
            var whereExpression = Expressionable.Create<MaterialEntity>().And(a => a.Deleted == 0)
                    .AndIF(!string.IsNullOrEmpty(categoryCodes), a => categoryCodes.Contains(a.Code)).ToExpression();

            var list = await _materialServices.FindList(whereExpression);

            foreach (var material in list)
            {
                material.Code = material.Code.ToUpper();
                material.Version = material.Version?.ToUpper();
            }
            return Success(list, "获取成功");

        }
        /// <summary>
        /// 根据物料列表获取物料信息
        /// </summary>
        /// <param name="models"></param>
        /// <param name="bu"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<MaterialEntity>>> GetListByModels(List<MaterialEntity> models, string bu = "")
        {

            var keyValueStr = string.Join(",", models.Select(t => t.Code).ToArray());
            var whereExpression = Expressionable.Create<MaterialEntity>().And(a => a.Deleted == 0 && keyValueStr.Contains(a.Code))
                    .ToExpression();

            var materialModels = await _materialServices.FindList(whereExpression);
            List<MaterialEntity> resList = new List<MaterialEntity>();

            foreach (var model in models)
            {
                if (!string.IsNullOrEmpty(model.Version))
                {

                    MaterialEntity materialModel = materialModels.Where(t => (t.Code == model.Code) || (t.CustomerMaterialcode == model.Code)).FirstOrDefault();

                    if (!string.IsNullOrEmpty(model.Plant))
                        materialModel = materialModels.Where(t => ((t.Code == model.Code) || (t.CustomerMaterialcode == model.Code)) && t.Plant == model.Plant).FirstOrDefault();
                    if (materialModel != null)
                        resList.Add(materialModel);
                }
                else
                {
                    if (!string.IsNullOrEmpty(bu) && bu == "10")
                    {
                        MaterialEntity materialModel = materialModels.Where(t => t.Code == model.Code).OrderByDescending(t => t.Version).FirstOrDefault();
                        if (!string.IsNullOrEmpty(model.Plant))
                            materialModel = materialModels.Where(t => t.Code == model.Code && t.Plant == model.Plant).OrderByDescending(t => t.Version).FirstOrDefault();
                        if (materialModel != null)
                            resList.Add(materialModel);
                    }
                    else
                    {
                        MaterialEntity materialModel = materialModels.Where(t => t.Code == model.Code || t.CustomerMaterialcode == model.Code).OrderByDescending(t => t.Version).FirstOrDefault();
                        if (!string.IsNullOrEmpty(model.Plant))
                            materialModel = materialModels.Where(t => (t.Code == model.Code || t.CustomerMaterialcode == model.Code) && t.Plant == model.Plant).OrderByDescending(t => t.Version).FirstOrDefault();
                        if (materialModel != null)
                            resList.Add(materialModel);
                    }
                }
            }
            return Success(resList, "获取成功");

        }

        /// <summary>
        /// 获取物料履历
        /// </summary>
        /// <param name="code"></param>
        /// <param name="plant"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<MaterialEntity>> GetResumeList(string code, string plant)
        {

            var whereExpression = Expressionable.Create<MaterialEntity>().And(a => a.Deleted == 0 && a.Code == code && a.Plant == plant)
                    .ToExpression();

            var data = (await _materialServices.FindList(whereExpression)).FirstOrDefault();
            return Success(data, "获取成功");

        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] MaterialEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {

                var exist = await _materialServices.isExist(request);
                if (!exist)
                {
                    return Failed(request.Version + "版本下的，" + request.Code + "物料已存在！");
                }

                request.CreateCustomGuid(request.CreateUserId);
                data.success = await _materialServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                    //data.response = id.ObjToString();
                    //data.msg = "添加成功";
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _materialServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();

            var whereExpression = Expressionable.Create<MaterialEntity>().And(a =>ids.Contains(a.ID)).ToExpression();

            data.success = await _materialServices.Update(new MaterialEntity() { Deleted = 1 }, whereExpression);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }

        [HttpDelete("{id}")]
        public async Task<MessageModel<string>> Export(string id)
        {
            var data = new MessageModel<string>();
            var whereExpression = Expressionable.Create<MaterialEntity>().And(a => a.ID==id).ToExpression();
            data.success = await _materialServices.Update(new MaterialEntity() { Deleted = 1 }, whereExpression);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }

        /// <summary>
        /// 导入模板下载
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> ImportExcelTemplates()
        {
            ExcelImporter Importer = new ExcelImporter();
            var result = await Importer.GenerateTemplateBytes<MaterialDto>();
            var fs = new MemoryStream(result);
            return new XlsxFileResult(stream: fs, fileDownloadName: "导入模板");
        }

        /// <summary>
        /// 批量导入
        /// </summary>
        /// <param name="file"></param>
        /// <returns></returns>
        [Consumes("multipart/form-data")]
        [HttpPost]
        public async Task<MessageModel<string>> ImportExcelAsync(IFormFile file)
        {
            ExcelImporter Importer = new ExcelImporter();
            var stream = file.OpenReadStream();
            var import = await Importer.Import<MaterialDto>(stream);
            if (import.Data.Count() < 1)
                return Failed("请导入有内容的表格");
            // 返回 导入异常信息
            if (import.Exception != null)
                return Failed(import.Exception.ToString());
            //异常错误
            if (import.RowErrors.Count > 0)
                return Failed(JsonConvert.SerializeObject(import.RowErrors));
            var studentList = new List<MaterialEntity>();
            foreach (var item in import.Data)
            {
                MaterialEntity student = new MaterialEntity()
                {
                    Code = item.Code,
                    Description = item.Description,
                    Accesstime = item.Accesstime,
                    Version = item.Version
                };

                student.CreateCustomGuid("System");
                studentList.Add(student);
            }
            //数据库列名
            await _materialServices.StorageList(studentList, new string[] { "PROC_CODE", "PROC_NAME" });
            var Count = _materialServices.AddBigData(studentList);
            //导入记录
            /*
            var fileName = file.FileName;
            string fileExtension = file.FileName.Substring(file.FileName.LastIndexOf(".") + 1);//获取文件名后缀
                                               
            //把stream转换成byte[]
            byte[] bytes = new byte[stream.Length];
            stream.Read(bytes, 0, bytes.Length);
            //设置当前流的位置为流的开始
            stream.Seek(0, SeekOrigin.Begin);
            //把byte[]写入文件
            FileStream fs = new FileStream("C:\\" + file.FileName, FileMode.Create);
            BinaryWriter bw = new BinaryWriter(fs);
            bw.Write(bytes);
            bw.Close();
            fs.Close();
            */
            return Success("", "成功");
        }

        /// <summary>
        /// 导出Excel文件
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> ByList()
        {
            Expression<Func<MaterialEntity, bool>> whereExpression = a => true;
            List<MaterialEntity> list = await _materialServices.FindList(whereExpression);
            var data = _mapper.Map<List<MaterialDto>>(list);
            IExcelExporter _excelExporter = new ExcelExporter();
            var result = await _excelExporter.ExportAsByteArray(data);
            var fs = new MemoryStream(result);

            return new XlsxFileResult(stream: fs, "物料信息.xlsx");
        }
    }


    public class MaterialRequestModel : RequestPageModelBase
    {
        public string id { get; set; }
        public string key { get; set; }
        public string type { get; set; }
        public string Description { get; set; }
        public int Count { get; set; }
        public string Code { get; set; }
        public string CategoryCode { get; set; }
        public string SearchType { get; set; }
    }
    public class MaterialClassRequestModel 
    {
        public string materialId { get; set; }
    
    }
}