using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;
using SEFA.DFM.Services;

namespace SEFA.DFMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [Authorize(Permissions.Name)]
    public class MaterialProcessDataController : BaseApiController
    {
        /// <summary>
        /// MaterialProcessData
        /// </summary>
        private readonly IMaterialProcessDataServices _materialProcessDataServices;
    
        public MaterialProcessDataController(IMaterialProcessDataServices MaterialProcessDataServices)
        {
            _materialProcessDataServices = MaterialProcessDataServices;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<MaterialProcessDataEntity>>> GetList([FromBody] MaterialProcessDataRequestModel reqModel)
        {
            var data = await _materialProcessDataServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<MaterialProcessDataEntity>>> GetPageList([FromBody] MaterialProcessDataRequestModel reqModel)
        {
            var data = await _materialProcessDataServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<MaterialProcessDataEntity>> GetEntity(string id)
        {
            var data = await _materialProcessDataServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] MaterialProcessDataEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _materialProcessDataServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _materialProcessDataServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> Insert([FromBody] MaterialProcessDataEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _materialProcessDataServices.Add(request) > 0;
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _materialProcessDataServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed( "删除失败");
            }
        }
    }
    //public class MaterialProcessDataRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}