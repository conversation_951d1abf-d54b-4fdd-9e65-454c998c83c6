using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;
using SqlSugar;

namespace SEFA.DFMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [Authorize(Permissions.Name)]
    public class MaterialTypeRelController : BaseApiController
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly IMaterialTypeRelServices _materialTypeRelServices;
    
        public MaterialTypeRelController(IMaterialTypeRelServices MaterialTypeRelServices)
        {
            _materialTypeRelServices = MaterialTypeRelServices;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<MaterialTypeRelEntity>>> GetList([FromBody] MaterialTypeRelRequestModel reqModel)
        {
            Expression<Func<MaterialTypeRelEntity, bool>> whereExpression = Expressionable.Create<MaterialTypeRelEntity>().And(a => a.Deleted == 0)
                      .AndIF(!string.IsNullOrEmpty(reqModel.Categorycode), a => a.Categorycode.Contains(reqModel.Categorycode))
                      .AndIF(!string.IsNullOrEmpty(reqModel.Type), a => a.Type.Equals(reqModel.Type)).ToExpression();
            var data = await _materialTypeRelServices.FindList(whereExpression);
            return Success(data, "获取成功");

        }

        [HttpPost]
        public async Task<MessageModel<PageModel<MaterialTypeRelEntity>>> GetPageList([FromBody] MaterialTypeRelRequestModel reqModel)
        {
            Expression<Func<MaterialTypeRelEntity, bool>> whereExpression =  Expressionable.Create<MaterialTypeRelEntity>().And(a => a.Deleted == 0)
                      .AndIF(!string.IsNullOrEmpty(reqModel.Categorycode), a => a.Categorycode.Contains(reqModel.Categorycode))
                      .AndIF(!string.IsNullOrEmpty(reqModel.Type), a => a.Type.Equals(reqModel.Type)).ToExpression();
            var data = await _materialTypeRelServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<MaterialTypeRelEntity>> GetEntity(string id)
        {
            var data = await _materialTypeRelServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] MaterialTypeRelEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _materialTypeRelServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                } 
                else
                {
                    return Failed( "添加失败");
                }
            }
            else
            {
                data.success = await _materialTypeRelServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                } 
                else
                {
                    return Failed( "更新失败");
                }
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _materialTypeRelServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed( "删除失败");
            }
        }
    }
    public class MaterialTypeRelRequestModel : RequestPageModelBase
    {
        public string Categorycode { get; set; }
        public string Type { get; set; }

        public int Deleted { get; set; }
    }
}