using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;

namespace SEFA.DFMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [Authorize(Permissions.Name)]
    public class OpcFuncExecPropController : BaseApiController
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly IOpcFuncExecPropServices _opcFuncExecPropServices;
    
        public OpcFuncExecPropController(IOpcFuncExecPropServices OpcFuncExecPropServices)
        {
            _opcFuncExecPropServices = OpcFuncExecPropServices;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<OpcFuncExecPropEntity>>> GetList(string key = "")
        {
            Expression<Func<OpcFuncExecPropEntity, bool>> whereExpression = a => true;
            var data = await _opcFuncExecPropServices.FindList(whereExpression);
            return Success(data, "获取成功");

        }

        [HttpPost]
        public async Task<MessageModel<PageModel<OpcFuncExecPropEntity>>> GetPageList([FromBody] OpcFuncExecPropRequestModel reqModel)
        {
             
            Expression<Func<OpcFuncExecPropEntity, bool>> whereExpression = a => true;
            var data = await _opcFuncExecPropServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return Success(data, "获取成功");

        }

        [HttpGet("{id}")]
        public async Task<MessageModel<OpcFuncExecPropEntity>> GetEntity(string id)
        {
            var data = await _opcFuncExecPropServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] OpcFuncExecPropEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _opcFuncExecPropServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                } 
                else
                {
                    return Failed( "添加失败");
                }
            }
            else
            {
                data.success = await _opcFuncExecPropServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                } 
                else
                {
                    return Failed( "更新失败");
                }
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _opcFuncExecPropServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed( "删除失败");
            }
        }
    }
    public class OpcFuncExecPropRequestModel : RequestPageModelBase
    {
        public string key { get; set; }
    }
}