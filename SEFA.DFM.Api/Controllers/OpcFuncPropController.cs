using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;
using SEFA.DFM.Services;
using SqlSugar;

namespace SEFA.DFMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [Authorize(Permissions.Name)]
    public class OpcFuncPropController : BaseApiController
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly IOpcFuncPropServices _opcFuncPropServices;
    
        public OpcFuncPropController(IOpcFuncPropServices OpcFuncPropServices)
        {
            _opcFuncPropServices = OpcFuncPropServices;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<OpcFuncPropEntity>>> GetList(string key = "")
        {
            Expression<Func<OpcFuncPropEntity, bool>> whereExpression = a => true;
            var data = await _opcFuncPropServices.FindList(whereExpression);
            return Success(data, "获取成功");

        }

        [HttpPost]
        public async Task<MessageModel<List<OpcFuncPropEntity>>> GetListModel([FromBody] OpcFuncPropRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<OpcFuncPropEntity>()
                   .And(a => a.Deleted == 0)
                   .AndIF(!string.IsNullOrEmpty(reqModel.OpcFunctionId), x => x.OpcFunctionId == reqModel.OpcFunctionId).ToExpression();
            var data = await _opcFuncPropServices.FindList(whereExpression);
            return Success(data, "获取成功");

        }

        [HttpPost]
        public async Task<MessageModel<PageModel<OpcFuncPropEntity>>> GetPageList([FromBody] OpcFuncPropRequestModel reqModel)
        {
             
            Expression<Func<OpcFuncPropEntity, bool>> whereExpression = a => true;
            var data = await _opcFuncPropServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return Success(data, "获取成功");

        }

        [HttpGet("{id}")]
        public async Task<MessageModel<OpcFuncPropEntity>> GetEntity(string id)
        {
            var data = await _opcFuncPropServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] OpcFuncPropEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _opcFuncPropServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                } 
                else
                {
                    return Failed( "添加失败");
                }
            }
            else
            {
                data.success = await _opcFuncPropServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                } 
                else
                {
                    return Failed( "更新失败");
                }
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _opcFuncPropServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed( "删除失败");
            }
        }
    }
    public class OpcFuncPropRequestModel : RequestPageModelBase
    {
        public string key { get; set; }

        public string OpcFunctionId { get; set; }
    }
}