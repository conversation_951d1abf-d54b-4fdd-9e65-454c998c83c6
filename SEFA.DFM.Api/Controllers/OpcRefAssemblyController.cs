using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;

namespace SEFA.DFMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [Authorize(Permissions.Name)]
    public class OpcRefAssemblyController : BaseApiController
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly IOpcRefAssemblyServices _opcRefAssemblyServices;
    
        public OpcRefAssemblyController(IOpcRefAssemblyServices OpcRefAssemblyServices)
        {
            _opcRefAssemblyServices = OpcRefAssemblyServices;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<OpcRefAssemblyEntity>>> GetList(string key = "")
        {
            Expression<Func<OpcRefAssemblyEntity, bool>> whereExpression = a => true;
            var data = await _opcRefAssemblyServices.FindList(whereExpression);
            return Success(data, "获取成功");

        }

        [HttpPost]
        public async Task<MessageModel<PageModel<OpcRefAssemblyEntity>>> GetPageList([FromBody] OpcRefAssemblyRequestModel reqModel)
        {
             
            Expression<Func<OpcRefAssemblyEntity, bool>> whereExpression = a => true;
            var data = await _opcRefAssemblyServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return Success(data, "获取成功");

        }

        [HttpGet("{id}")]
        public async Task<MessageModel<OpcRefAssemblyEntity>> GetEntity(string id)
        {
            var data = await _opcRefAssemblyServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] OpcRefAssemblyEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _opcRefAssemblyServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                } 
                else
                {
                    return Failed( "添加失败");
                }
            }
            else
            {
                data.success = await _opcRefAssemblyServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                } 
                else
                {
                    return Failed( "更新失败");
                }
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _opcRefAssemblyServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed( "删除失败");
            }
        }
    }
    public class OpcRefAssemblyRequestModel : RequestPageModelBase
    {
        public string key { get; set; }
    }
}