using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;
using SqlSugar;
using SEFA.DFM.Model.ViewModels;
using AutoMapper;

namespace SEFA.DFMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [Authorize(Permissions.Name)]
    public class OpcServerController : BaseApiController
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly IOpcServerServices _opcServerServices;
        private readonly IOpcGroupServices _opcGroupServices;
        private readonly IOpcTagServices _opcTagServices;

        public OpcServerController(IOpcServerServices OpcServerServices, IOpcGroupServices opcGroupServices, IOpcTagServices opcTagServices)
        {
            _opcServerServices = OpcServerServices;
            _opcGroupServices = opcGroupServices;
            _opcTagServices = opcTagServices;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<OpcServerEntity>>> GetList(string key = "")
        {
            Expression<Func<OpcServerEntity, bool>> whereExpression = a => true;
            var data = await _opcServerServices.FindList(whereExpression);
            return Success(data, "获取成功");

        }

        /// <summary>
        /// 获取数据
        /// </summary>
        /// <param name="requestModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<OpcServerTreeModel>>> GetTreeList([FromBody] OpcServerTreeModel requestModel)
        {
            var serverWhere = Expressionable.Create<OpcServerEntity>()
                .And(a => a.Deleted == 0)
                .AndIF(requestModel.WhereServerDesc.IsNotEmptyOrNull(), x => x.Description == requestModel.WhereServerDesc)
                .ToExpression();
            var dataServer = await _opcServerServices.FindList(serverWhere);

            var mapperServer = new MapperConfiguration(cfg =>
            {
                cfg.CreateMap<OpcServerEntity, OpcServerTreeModel>()
                    .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Uri))
                    .ForMember(dest => dest.NodeType, opt => opt.MapFrom(src => "Server"));
            }).CreateMapper();
            List<OpcServerTreeModel> datamodels = mapperServer.Map<List<OpcServerTreeModel>>(dataServer);

            var serverIDs = dataServer.Select(x => x.ID).ToList();
            var groupWhere = Expressionable.Create<OpcGroupEntity>()
                .And(a => a.Deleted == 0)
                .AndIF(requestModel.WhereGroupName.IsNotEmptyOrNull(), x => x.Description == requestModel.WhereGroupName)
                .AndIF(serverIDs.Count > 0, x => serverIDs.Contains(x.OpcServerId))
                .ToExpression();
            var dataGroup = await _opcGroupServices.FindList(groupWhere);
            var mapperGroup = new MapperConfiguration(cfg =>
            {
                cfg.CreateMap<OpcGroupEntity, OpcServerTreeModel>()
                    .ForMember(dest => dest.ParentId, opt => opt.MapFrom(src => src.OpcServerId))
                    .ForMember(dest => dest.NodeType, opt => opt.MapFrom(src => "Group"));
            }).CreateMapper();
            datamodels.AddRange(mapperGroup.Map<List<OpcServerTreeModel>>(dataGroup));

            var groupIDs = dataGroup.Select(x => x.ID).ToList();
            var tagWhere = Expressionable.Create<OpcTagEntity>()
                .And(a => a.Deleted == 0)
                .AndIF(requestModel.WhereTagName.IsNotEmptyOrNull(), x => x.Name == requestModel.WhereTagName)
                .AndIF(groupIDs.Count>0, x => groupIDs.Contains(x.OpcGroupId))
                .ToExpression();
            var dataTag = await _opcTagServices.FindList(tagWhere);
            var mapperTag = new MapperConfiguration(cfg =>
            {
                cfg.CreateMap<OpcTagEntity, OpcServerTreeModel>()
                    .ForMember(dest => dest.ParentId, opt => opt.MapFrom(src => src.OpcGroupId))
                    .ForMember(dest => dest.NodeType, opt => opt.MapFrom(src => "Tag"));
            }).CreateMapper();
            datamodels.AddRange(mapperTag.Map<List<OpcServerTreeModel>>(dataTag));

            return Success(datamodels, "获取成功");
        }
        

        [HttpPost]
        public async Task<MessageModel<PageModel<OpcServerEntity>>> GetPageList([FromBody] OpcServerRequestModel reqModel)
        {
             
            Expression<Func<OpcServerEntity, bool>> whereExpression = a => true;
            var data = await _opcServerServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return Success(data, "获取成功");

        }

        [HttpGet("{id}")]
        public async Task<MessageModel<OpcServerEntity>> GetEntity(string id)
        {
            var data = await _opcServerServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] OpcServerEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _opcServerServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                } 
                else
                {
                    return Failed( "添加失败");
                }
            }
            else
            {
                data.success = await _opcServerServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                } 
                else
                {
                    return Failed( "更新失败");
                }
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _opcServerServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed( "删除失败");
            }
        }
    }
    public class OpcServerRequestModel : RequestPageModelBase
    {
        public string key { get; set; }
    }
}