using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;

namespace SEFA.DFMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [Authorize(Permissions.Name)]
    public class OpcServiceLogController : BaseApiController
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly IOpcServiceLogServices _opcServiceLogServices;
    
        public OpcServiceLogController(IOpcServiceLogServices OpcServiceLogServices)
        {
            _opcServiceLogServices = OpcServiceLogServices;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<OpcServiceLogEntity>>> GetList(string key = "")
        {
            Expression<Func<OpcServiceLogEntity, bool>> whereExpression = a => true;
            var data = await _opcServiceLogServices.FindList(whereExpression);
            return Success(data, "获取成功");

        }

        [HttpPost]
        public async Task<MessageModel<PageModel<OpcServiceLogEntity>>> GetPageList([FromBody] OpcServiceLogRequestModel reqModel)
        {
             
            Expression<Func<OpcServiceLogEntity, bool>> whereExpression = a => true;
            var data = await _opcServiceLogServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return Success(data, "获取成功");

        }

        [HttpGet("{id}")]
        public async Task<MessageModel<OpcServiceLogEntity>> GetEntity(string id)
        {
            var data = await _opcServiceLogServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] OpcServiceLogEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _opcServiceLogServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                } 
                else
                {
                    return Failed( "添加失败");
                }
            }
            else
            {
                data.success = await _opcServiceLogServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                } 
                else
                {
                    return Failed( "更新失败");
                }
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _opcServiceLogServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed( "删除失败");
            }
        }
    }
    public class OpcServiceLogRequestModel : RequestPageModelBase
    {
        public string key { get; set; }
    }
}