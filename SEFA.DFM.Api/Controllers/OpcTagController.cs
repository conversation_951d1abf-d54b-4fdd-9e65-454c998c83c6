using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;
using SEFA.DFM.Model.ViewModels;
using SqlSugar;
using Microsoft.AspNetCore.DataProtection.KeyManagement;

namespace SEFA.DFMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [Authorize(Permissions.Name)]
    public class OpcTagController : BaseApiController
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly IOpcTagServices _opcTagServices;
    
        public OpcTagController(IOpcTagServices OpcTagServices)
        {
            _opcTagServices = OpcTagServices;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<OpcTagEntity>>> GetList(string key = "")
        {
            var whereExpression = Expressionable.Create<OpcTagEntity>()
                .AndIF(!string.IsNullOrEmpty(key), a => a.Name.Contains(key) || a.Description.Contains(key))
                .ToExpression();
            var data = await _opcTagServices.FindList(whereExpression);
            return Success(data, "获取成功");

        }

        [HttpPost]
        public async Task<MessageModel<PageModel<OpcTagEntity>>> GetPageList([FromBody] OpcTagRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<OpcTagEntity>()
                .AndIF(!string.IsNullOrEmpty(reqModel.Name), a => a.Name.Contains(reqModel.Name))
                .AndIF(!string.IsNullOrEmpty(reqModel.Description), a => a.Name.Contains(reqModel.Description))
                .AndIF(!string.IsNullOrEmpty(reqModel.key), a => a.Name.Contains(reqModel.key) || a.Description.Contains(reqModel.key))
                .ToExpression();
            var data = await _opcTagServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return Success(data, "获取成功");

        }

        [HttpGet("{id}")]
        public async Task<MessageModel<OpcTagEntity>> GetEntity(string id)
        {
            var data = await _opcTagServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] OpcTagEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _opcTagServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                } 
                else
                {
                    return Failed( "添加失败");
                }
            }
            else
            {
                data.success = await _opcTagServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                } 
                else
                {
                    return Failed( "更新失败");
                }
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _opcTagServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed( "删除失败");
            }
        }
    }
    public class OpcTagRequestModel : RequestPageModelBase
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public string key { get; set; }
    }
}