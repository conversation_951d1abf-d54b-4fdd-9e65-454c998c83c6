using AutoMapper;
using Magicodes.ExporterAndImporter.Excel;
using Magicodes.ExporterAndImporter.Excel.AspNetCore;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Common.HttpContextUser;
using SEFA.Base.Model;
using SEFA.DFM.Controllers;
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using SqlSugar;

namespace SEFA.DFMApi.Controllers
{

    /// <summary>
    /// 岗位信息
    /// </summary>
	[Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class PostController : BaseApiController
    {
        private readonly IPostServices _postServices;
        private readonly IMapper _mapper;
        private readonly IUser _user;
        public PostController(IPostServices PostServices
            , IMapper mapper
            , IUser user
            )
        {
            _postServices = PostServices;
            _mapper = mapper;
            _user = user;
        }


        /// <summary>
        /// 获取岗位列表
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<PostEntity>>> GetList([FromBody] PostRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<PostEntity>().And(a => a.Deleted == 0)
            .AndIF(!string.IsNullOrEmpty(reqModel.Departmentid), a => a.Departmentid == reqModel.Departmentid)
            .AndIF(!string.IsNullOrEmpty(reqModel.key), a => a.Name.Contains(reqModel.key) || a.Encode.Contains(reqModel.key))
            .AndIF(!string.IsNullOrEmpty(reqModel.Type), x => x.Type == reqModel.Type)
            .AndIF(!string.IsNullOrEmpty(reqModel.PostRange), x => x.PostRange == reqModel.PostRange)
            .ToExpression();
            var data = await _postServices.FindList(whereExpression);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 获取岗位分页列表
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<PageModel<PostEntity>>> GetPageList([FromBody] PostRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<PostEntity>().And(a => a.Deleted == 0)
                    .AndIF(!string.IsNullOrEmpty(reqModel.Departmentid), a => a.Departmentid == reqModel.Departmentid)
                    .AndIF(!string.IsNullOrEmpty(reqModel.key), a => a.Name.Contains(reqModel.key) || a.Encode.Contains(reqModel.key))
                    .AndIF(!string.IsNullOrEmpty(reqModel.Type), x => x.Type == reqModel.Type)
                    .AndIF(!string.IsNullOrEmpty(reqModel.PostRange), x => x.PostRange == reqModel.PostRange)
                    .ToExpression();
            var data = await _postServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize, reqModel.orderByFileds);
            return Success(data, "获取成功");
        }


        /// <summary>
        /// 获取岗位信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("{id}")]
        public async Task<MessageModel<PostEntity>> GetEntity(string id)
        {
            var data = await _postServices.QueryById(id);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 保存岗位信息
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] PostEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _postServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _postServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _postServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }




        #region 数据导入、导出


        /// <summary>
        /// 数据导入
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> ImportData([FromForm] FileImportDto input)
        {
            var importer = new ExcelImporter();
            var stream = input.File.OpenReadStream();
            var import = await importer.Import<PostExcelDto>(stream);
            if (import.Data.Count() < 1)
                return Failed("表格中没有效数据");
            // 返回 导入异常信息
            if (import.Exception != null)
                return Failed(import.Exception.ToString(), "出现错误");

            var excelData = import.Data.Where(x => x.Name != null).ToList();
            if (excelData.Count() < 1)
                return Failed("表格中无有效数据");

            var allData = await _postServices.FindList(x => x.Deleted == 0);
            var addList = new List<PostEntity>();
            var updateList = new List<PostEntity>();
            foreach (var item in excelData)
            {
                var entity = allData.Where(x => x.Name == item.Name && x.Type == item.Type).FirstOrDefault();
                if (entity!=null)
                {
                    entity.WorkHours = item.WorkHours;
                    entity.Description = item.Description;
                    entity.PostPrice = item.PostPrice;
                    updateList.Add(entity);
                }
                else
                {
                    entity = new PostEntity();
                    entity.Name = item.Name;
                    entity.Type = item.Type;
                    entity.PostPrice = item.PostPrice;
                    entity.WorkHours = item.WorkHours;
                    entity.Description= item.Description;
                    entity.CreateCustomGuid(_user.Name);
                    entity.CreateUserId = _user.Name;
                    addList.Add(entity);
                }
            }
            if (updateList.Any())
            {
                await _postServices.Update(updateList);
            }

            if (addList.Any())
            {
                await _postServices.Add(addList);
            }
            var improtInfo = $"导入{addList.Count()}条数据，更新{updateList.Count()}条数据;";
            return Success(improtInfo, "导入成功");
        }

        /// <summary>
        /// 数据导出
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> ExportData()
        {
            ExcelExporter exporter = new ExcelExporter();
            var whereExpression = Expressionable.Create<PostEntity>().And(a => a.Deleted == 0).ToExpression();
            var query = await _postServices.FindList(whereExpression);
            var queryData = query.Select(item =>
            {
                var dto = new PostExcelDto();
                dto.Name = item.Name;
                dto.Description = item.Description;
                dto.Type = item.Type;
                dto.WorkHours = item.WorkHours;
                return dto;
            }).ToList();
            var result = await exporter.ExportAsByteArray<PostExcelDto>(queryData);
            var fs = new MemoryStream(result);
            return new XlsxFileResult(stream: fs, fileDownloadName: $"岗位数据-{DateTime.Now.ToString("yyyy-MM-dd")}");
        }

        /// <summary>
        /// 下载模板
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> DownLoadTemplate()
        {
            ExcelImporter Importer = new ExcelImporter();
            var result = await Importer.GenerateTemplateBytes<PostExcelDto>();
            var fs = new MemoryStream(result);
            return new XlsxFileResult(stream: fs, fileDownloadName: "岗位_导入模板");
        }

        #endregion
    }
    public class PostRequestModel : RequestPageModelBase
    {
        public string companyId { get; set; }
        public string Departmentid { get; set; }
        public string key { get; set; }

        /// <summary>
        /// 岗位类型
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// 岗位职责范围
        /// </summary>
        public string PostRange { get; set; }
    }
}