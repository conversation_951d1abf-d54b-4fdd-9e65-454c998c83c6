using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Nacos.V2.Utils;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.DFM.Controllers;
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SqlSugar;

namespace SEFA.DFMApi.Controllers
{
    /// <summary>
    /// 打印模板管理
    /// </summary>
	[Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class PrintTplController : BaseApiController
    {
        private readonly IPrintTplServices _printTplServices;

        public PrintTplController(IPrintTplServices PrintTplServices)
        {
            _printTplServices = PrintTplServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<PrintTplEntity>>> GetList([FromBody] PrintTplRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<PrintTplEntity>().And(x => x.Deleted == 0)
            .And(x => x.Status == 1)//状态为启用
            .AndIF(reqModel.type.IsNotNullOrWhiteSpace(), x => x.Type.Equals(reqModel.type))
              .ToExpression();
            var data = await _printTplServices.FindList(whereExpression);
            return Success(data, "获取成功");

        }

        [HttpPost]
        public async Task<MessageModel<PageModel<PrintTplEntity>>> GetPageList([FromBody] PrintTplRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<PrintTplEntity>().And(x => x.Deleted == 0)
               .And(x => x.Status == 1)//状态为启用
               .AndIF(reqModel.type.IsNotNullOrWhiteSpace(), x => x.Type.Equals(reqModel.type))
               .ToExpression();
            var data = await _printTplServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return Success(data, "获取成功");

        }

        [HttpGet("{id}")]
        public async Task<MessageModel<PrintTplEntity>> GetEntity(string id)
        {
            var data = await _printTplServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] PrintTplEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _printTplServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _printTplServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveTPL([FromBody] PrintTplEntity request)
        {
            var entity = await _printTplServices.FindEntity(request.ID);
            if (entity is null)
            {
                return Failed("模板不存在!");
            }
            entity.TplJson = request.TplJson;
            var result = await _printTplServices.Update(entity);
            if (result)
            {
                return Success("", "更新成功");
            }
            else
            {
                return Failed("更新失败");
            }
        }



        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _printTplServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    public class PrintTplRequestModel : RequestPageModelBase
    {
        public string key { get; set; }

        /// <summary>
        /// 类型
        /// </summary>
        public string type { get; set; }
    }
}