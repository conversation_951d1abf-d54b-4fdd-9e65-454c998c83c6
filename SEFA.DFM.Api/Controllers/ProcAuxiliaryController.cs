using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.DFM.Controllers;
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SqlSugar;
using System.Linq.Expressions;

namespace SEFA.DFMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class ProcAuxiliaryController : BaseApiController
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly IProcAuxiliaryServices _procAuxiliaryServices;

        public ProcAuxiliaryController(IProcAuxiliaryServices ProcAuxiliaryServices)
        {
            _procAuxiliaryServices = ProcAuxiliaryServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<ProcAuxiliaryEntity>>> GetList(string key = "")
        {
            Expression<Func<ProcAuxiliaryEntity, bool>> whereExpression = a => true;
            var data = await _procAuxiliaryServices.FindList(whereExpression);
            return Success(data, "获取成功");

        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<PageModel<ProcAuxiliaryEntity>>> GetPageList([FromBody] ProcAuxiliaryRequestModel request)
        {

            var whereExpression = Expressionable.Create<ProcAuxiliaryEntity>().And(a => a.Deleted == 0)
                .AndIF(request.RoutingDetailIds.Any(), a => request.RoutingDetailIds.Contains(a.RoutingDetailId))
                .ToExpression();

            var data = await _procAuxiliaryServices.QueryPage(whereExpression, request.pageIndex, request.pageSize);
            return Success(data, "获取成功");

        }

        [HttpGet("{id}")]
        public async Task<MessageModel<ProcAuxiliaryEntity>> GetEntity(string id)
        {
            var data = await _procAuxiliaryServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] ProcAuxiliaryEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                if (await _procAuxiliaryServices.isExist(request, false))
                {
                    return Failed("物料号已经存在");
                }
                data.success = await _procAuxiliaryServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                if (await _procAuxiliaryServices.isExist(request, true))
                {
                    return Failed("物料号已经存在");
                }
                data.success = await _procAuxiliaryServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _procAuxiliaryServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    public class ProcAuxiliaryRequestModel : RequestPageModelBase
    {
        public string[] RoutingDetailIds { get; set; }

    }
}