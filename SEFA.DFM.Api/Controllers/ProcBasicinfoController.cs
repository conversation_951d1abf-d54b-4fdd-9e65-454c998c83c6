using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.DFM.Controllers;
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SqlSugar;
using System.Linq.Expressions;

namespace SEFA.DFMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class ProcBasicinfoController : BaseApiController
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly IProcBasicinfoServices _procBasicinfoServices;

        public ProcBasicinfoController(IProcBasicinfoServices ProcBasicinfoServices)
        {
            _procBasicinfoServices = ProcBasicinfoServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<ProcBasicinfoEntity>>> GetList(string key = "")
        {
            Expression<Func<ProcBasicinfoEntity, bool>> whereExpression = a => true;
            var data = await _procBasicinfoServices.FindList(whereExpression);
            return Success(data, "获取成功");

        }

        [HttpPost]
        public async Task<MessageModel<PageModel<ProcBasicinfoEntity>>> GetPageList([FromBody] ProcBasicinfoRequestModel m)
        {

            Expression<Func<ProcBasicinfoEntity, bool>> whereExpression = Expressionable.Create<ProcBasicinfoEntity>().And(a => a.Deleted == 0)
                    .AndIF(!string.IsNullOrEmpty(m.detailid), a => a.DetailId.Contains(m.detailid))
                    .AndIF(!string.IsNullOrEmpty(m.resid), a => a.ResId.Contains(m.resid))
                    .ToExpression();
            var data = await _procBasicinfoServices.QueryPage(whereExpression, m.pageIndex, m.pageSize);
            return Success(data, "获取成功");

        }

        [HttpGet("{id}")]
        public async Task<MessageModel<ProcBasicinfoEntity>> GetEntity(string id)
        {
            var data = await _procBasicinfoServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] ProcBasicinfoEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _procBasicinfoServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _procBasicinfoServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _procBasicinfoServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    public class ProcBasicinfoRequestModel : RequestPageModelBase
    {
        public string resid { get; set; }
        public string detailid { get; set; }

    }
}