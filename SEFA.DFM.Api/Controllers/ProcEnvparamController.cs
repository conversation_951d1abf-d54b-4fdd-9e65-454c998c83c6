using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.DFM.Controllers;
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SqlSugar;
using System.Linq.Expressions;

namespace SEFA.DFMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class ProcEnvparamController : BaseApiController
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly IProcEnvparamServices _procEnvparamServices;

        public ProcEnvparamController(IProcEnvparamServices ProcEnvparamServices)
        {
            _procEnvparamServices = ProcEnvparamServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<ProcEnvparamEntity>>> GetList(string key = "")
        {
            Expression<Func<ProcEnvparamEntity, bool>> whereExpression = a => true;
            var data = await _procEnvparamServices.FindList(whereExpression);
            return Success(data, "获取成功");

        }

        [HttpPost]
        public async Task<MessageModel<PageModel<ProcEnvparamEntity>>> GetPageList([FromBody] ProcEnvparamRequestModel m)
        {


            Expression<Func<ProcEnvparamEntity, bool>> whereExpression = Expressionable.Create<ProcEnvparamEntity>().And(a => a.Deleted == 0)
                    .AndIF(!string.IsNullOrEmpty(m.key), a => a.Code.Contains(m.key) || a.Name.Contains(m.key))
                    .AndIF(!string.IsNullOrEmpty(m.detailid), a => a.Detaildid.Contains(m.detailid))
                    .AndIF(!string.IsNullOrEmpty(m.resid), a => a.Resid.Contains(m.resid))
                    .ToExpression();
            var data = await _procEnvparamServices.QueryPage(whereExpression, m.pageIndex, m.pageSize, "Sort asc");
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<ProcEnvparamEntity>> GetEntity(string id)
        {
            var data = await _procEnvparamServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] ProcEnvparamEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _procEnvparamServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _procEnvparamServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _procEnvparamServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    public class ProcEnvparamRequestModel : RequestPageModelBase
    {
        public string key { get; set; }
        public string detailid { get; set; }
        public string resid { get; set; }
    }
}