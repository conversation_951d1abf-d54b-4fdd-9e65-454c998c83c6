using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.DFM.Controllers;
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SqlSugar;
using System.Linq.Expressions;

namespace SEFA.DFMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class ProcProgramController : BaseApiController
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly IProcProgramServices _procProgramServices;

        public ProcProgramController(IProcProgramServices ProcProgramServices)
        {
            _procProgramServices = ProcProgramServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<ProcProgramEntity>>> GetList(string key = "")
        {
            Expression<Func<ProcProgramEntity, bool>> whereExpression = a => true;
            var data = await _procProgramServices.FindList(whereExpression);
            return Success(data, "获取成功");

        }

        [HttpPost]
        public async Task<MessageModel<PageModel<ProcProgramEntity>>> GetPageList([FromBody] ProcProgramRequestModel request)
        {
            var whereExpression = Expressionable.Create<ProcProgramEntity>().And(a => a.Deleted == 0)
                .AndIF(string.IsNullOrEmpty(request.key), a => a.Code.Contains(request.key) || a.Name.Contains(request.key))
                .ToExpression();
            var data = await _procProgramServices.QueryPage(whereExpression, request.pageIndex, request.pageSize);
            return Success(data, "获取成功");

        }

        [HttpGet("{id}")]
        public async Task<MessageModel<ProcProgramEntity>> GetEntity(string id)
        {
            var data = await _procProgramServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] ProcProgramEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                //where = where.And(n => n.FACTORY.Equals(model.FACTORY) && n.VERSION.Equals(model.VERSION) && n.NAME.Equals(model.NAME));
                if (await _procProgramServices.isExist(request, false))
                {
                    return Failed("当前版本程序文件已经存在");
                }
                data.success = await _procProgramServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                if (await _procProgramServices.isExist(request, true))
                {
                    return Failed("当前版本程序文件已经存在");
                }
                data.success = await _procProgramServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _procProgramServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    public class ProcProgramRequestModel : RequestPageModelBase
    {
        public string key { get; set; }
    }
}