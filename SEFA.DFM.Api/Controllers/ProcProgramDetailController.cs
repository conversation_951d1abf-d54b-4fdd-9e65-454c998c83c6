using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;
using SqlSugar;

namespace SEFA.DFMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class ProcProgramDetailController : BaseApiController
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly IProcProgramDetailServices _procProgramDetailServices;

        public ProcProgramDetailController(IProcProgramDetailServices ProcProgramDetailServices)
        {
            _procProgramDetailServices = ProcProgramDetailServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<ProcProgramDetailEntity>>> GetList(string key = "")
        {
            Expression<Func<ProcProgramDetailEntity, bool>> whereExpression = a => true;
            var data = await _procProgramDetailServices.FindList(whereExpression);
            return Success(data, "获取成功");

        }

        [HttpPost]
        public async Task<MessageModel<PageModel<ProcProgramDetailEntity>>> GetPageList([FromBody] ProcProgramDetailRequestModel request)
        {
            var whereExpression = Expressionable.Create<ProcProgramDetailEntity>().And(a => a.Deleted == 0)
               .AndIF(request.RoutingDetailIds.Any(), a => request.RoutingDetailIds.Contains(a.RoutingDetailId))
               .ToExpression();
            var data = await _procProgramDetailServices.QueryPage(whereExpression, request.pageIndex, request.pageSize, request.orderByFileds);
            return Success(data, "获取成功");

        }

        [HttpGet("{id}")]
        public async Task<MessageModel<ProcProgramDetailEntity>> GetEntity(string id)
        {
            var data = await _procProgramDetailServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] ProcProgramDetailEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                if (await _procProgramDetailServices.isExist(request, false))
                {
                    return Failed("物料号已经存在");
                }
                data.success = await _procProgramDetailServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                if (await _procProgramDetailServices.isExist(request, true))
                {
                    return Failed("物料号已经存在");
                }
                data.success = await _procProgramDetailServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _procProgramDetailServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    public class ProcProgramDetailRequestModel : RequestPageModelBase
    {
        public string[] RoutingDetailIds { get; set; }
    }
}