using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.DFM.Controllers;
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using System.Linq.Expressions;

namespace SEFA.DFMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class ProcResSettingController : BaseApiController
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly IProcResSettingServices _procResSettingServices;

        public ProcResSettingController(IProcResSettingServices ProcResSettingServices)
        {
            _procResSettingServices = ProcResSettingServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<ProcResSettingEntity>>> GetList(string key = "")
        {
            Expression<Func<ProcResSettingEntity, bool>> whereExpression = a => true;
            var data = await _procResSettingServices.FindList(whereExpression);
            return Success(data, "获取成功");

        }

        [HttpPost]
        public async Task<MessageModel<PageModel<ProcResSettingEntity>>> GetPageList([FromBody] ProcResSettingRequestModel reqModel)
        {

            Expression<Func<ProcResSettingEntity, bool>> whereExpression = a => true;
            var data = await _procResSettingServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return Success(data, "获取成功");

        }

        [HttpGet("{id}")]
        public async Task<MessageModel<ProcResSettingEntity>> GetEntity(string id)
        {
            var data = await _procResSettingServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] ProcResSettingEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _procResSettingServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _procResSettingServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _procResSettingServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    public class ProcResSettingRequestModel : RequestPageModelBase
    {
        public string key { get; set; }
    }
}