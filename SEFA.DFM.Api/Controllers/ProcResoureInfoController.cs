using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using SqlSugar;

namespace SEFA.DFM.Api.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class ProcResoureInfoController : ControllerBase
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly IProcResoureInfoServices _procResoureInfoServices;

        public ProcResoureInfoController(IProcResoureInfoServices procResoureInfoServices)
        {
            _procResoureInfoServices = procResoureInfoServices;
        }


        [HttpPost]
        public async Task<MessageModel<PageModel<ProcResoureInfoModel>>> GetPageList([FromBody] ProcResoureInfoRequestModel model)
        {
            var whereExpression = Expressionable.Create<RoutingDetailEntity, RoutingHeadEntity, RoutingProductEntity>()
                      .AndIF(!string.IsNullOrEmpty(model.ProcCode), (rd, rh, rp) => rd.ProcCode.Contains(model.ProcCode))
                      .AndIF(!string.IsNullOrEmpty(model.Factory), (rd, rh, rp) => rh.Factory.Equals(model.Factory))
                      .AndIF(!string.IsNullOrEmpty(model.MaterialCode), (rd, rh, rp) => rp.MaterialCode.Equals(model.MaterialCode)).ToExpression();

            return new MessageModel<PageModel<ProcResoureInfoModel>>()
            {
                msg = "获取成功",
                success = true,
                response = await _procResoureInfoServices.QueryPageList(whereExpression, model.pageIndex, model.pageSize, model.orderByFileds)
            };
        }

    }

    public class ProcResoureInfoRequestModel : RequestPageModelBase
    {
        public string Factory { get; set; }

        public string MaterialCode { get; set; }

        public string ProcCode { get; set; }

    }
}
