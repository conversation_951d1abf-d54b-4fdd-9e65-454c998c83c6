using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.DFM.Controllers;
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SqlSugar;
using System.Linq.Expressions;

namespace SEFA.DFMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class ProcessController : BaseApiController
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly IProcessServices _ProcessServices;

        public ProcessController(IProcessServices ProcessServices)
        {
            _ProcessServices = ProcessServices;
        }
        /// <summary>
        /// 获取全部
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<MessageModel<List<ProcessEntity>>> GetList(string key = "")
        {
            if (string.IsNullOrEmpty(key) || string.IsNullOrWhiteSpace(key))
            {
                key = "";
            }

            Expression<Func<ProcessEntity, bool>> whereExpression = a => true;

            return new MessageModel<List<ProcessEntity>>()
            {
                msg = "获取成功",
                success = true,
                response = await _ProcessServices.FindList(whereExpression)
            };

        }


        /// <summary>
        /// 获取工序信息
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<ProcessEntity>> GetProcEntity([FromBody] ProcessModel model)
        {
            var data = new MessageModel<ProcessEntity>();
            if (string.IsNullOrEmpty(model.ProcId) && string.IsNullOrWhiteSpace(model.ProcCode))
            {
                data.success = false;
                data.msg = "请传入工序ID或CODE";
                return data;
            }
            var whereExpression = Expressionable.Create<ProcessEntity>()
                .And(a => a.Deleted == 0)
                .AndIF(!string.IsNullOrEmpty(model.ProcId), a => a.ID == model.ProcId)
                .AndIF(!string.IsNullOrEmpty(model.ProcCode), a => a.ProcCode.Trim().ToUpper() == model.ProcCode.Trim().ToUpper())
                .ToExpression();

            var list = await _ProcessServices.FindList(whereExpression);
            if (list == null || list.Count == 0)
            {
                data.success = false;
                data.msg = "工序不存在";
                return data;
            }
            if (list.Count > 1)
            {
                data.success = false;
                data.msg = "工序重复";
                return data;
            }
            data.success = true;
            data.response = list[0];
            return data;


        }

        /// <summary>
        /// 分页
        /// </summary>
        /// <param name="model">页数</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<PageModel<ProcessEntity>>> GetPageList([FromBody] ProcessModel model)
        {

            var whereExpression = Expressionable.Create<ProcessEntity>().And(a => a.Deleted == 0)
                .AndIF(!string.IsNullOrEmpty(model.key), a => a.ProcCode.Contains(model.key) || a.ProcName.Contains(model.key))
                .AndIF(!string.IsNullOrEmpty(model.parentId), a => a.ParentId.Equals(model.parentId))

                .ToExpression();
            var response = await _ProcessServices.QueryPage(whereExpression, model.pageIndex, model.pageSize);
            return Success(response, "获取成功");
        }

        /***
        /// <summary>
        /// treegrid
        /// </summary>
        /// <param name="model">treegrid</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<TreeModel>>> GetTreeList([FromBody] ProcessModel model)
        {

            var whereExpression = Expressionable.Create<ProcessEntity>().And(a => a.Deleted == 0)
                .AndIF(!string.IsNullOrEmpty(model.key), a => a.ProcCode.Contains(model.key) || a.ProcName.Contains(model.key))
                .AndIF(!string.IsNullOrEmpty(model.proctype), a => a.ProcType.Equals(model.proctype))

                .ToExpression();

            var data = await _ProcessServices.FindList(whereExpression);

            List<TreeModel> treeList = new List<TreeModel>();
            foreach (ProcessEntity item in data)
            {
                ProcessTreeModel node = new ProcessTreeModel();
                node.id = item.ID;
                node.name = item.ProcName;
                node.value = item.ProcCode;
                node.showcheck = false;
                node.checkstate = 0;
                node.isexpand = true;
                node.parentId = item.ParentId == null?"0": item.ParentId;
                node.ProcCode = item.ProcCode;
                node.ProcName = item.ProcName;
                node.ProcType = item.ProcType;
                node.ParentId = item.ParentId;
                node.Description = item.Description;
                node.ID = item.ID;
                node.Deleted = item.Deleted;
                node.CreateDate = item.CreateDate;
                node.CreateUserId = item.CreateUserId;
                node.ModifyDate = item.ModifyDate;
                node.ModifyUserId = item.ModifyUserId;
                node.StandardTime = item.StandardTime;
                node.StandardUom = item.StandardUom;
                node.PrepareTime = item.PrepareTime;
                node.TransTime = item.TransTime;
                node.StandardPeronNum = item.StandardPeronNum;
                node.UnitCapacity = item.UnitCapacity;
                node.IsCreateOrder = node.IsCreateOrder;
                
                treeList.Add(node);
            }
            return Success(treeList.ToTree(), "获取成功");
        }
        **/

        /// <summary>
        /// 根据ID获取
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("{id}")]
        public async Task<MessageModel<ProcessEntity>> GetEntity(string id)
        {
            var response = await _ProcessServices.QueryById(id);
            return Success(response, "获取成功");

        }

        /// <summary>
        /// 工艺路线明细保存及更新
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] ProcessEntity request)
        {
            var data = new MessageModel<string>();

            if (string.IsNullOrEmpty(request.ID))
            {
                if (await _ProcessServices.isExist(request, false))
                {
                    return Failed("路线代码或名称已存在。");
                }
                data.success = await _ProcessServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _ProcessServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }

        }

        /// <summary>
        /// 工艺路线明细删除
        /// </summary>
        /// <param name="ids">id数组</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> Delete(string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _ProcessServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    public class ProcessModel : RequestPageModelBase
    {
        public string key { get; set; }

        public string ProcId { get; set; }

        public string ProcCode { get; set; }

        public string parentId { get; set; }

    }
}