using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;
using SEFA.DFM.Services;
using LKK.Lib.Core;
using SEFA.Base.Common.HttpContextUser;
using SEFA.Base.Common.HttpRestSharp;

namespace SEFA.DFMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [Authorize(Permissions.Name)]
    public class ProcessDataMappingController : BaseApiController
    {
        /// <summary>
        /// ProcessDataMapping
        /// </summary>
        private readonly IProcessDataMappingServices _processDataMappingServices;
        private readonly IMaterialProcessDataServices _materialProcessDataServices;

        public ProcessDataMappingController(IProcessDataMappingServices ProcessDataMappingServices, IMaterialProcessDataServices materialProcessDataServices)
        {
            _processDataMappingServices = ProcessDataMappingServices;
            _materialProcessDataServices = materialProcessDataServices;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<ProcessDataMappingEntity>>> GetList([FromBody] ProcessDataMappingRequestModel reqModel)
        {
            var data = await _processDataMappingServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<ProcessDataMappingEntity>>> GetPageList([FromBody] ProcessDataMappingRequestModel reqModel)
        {
            var data = await _processDataMappingServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<ProcessDataMappingEntity>> GetEntity(string id)
        {
            var data = await _processDataMappingServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] ProcessDataMappingEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _processDataMappingServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _processDataMappingServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _processDataMappingServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed( "删除失败");
            }
        }


        /// <summary>
        /// 根据ContextVersion获取工艺长文本
        /// </summary>
        /// <param name="contextVersionId"></param>
        /// <returns></returns>
        [HttpGet("{contextVersionId}")]
        public async Task<MessageModel<MaterialProcessDataEntity>> GetLastProcessDataByContextVersion (string contextVersionId) {
            var result = new MessageModel<MaterialProcessDataEntity>
            {
                msg = "获取失败！",
                success = true
            };

            var processDataMappings =  await _processDataMappingServices.FindList(a => a.VersionId == contextVersionId);
 
            if (processDataMappings == null || processDataMappings.Count == 0)
            {
                result.msg = "processDataMappings为空";
                return result;
            }
            

            var ids = processDataMappings.Select(x => x.ProcessData);
            var lastProcessDataList = await _materialProcessDataServices.FindList(x => x.Status == "2" && x.Type == 0 && ids.Contains(x.ID));
            if (lastProcessDataList == null)
            {
                result.msg = "lastProcessData为空";
                return result;
            }

            var lastProcessData = lastProcessDataList.OrderByDescending(x => x.TextVersion)?.FirstOrDefault();
            if (lastProcessData == null)
            {
                result.msg = "lastProcessData为空";
                return result;
            }
            try
            {
                lastProcessData.ProcessData = Enigma.Decrypt(lastProcessData.ProcessData, lastProcessData.Token)?.Replace("@@", "\n");
            }
            catch (Exception ex)
            {

            }
            
            result.response = lastProcessData;
            result.success = true;
            result.msg = "获取成功！";
            return result;
        }

    }
    //public class ProcessDataMappingRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}