using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;

namespace SEFA.DFMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [Authorize(Permissions.Name)]
    public class ProcessPropertyValueController : BaseApiController
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly IProcessPropertyValueServices _processPropertyValueServices;
    
        public ProcessPropertyValueController(IProcessPropertyValueServices ProcessPropertyValueServices)
        {
            _processPropertyValueServices = ProcessPropertyValueServices;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<ProcessPropertyValueEntity>>> GetList(string key = "")
        {
            Expression<Func<ProcessPropertyValueEntity, bool>> whereExpression = a => true;
            var data = await _processPropertyValueServices.FindList(whereExpression);
            return Success(data, "获取成功");

        }


        [HttpPost]
        public async Task<MessageModel<PageModel<ProcessPropertyValueEntity>>> GetPageList([FromBody] ProcessPropertyValueRequestModel reqModel)
        {
             
            Expression<Func<ProcessPropertyValueEntity, bool>> whereExpression = a => a.Deleted==0 && a.ProcessId.Equals(reqModel.processId);
            var data = await _processPropertyValueServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return Success(data, "获取成功");

        }

        [HttpGet("{id}")]
        public async Task<MessageModel<ProcessPropertyValueEntity>> GetEntity(string id)
        {
            var data = await _processPropertyValueServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] ProcessPropertyValueEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _processPropertyValueServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                } 
                else
                {
                    return Failed( "添加失败");
                }
            }
            else
            {
                data.success = await _processPropertyValueServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                } 
                else
                {
                    return Failed( "更新失败");
                }
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _processPropertyValueServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed( "删除失败");
            }
        }
    }
    public class ProcessPropertyValueRequestModel : RequestPageModelBase
    {
        public string processId { get; set; }
        public string key { get; set; }
    }
}