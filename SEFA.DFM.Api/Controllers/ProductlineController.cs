using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;
using SqlSugar;

namespace SEFA.DFMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class ProductlineController : BaseApiController
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly IProductlineServices _productlineServices;

        public ProductlineController(IProductlineServices ProductlineServices)
        {
            _productlineServices = ProductlineServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<ProductlineEntity>>> GetList([FromBody] List<RequestModels> req)
        {
            var whereExpression = Expressionable.Create<ProductlineEntity>().And(a => true).ToExpression();
            List<ProductlineEntity> list = new List<ProductlineEntity>();
            var data = await _productlineServices.FindList(whereExpression);
            if (req==null || req.Count() <= 0)
            {
                list = data;
            }
            else
            {
                foreach (var entity in data)
                {
                    if (req.Where(a => a.productLineName == entity.ProductlineName && entity.SegmentShortname == a.segmentShortName).Count() > 0)
                    {
                        list.Add(entity);
                    }
                }
            }
            return Success(list, "获取成功");

        }

        [HttpPost]
        public async Task<MessageModel<PageModel<ProductlineEntity>>> GetPageList([FromBody] ProductlineRequestModel reqModel)
        {

            Expression<Func<ProductlineEntity, bool>> whereExpression = a => true;
            var data = await _productlineServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return Success(data, "获取成功");

        }

        [HttpGet("{id}")]
        public async Task<MessageModel<ProductlineEntity>> GetEntity(string id)
        {
            var data = await _productlineServices.QueryById(id);
            return Success(data, "获取成功");
        }



    }
    public class ProductlineRequestModel : RequestPageModelBase
    {
        public string key { get; set; }
    }
    public class RequestModels
    { 
        public string productLineName { get; set; }
        public string segmentShortName { get; set; }
    }
}