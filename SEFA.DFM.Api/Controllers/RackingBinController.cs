using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;
using SqlSugar;

namespace SEFA.DFMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class RackingBinController : BaseApiController
    {
        /// <summary>
        /// 线边货架位
        /// </summary>
        private readonly IRackingBinServices _rackingBinServices;

        public RackingBinController(IRackingBinServices RackingBinServices)
        {
            _rackingBinServices = RackingBinServices;
        }

        /// <summary>
        /// 获取线边货架位信息
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<RackingBinEntity>>> GetList(string key = "")
        {
            var whereExpression = Expressionable.Create<RackingBinEntity>().And(a => a.Deleted == 0)
            .AndIF(!string.IsNullOrEmpty(key), a => a.RackingId.Contains(key))
          .ToExpression();
            var data = await _rackingBinServices.FindList(whereExpression);
            return Success(data, "获取成功");

        }
        /// <summary>
        /// 获取线边货架位信息（分页）
        /// </summary>
        /// <param name="page"></param>
        /// <param name="key"></param>
        /// <param name="intPageSize"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<PageModel<RackingBinEntity>>> GetPageList(int page = 1, string key = "", int intPageSize = 50)
        {

            var whereExpression = Expressionable.Create<RackingBinEntity>().And(a => a.Deleted == 0)
             .AndIF(!string.IsNullOrEmpty(key), a => a.RackingId.Contains(key))
           .ToExpression();
            var data = await _rackingBinServices.QueryPage(whereExpression, page, intPageSize);
            return Success(data, "获取成功");

        }
        /// <summary>
        /// 获取 BY id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("{id}")]
        public async Task<MessageModel<RackingBinEntity>> GetEntity(string id)
        {
            var data = await _rackingBinServices.QueryById(id);
            return Success(data, "获取成功");
        }
        /// <summary>
        /// 提交信息
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] RackingBinEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                if (string.IsNullOrEmpty(request.BinCode))
                {
                    return Failed("货架位代码不能为空！");
                }
                if (string.IsNullOrEmpty(request.BinName))
                {
                    return Failed("货架位名称不能为空！");
                }
                data.success = await _rackingBinServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _rackingBinServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _rackingBinServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
}