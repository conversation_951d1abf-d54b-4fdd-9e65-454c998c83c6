using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;
using SEFA.DFM.Services;
using SqlSugar;
using SEFA.DFM.Model.ViewModels;
using SEFA.Base.Common.HttpContextUser;
using Magicodes.ExporterAndImporter.Excel.AspNetCore;
using Magicodes.ExporterAndImporter.Excel;
using AutoMapper;
//using K4os.Compression.LZ4.Internal;

namespace SEFA.DFMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [Authorize(Permissions.Name)]
    public class MaterialGroupMappingController : BaseApiController
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly IMaterialGroupMappingServices _materialGroupMappingServices;
        private readonly IMaterialGroupServices _materialGroupServices;
        private readonly IMaterialServices _materialServices;
        private readonly IUser _user;
        private readonly IMapper _mapper;

        public MaterialGroupMappingController(IMaterialGroupMappingServices MaterialGroupMappingServices, IMaterialGroupServices materialGroupServices, IMaterialServices materialServices, IUser user,IMapper mapper)
        {
            _materialGroupMappingServices = MaterialGroupMappingServices;
            _materialGroupServices = materialGroupServices;
            _materialServices = materialServices;
            _user = user;
            _mapper = mapper;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<RecipeMappingModel>>> GetList(string key = "")
        {
              var data = await _materialGroupMappingServices.QueryTwoTable<MaterialGroupMappingEntity, MaterialEntity, RecipeMappingModel>((mgm, m) => 
              new object[] {JoinType.Inner, mgm.MaterialId == m.ID},
                 (mgm, m) => new RecipeMappingModel
                 {
                     ID = mgm.ID,
                     MaterialGroupId = mgm.MaterialGroupId,
                     MaterialId = m.ID,
                     MaterialCode = m.Code,
                     MaterialName  =m.NAME
                 }, (mgm, m) => m.NAME.Contains(key) || m.Code.Contains(key));
                return Success(data, "获取成功");

        }

        [HttpPost]
        public async Task<MessageModel<PageModel<MaterialModel>>> GetPageList([FromBody] MaterialGroupRequestModel reqModel)
        {
            var data = await _materialGroupMappingServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<MaterialGroupMappingEntity>> GetEntity(string id)
        {
            var data = await _materialGroupMappingServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] MaterialGroupMappingEntity request)
        {
            var data = new MessageModel<string>();
            List< MaterialGroupMappingEntity > entities = new List< MaterialGroupMappingEntity >(); 

            if (string.IsNullOrEmpty(request.MaterialGroupId))
            {
                return Failed($"物料组Id不可为空");
            }
            if (string.IsNullOrEmpty(request.MaterialId))
            {
                return Failed($"物料Id不可为空");
            }
            List<string> materialIds = request.MaterialId.Split(',').ToList();

            var materialGroupEntity = await _materialGroupServices.FindEntity(request.MaterialGroupId);
            if (materialGroupEntity == null)
            {
                return Failed($"物料组不存在");
            }

            var materialList = await _materialServices.FindList(a => materialIds.Contains(a.ID));
            if (materialList.Count <=0 )
            {
                return Failed($"物料不存在");
            }

            var existList = await _materialGroupMappingServices.FindList(a => a.MaterialGroupId == request.MaterialGroupId);
           

            foreach ( var material in materialList)
            {
                if (existList.Exists(a => a.MaterialId == material.ID))
                    continue;

                MaterialGroupMappingEntity entity = new MaterialGroupMappingEntity();
                entity.MaterialId = material.ID;
                entity.MaterialGroupId = materialGroupEntity.ID;
                entity.Deleted = 0;
                entity.CreateCustomGuid(_user.Name);
                entities.Add(entity);
            }

            if (entities.Count > 0 )
            {
                data.success = await _materialGroupMappingServices.Add(entities) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                } 
                else
                {
                    return Failed( "添加失败");
                }
            }
            return Success("", "添加成功");
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _materialGroupMappingServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed( "删除失败");
            }
        }

        #region 数据导入、导出
        /// <summary>
        /// 数据导入
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Consumes("multipart/form-data")]
        [HttpPost]
        public async Task<MessageModel<string>> ImportData ([FromForm] FileImportDto input) {
            var result = await _materialGroupMappingServices.ImportData(input);
            if (result.Succeed)
                return Success(result.Data, "导入成功");
            else
                return Failed("导入失败：" + result.Error.Text);
        }

        /// <summary>
        /// 数据导出
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<ActionResult> ExportData ([FromBody] MaterialGroupRequestModel reqModel) {
            ExcelExporter exporter = new ExcelExporter();
            var query = await _materialGroupMappingServices.GetList(reqModel);

            var data = _mapper.Map<List<MaterialGroupExcelDto>>(query);

            var result = await exporter.ExportAsByteArray<MaterialGroupExcelDto>(data);
            var fs = new MemoryStream(result);
            return new XlsxFileResult(stream: fs, fileDownloadName: $"物料组数据-{DateTime.Now.ToString("yyyy-MM-dd HH-mm-ss")}");
        }

        /// <summary>
        /// 下载模板
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> DownLoadTemplate () {
            ExcelImporter Importer = new ExcelImporter();
            var result = await Importer.GenerateTemplateBytes<MaterialGroupExcelDto>();
            var fs = new MemoryStream(result);
            return new XlsxFileResult(stream: fs, fileDownloadName: "导入模板");
        }

        #endregion
    }
    public class MaterialGroupMappingRequestModel : RequestPageModelBase
    {
        public string key { get; set; }
        public string MaterialGroupId { get; set; }
        public string MaterialId { get; set; }
    }
}