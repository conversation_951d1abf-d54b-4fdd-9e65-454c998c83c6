using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;
using SEFA.DFM.Services;
using SEFA.Base.Common.HttpContextUser;

namespace SEFA.DFMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [Authorize(Permissions.Name)]
    public class ParameterConfigController : BaseApiController
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly IParameterConfigServices _parameterConfigServices;
        private readonly IParameterDefinitionServices _parameterDefinitionServices;
        private readonly IRecipeContextVersionServices _recipeContextVersionServices;
        private readonly IUser _user;
        public ParameterConfigController(IParameterConfigServices ParameterConfigServices, 
            IParameterDefinitionServices parameterDefinitionServices,
            IRecipeContextVersionServices recipeContextVersionServices,
            IUser user)
        {
            _parameterConfigServices = ParameterConfigServices;
            _parameterDefinitionServices = parameterDefinitionServices;
            _recipeContextVersionServices = recipeContextVersionServices;
            _user = user;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<ParameterConfigEntity>>> GetList(string key = "")
        {
            Expression<Func<ParameterConfigEntity, bool>> whereExpression = a => true;
            var data = await _parameterConfigServices.FindList(whereExpression);
            return Success(data, "获取成功");

        }

        [HttpPost]
        public async Task<MessageModel<PageModel<ParameterConfigEntity>>> GetPageList([FromBody] ParameterConfigRequestModel reqModel)
        {
             
            Expression<Func<ParameterConfigEntity, bool>> whereExpression = a => true;
            var data = await _parameterConfigServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return Success(data, "获取成功");

        }

        [HttpGet("{id}")]
        public async Task<MessageModel<ParameterConfigEntity>> GetEntity(string id)
        {
            var data = await _parameterConfigServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] ParameterConfigEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                return Failed("ID为空");
            }

            var entity = await _parameterConfigServices.FindEntity(request.ID);
            if (entity == null)
            {
                return Failed("数据不存在");
            }

            var recipeContextVersion = await _recipeContextVersionServices.FindEntity(request.RecipeContextVersionId);

            if (recipeContextVersion == null)
            {
                return Failed("数据不存在");
            }
            if (recipeContextVersion.Status != RecipeDataType.CheckStatus.CheckOut)
            {
                return Failed("当前版本未签出,无法保存修改");
            }


            entity.MinValue = request.MinValue;
            entity.MaxValue = request.MaxValue;
            entity.Target = request.Target;
            entity.TargetText = request.TargetText;
            entity.DefaultValue = request.DefaultValue;
            request.Modify(entity.ID, _user.Name);

            data.success = await _parameterConfigServices.Update(entity);
            if (data.success)
            {
                return Success("", "更新成功");
            }
            else
            {
                return Failed("更新失败");
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _parameterConfigServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed( "删除失败");
            }
        }
    }
    public class ParameterConfigRequestModel : RequestPageModelBase
    {
        public string key { get; set; }
    }
}