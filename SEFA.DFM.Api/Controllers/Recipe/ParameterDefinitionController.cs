using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;
using SEFA.DFM.Services;
using SEFA.DFM.Model.ViewModels;
using SEFA.Base.Common.HttpContextUser;

namespace SEFA.DFMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [Authorize(Permissions.Name)]
    public class ParameterDefinitionController : BaseApiController
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly IParameterDefinitionServices _parameterDefinitionServices;
        private readonly IParameterGroupServices _parameterGroupServices;
        private readonly IParameterConfigServices _parameterConfigServices;
        private readonly IUser _user;

        public ParameterDefinitionController(IParameterDefinitionServices ParameterDefinitionServices, IParameterGroupServices parameterGroupServices,
            IParameterConfigServices parameterConfigServices, IUser user)
        {
            _parameterDefinitionServices = ParameterDefinitionServices;
            _parameterGroupServices = parameterGroupServices;
            _parameterConfigServices = parameterConfigServices;
            _user = user;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<ParameterDefinitionEntity>>> GetList(string key = "")
        {
            Expression<Func<ParameterDefinitionEntity, bool>> whereExpression = a => true;
            var data = await _parameterDefinitionServices.FindList(whereExpression);
            return Success(data, "获取成功");

        }

        [HttpPost]
        public async Task<MessageModel<PageModel<ParameterDefinitionEntity>>> GetPageList([FromBody] ParameterDefinitionRequestModel reqModel)
        {
             
            Expression<Func<ParameterDefinitionEntity, bool>> whereExpression = a => true;
            var data = await _parameterDefinitionServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return Success(data, "获取成功");

        }

        [HttpGet("{id}")]
        public async Task<MessageModel<ParameterDefinitionEntity>> GetEntity(string id)
        {
            var data = await _parameterDefinitionServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> AddParameter ([FromBody] RecipeParameterModel request)
        {
           request.UserId = _user.Name;
           return await _parameterDefinitionServices.AddParameter(request);
        }

        [HttpPost]
        public async Task<MessageModel<string>> EditParameter ([FromBody] RecipeParameterModel request) {
            request.UserId = _user.Name;
            return await _parameterDefinitionServices.EditParameter(request);
        }

        [HttpPost]
        public async Task<MessageModel<string>> DeleteParameter ([FromBody] RecipeParameterModel request) {
            return await _parameterDefinitionServices.DeleteParameter(request);
        }


    }
    public class ParameterDefinitionRequestModel : RequestPageModelBase
    {
        public string key { get; set; }
    }
}