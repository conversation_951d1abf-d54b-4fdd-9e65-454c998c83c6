using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;
using SEFA.DFM.Services;
using SEFA.Base.Common.HttpContextUser;

namespace SEFA.DFMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [Authorize(Permissions.Name)]
    public class RecipeContextVersionController : BaseApiController
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly IRecipeContextVersionServices _recipeContextVersionServices;
        private readonly IUser _user;

        public RecipeContextVersionController(IRecipeContextVersionServices RecipeContextVersionServices, IUser user)
        {
            _recipeContextVersionServices = RecipeContextVersionServices;
            _user = user;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<RecipeContextVersionEntity>>> GetList(string key = "")
        {
            Expression<Func<RecipeContextVersionEntity, bool>> whereExpression = a => true;
            var data = await _recipeContextVersionServices.FindList(whereExpression);
            return Success(data, "获取成功");

        }

        [HttpPost]
        public async Task<MessageModel<PageModel<RecipeContextVersionEntity>>> GetPageList([FromBody] RecipeContextVersionRequestModel reqModel)
        {
             
            Expression<Func<RecipeContextVersionEntity, bool>> whereExpression = a => true;
            var data = await _recipeContextVersionServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return Success(data, "获取成功");

        }

        [HttpGet("{id}")]
        public async Task<MessageModel<RecipeContextVersionEntity>> GetEntity(string id)
        {
            var data = await _recipeContextVersionServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> EditRecipeContextVersion ([FromBody] RecipeContextVersionEntity request) {
            var data = new MessageModel<string>();

            var entity = await _recipeContextVersionServices.FindEntity(request.ID);
            if (entity == null)
            {
                return Failed("数据不存在");
            }
            entity.Comments = request.Comments;
            entity.EffectiveStartTime = request.EffectiveStartTime;
            entity.EffectiveEndTime = request.EffectiveEndTime;
            entity.Modify(entity.ID, _user.Name);

            data.success = await _recipeContextVersionServices.Update(entity);
            if (data.success)
            {
                return Success("", "更新成功");
            }
            else
            {
                return Failed("更新失败");
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _recipeContextVersionServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed( "删除失败");
            }
        }
    }
    public class RecipeContextVersionRequestModel : RequestPageModelBase
    {
        public string key { get; set; }
    }
}