using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;

namespace SEFA.DFMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [Authorize(Permissions.Name)]
    public class RecipeExeutionController : BaseApiController
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly IRecipeExeutionServices _recipeExeutionServices;
    
        public RecipeExeutionController(IRecipeExeutionServices RecipeExeutionServices)
        {
            _recipeExeutionServices = RecipeExeutionServices;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<RecipeExeutionEntity>>> GetList(string key = "")
        {
            Expression<Func<RecipeExeutionEntity, bool>> whereExpression = a => true;
            var data = await _recipeExeutionServices.FindList(whereExpression);
            return Success(data, "获取成功");

        }

        [HttpPost]
        public async Task<MessageModel<PageModel<RecipeExeutionEntity>>> GetPageList([FromBody] RecipeExeutionRequestModel reqModel)
        {
             
            Expression<Func<RecipeExeutionEntity, bool>> whereExpression = a => true;
            var data = await _recipeExeutionServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return Success(data, "获取成功");

        }

        [HttpGet("{id}")]
        public async Task<MessageModel<RecipeExeutionEntity>> GetEntity(string id)
        {
            var data = await _recipeExeutionServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] RecipeExeutionEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _recipeExeutionServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                } 
                else
                {
                    return Failed( "添加失败");
                }
            }
            else
            {
                data.success = await _recipeExeutionServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                } 
                else
                {
                    return Failed( "更新失败");
                }
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _recipeExeutionServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed( "删除失败");
            }
        }
    }
    public class RecipeExeutionRequestModel : RequestPageModelBase
    {
        public string key { get; set; }
    }
}