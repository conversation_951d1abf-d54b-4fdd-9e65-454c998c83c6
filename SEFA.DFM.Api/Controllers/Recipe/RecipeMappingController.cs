using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;
using SEFA.DFM.Services;
using SqlSugar;
using SEFA.Base.Common.Helper;
using SEFA.DFM.Model.ViewModels;
using SEFA.Base.Common.HttpContextUser;
using SEFA.Base.Common.WebApiClients.HttpApis;

namespace SEFA.DFMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [Authorize(Permissions.Name)]
    public class RecipeMappingController : BaseApiController
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly IRecipeMappingServices _recipeMappingServices;
        private readonly IRecipeContextServices _recipeContextServices;
        private readonly IMaterialVersionServices _materialVersionServices;
        private readonly IUser _user;

        public RecipeMappingController(IRecipeMappingServices RecipeMappingServices, IRecipeContextServices recipeContextServices, IMaterialVersionServices materialVersionServices,IUser user)
        {
            _recipeMappingServices = RecipeMappingServices;
            _recipeContextServices = recipeContextServices;
            _materialVersionServices = materialVersionServices;
            _user = user;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<RecipeMappingModel>>> GetList(RecipeMappingModel recipeMappingModel)
        {
            var data = await _recipeMappingServices.GetList(recipeMappingModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<RecipeMappingEntity>>> GetPageList([FromBody] RecipeMappingRequestModel reqModel)
        {
             
            Expression<Func<RecipeMappingEntity, bool>> whereExpression = a => true;
            var data = await _recipeMappingServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return Success(data, "获取成功");

        }

        [HttpGet("{id}")]
        public async Task<MessageModel<RecipeMappingEntity>> GetEntity(string id)
        {
            var data = await _recipeMappingServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] RecipeMappingEntity request)
        {
            var data = new MessageModel<string>();

            if (string.IsNullOrEmpty(request.RecipeId))
            {
                return Failed($"RecipeId不可为空");
            }

            var mappingList =await _recipeMappingServices.FindList(a =>a.RecipeId == request.RecipeId && a.Deleted == 0);

            if (!string.IsNullOrEmpty(request.MaterialGroupId))
            {
                var groupIdList = request.MaterialGroupId.Split(",");

                foreach (var groupId in groupIdList)
                {
                    if(mappingList.Select(a => a.MaterialGroupId).Contains(groupId))
                    {
                        continue;
                    }

                    RecipeMappingEntity entity = new RecipeMappingEntity();
                    entity.RecipeId= request.RecipeId;
                    entity.MaterialGroupId= groupId;
                    entity.Deleted = 0;
                    entity.CreateCustomGuid(_user.Name);
                    data.success = await _recipeMappingServices.Add(entity) > 0;
                }
            }
            else if (!string.IsNullOrEmpty(request.MaterialId))
            {
                var materialIdList = request.MaterialId.Split(",");

                foreach (var materialId in materialIdList)
                {
                    if (mappingList.Select(a => a.MaterialId).Contains(materialId))
                    {
                        continue;
                    }

                    RecipeMappingEntity entity = new RecipeMappingEntity();
                    entity.RecipeId = request.RecipeId;
                    entity.MaterialId = materialId;
                    entity.Deleted = 0;
                    entity.CreateCustomGuid(_user.Name);
                    data.success = await _recipeMappingServices.Add(entity) > 0;
                }
            }
            else if (!string.IsNullOrEmpty(request.MaterialVersionId))
            {
                var materialVersionIdList = request.MaterialVersionId.Split(",");

                var mvList = await _materialVersionServices.FindList(a => materialVersionIdList.Contains(a.ID));

                foreach (var materialVersionId in materialVersionIdList)
                {
                    if (mappingList.Select(a => a.MaterialVersionId).Contains(materialVersionId))
                    {
                        continue;
                    }
                    var mvEntity = mvList.Where(a => a.ID == materialVersionId).FirstOrDefault();
                    if(mvEntity == null)
                    {
                        continue;
                    }

                    RecipeMappingEntity entity = new RecipeMappingEntity();
                    entity.RecipeId = request.RecipeId;
                    entity.MaterialVersionId = materialVersionId;
                    entity.MaterialId = mvEntity.MaterialId;
                    entity.Deleted = 0;
                    entity.CreateCustomGuid(_user.Name);
                    data.success = await _recipeMappingServices.Add(entity) > 0;
                }
            }
            else
            {
                return Failed("未传入物料参数");
            }

            return Success("", "添加成功");
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] RecipeMappingEntity request)
        {
            var data = new MessageModel<string>();

            var entity = await _recipeMappingServices.FindEntity(request.ID);
            if(entity == null)
            {
                return Failed("Id不存在");
            }

            var  where = Expressionable.Create<RecipeContextEntity>().And(a => a.Status == "1")
                .AndIF(!string.IsNullOrEmpty(entity.MaterialGroupId), a => a.MaterialGroupId == entity.MaterialGroupId)
                .AndIF(!string.IsNullOrEmpty(entity.MaterialVersionId), a => a.MaterialVersionId == entity.MaterialVersionId)
                .AndIF(!string.IsNullOrEmpty(entity.MaterialId), a => a.MaterialId == entity.MaterialId)
                .ToExpression();

            var contextList = await _recipeContextServices.FindList(where);

           
            data.success = await _recipeMappingServices.Delete(entity);

            if(contextList.Count >0)
            {
                contextList.ForEach(a => a.Status = "0");
                data.success = await _recipeContextServices.Update(contextList);
            }
            

            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed( "删除失败");
            }
        }
    }
    public class RecipeMappingRequestModel : RequestPageModelBase
    {
        public string key { get; set; }
    }
}