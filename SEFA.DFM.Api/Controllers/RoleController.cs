using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using SEFA.Base;
using SEFA.Base.Common.HttpContextUser;
using SEFA.Base.Model;
using SEFA.DFM.Controllers;
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using System.Linq.Expressions;

namespace SEFA.DFMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class RoleController : BaseApiController
    {
        /// <summary>
        ///
        /// </summary>
        private readonly IRoleServices _roleServices;

        private readonly IUser _user;
        private readonly IAuditLogServices _auditLogServices;

        public RoleController(IRoleServices RoleServices, IUser user, IAuditLogServices auditLogServices)
        {
            _roleServices = RoleServices;
            _user = user;
            _auditLogServices = auditLogServices;
        }

        /// <summary>
        /// 获取角色用户树
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<TreeModel>>> GetUserTree([FromBody] RoleRequestModel reqModel)
        {
            var data = await _roleServices.GetUserRoleTree(reqModel.Name, reqModel.MenuType);

            return Success(data.ToTree(), "获取成功");
        }

        /// <summary>
        /// 获取角色菜单树
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<TreeModel>>> GetUserPerssionTree([FromBody] RoleRequestModel reqModel, string menuCategory = "Web")
        {
            var data = await _roleServices.GetUserPerssionTree(reqModel.roleid, menuCategory);

            return Success(data.ToTree(), "获取成功");
        }

        /// <summary>
        /// 获取
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<RoleEntity>>> GetList([FromBody] RoleRequestModel reqModel)
        {
            RoleEntity role = new RoleEntity();
            role.Name = reqModel.Name;

            var data = await _roleServices.GetRoleList(role);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<RoleEntity>>> GetPageList([FromBody] RoleRequestModel reqModel)
        {
            Expression<Func<RoleEntity, bool>> whereExpression = a => true;
            var data = await _roleServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<RoleEntity>> GetEntity(string id)
        {
            var data = await _roleServices.QueryById(id);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 提交数据
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] RoleEntity request)
        {
            var data = new MessageModel<string>();
            AuditLogEntity auditLog = _auditLogServices.GetMethodInfo();
            auditLog.MethodName = "Role/SaveForm";
            auditLog.ServiceName = "DFM";
            auditLog.Parameters = JsonConvert.SerializeObject(request);
            //auditLog.Parameters = JsonConvert.SerializeObject(request).Length > 250 ? JsonConvert.SerializeObject(request).Substring(0, 250) : JsonConvert.SerializeObject(request);
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _roleServices.Add(request) > 0;
                auditLog.MethodRemark = $"新增角色[{request.Name}]";
                auditLog.ReturnValue = data.success ? "新增成功" : "新增失败";
                await _auditLogServices.Add(auditLog);
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _roleServices.Update(request);
                auditLog.MethodRemark = $"修改角色[{request.Name}]";
                auditLog.ReturnValue = data.success ? "更新成功" : "更新失败";
                await _auditLogServices.Add(auditLog);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            var roleName = "";
            var roles = await _roleServices.QueryByIDs(ids);
            if (roles.Count > 0)
            {
                roleName = string.Join(",", roles.Select(a => a.Name).ToArray());
            }
            data.success = await _roleServices.DeleteByIds(ids);
            AuditLogEntity auditLog = _auditLogServices.GetMethodInfo();
            auditLog.MethodName = "Role/Delete";
            auditLog.ServiceName = "DFM";
            auditLog.Parameters = JsonConvert.SerializeObject(ids);
            //auditLog.Parameters = JsonConvert.SerializeObject(ids).Length > 250 ? JsonConvert.SerializeObject(ids).Substring(0, 250) : JsonConvert.SerializeObject(ids);
            auditLog.ReturnValue = data.success ? "删除成功" : "删除失败";
            auditLog.MethodRemark = $"删除角色[{roleName}]";
            await _auditLogServices.Add(auditLog);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }

        /// <summary>
        /// 根据 LoginName 获取菜单信息
        /// </summary>
        /// <param name="reqModel"></param>
        /// <param name="menuCategory">Web：Web端；App：移动端</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<MenuTreeModel>>> GetUserRolePerssionTree([FromBody] RoleRequestModel reqModel, string menuCategory = "Web")
        {
            var data = await _roleServices.GetUserRolePerssionTree(_user.Name, menuCategory);

            return Success(data, "获取成功");
        }
    }

    public class RoleRequestModel : RequestPageModelBase
    {
        public string key { get; set; }
        public string Name { get; set; }
        public string MenuType { get; set; }
        public string roleid { get; set; }
    }
}