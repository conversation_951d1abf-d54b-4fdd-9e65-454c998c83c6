using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;

namespace SEFA.DFMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [Authorize(Permissions.Name)]
     public class RoutingAdditionController : BaseApiController
    {
            /// <summary>
            /// 
            /// </summary>
            private readonly IRoutingAdditionServices _routingAdditionServices;
    
            public RoutingAdditionController(IRoutingAdditionServices RoutingAdditionServices)
            {
                _routingAdditionServices = RoutingAdditionServices;
            }
    
            [HttpPost]
            public async Task<MessageModel<List<RoutingAdditionEntity>>> GetList(string key = "")
            {
                Expression<Func<RoutingAdditionEntity, bool>> whereExpression = a => true;
                var data = await _routingAdditionServices.FindList(whereExpression);
                return Success(data, "获取成功");

            }

            [HttpPost]
            public async Task<MessageModel<PageModel<RoutingAdditionEntity>>> GetPageList(int page = 1, string key = "",int intPageSize = 50)
            {
             
                Expression<Func<RoutingAdditionEntity, bool>> whereExpression = a => true;
                var data = await _routingAdditionServices.QueryPage(whereExpression, page, intPageSize);
                return Success(data, "获取成功");

            }

            [HttpGet("{id}")]
            public async Task<MessageModel<RoutingAdditionEntity>> GetEntity(string id)
            {
                var data = await _routingAdditionServices.QueryById(id);
                return Success(data, "获取成功");
            }

            [HttpPost]
            public async Task<MessageModel<string>> SaveForm([FromBody] RoutingAdditionEntity request)
            {
                var data = new MessageModel<string>();
                if (string.IsNullOrEmpty(request.ID))
                {
                    data.success = await _routingAdditionServices.Add(request) > 0;
                    if (data.success)
                    {
                        return Success("", "添加成功");
                    } 
                    else
                    {
                        return Failed( "添加失败");
                    }
                }
                else
                {
                    data.success = await _routingAdditionServices.Update(request);
                    if (data.success)
                    {
                        return Success("", "更新成功");
                    } 
                    else
                    {
                        return Failed( "更新失败");
                    }
                }
            }


            [HttpPost]
            public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
            {
                var data = new MessageModel<string>();
                data.success = await _routingAdditionServices.DeleteByIds(ids);
                if (data.success)
                {
                    return Success("", "删除成功");
                }
                else
                {
                    return Failed( "删除失败");
                }
            }
    }
}