using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.DFM.Controllers;
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using SqlSugar;
using System.Linq.Expressions;

namespace SEFA.DFMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class RoutingProductController : BaseApiController
    {   /// <summary>
        /// 
        /// </summary>
        private readonly IRoutingProductServices _routingProductServices;

        private readonly IMaterialServices _materialServices;

        public RoutingProductController(IRoutingProductServices RoutingProductServices, IMaterialServices MaterialServices)
        {
            _routingProductServices = RoutingProductServices;
            _materialServices = MaterialServices;
        }
        /// <summary>
        /// 获取全部
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<MessageModel<List<RoutingProductEntity>>> GetList(string key = "")
        {
            if (string.IsNullOrEmpty(key) || string.IsNullOrWhiteSpace(key))
            {
                key = "";
            }

            Expression<Func<RoutingProductEntity, bool>> whereExpression = a => true;

            var response = await _routingProductServices.FindList(whereExpression);
            return Success(response, "获取成功");

        }
        /// <summary>
        /// 根据工艺路线ID获取产品分页
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<PageModel<RoutingProductEntity>>> GetPageList([FromBody] RoutingProductModel request)
        {
            var whereExpression = Expressionable.Create<RoutingProductEntity>().And(a => a.Deleted == 0)
                .And(a => a.RoutingId.Equals(request.RoutingId))
                .ToExpression();
            var response = await _routingProductServices.QueryPage(whereExpression, request.pageIndex, request.pageSize);
            return Success(response, "获取成功");
        }

        /// <summary>
        /// 根据主键获取
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("{id}")]
        public async Task<MessageModel<RoutingProductEntity>> GetEntity(string id)
        {
            var response = await _routingProductServices.QueryById(id);
            return Success(response, "获取成功");
        }

        /// <summary>
        /// 提交 审核 作废
        /// </summary>
        /// <param name="id"></param>
        /// <param name="status">PendingSubmission待提交  PendingApproval待审核  Approved已通过   Unapproved未通过  Void作废</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> ChangeStatus(string id, string status)
        {
            var msg = new MessageModel<string>();
            var data = await _routingProductServices.QueryById(id);

            if (AuditStatus.PendingSubmission.ToString().Equals(data.Status))
            {
                if (!AuditStatus.PendingApproval.ToString().Equals(status))
                {
                    return Failed("未提交");
                }
            }
            else if (AuditStatus.PendingApproval.ToString().Equals(data.Status))
            {
                if ((!AuditStatus.Approved.ToString().Equals(status)) || !AuditStatus.Unapproved.ToString().Equals(status))
                {
                    return Failed("待审核");
                }
            }
            else if (AuditStatus.Approved.ToString().Equals(data.Status))
            {
                return Failed("已核准");
            }
            data.Status = status;
            msg.success = await _routingProductServices.Update(data);
            if (msg.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }
        /// <summary>
        /// 产品保存和更新
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] RoutingProductEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                if (await _routingProductServices.isExist(request, false))
                {
                    return Failed("路线代码已存在。");
                }
                data.success = await _routingProductServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "新增成功");
                }
                else
                {
                    return Failed("新增失败");
                }
            }
            else
            {
                if (await _routingProductServices.isExist(request, true))
                {
                    return Failed("路线代码已存在。");
                }
                data.success = await _routingProductServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }
        /// <summary>
        /// 根据成品料号获取工艺路线
        /// </summary>
        /// <param name="MaterialCode">物料号</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<RoutingProductEntity>>> GetListByMaterialCode(string MaterialCode)
        {
            if (string.IsNullOrEmpty(MaterialCode))
            {
                return null;
            }
            Expression<Func<RoutingProductEntity, bool>> whereExpression = a => a.MaterialCode.Equals(MaterialCode);

            var data = await _routingProductServices.FindList(whereExpression);

            return Success(data, "成功");


        }


        /// <summary>
        /// 获取资源工艺路线树
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<MessageModel<List<TreeModel>>> GetClassifyTree()
        {
            var whereExpression1 = Expressionable.Create<MaterialEntity>().And(a => a.Deleted == 0).And(a => a.Type == "CPWL").ToExpression();
            List<MaterialEntity> list = await _materialServices.FindList(whereExpression1);
            var whereExpression = Expressionable.Create<RoutingProductEntity>().And(a => a.Deleted == 0).ToExpression();
            List<RoutingProductEntity> classifyList = await _routingProductServices.FindList(whereExpression);
            List<TreeModel> treeList = new List<TreeModel>();
            foreach (MaterialEntity item in list)
            {
                RoutingProductTreeMode node = new RoutingProductTreeMode();
                node.id = item.Code;
                node.name = item.NAME;
                node.value = item.Code;
                node.showcheck = false;
                node.checkstate = 0;
                node.isexpand = true;
                node.parentId = "0";
                node.children = new List<TreeModel>();
                treeList.Add(node);
            }


            List<RoutingProductEntity> novlist = classifyList.GroupBy(a => new { a.MainMaterialCode, a.MaterialCode }).Select(a => a.FirstOrDefault()).ToList();
            foreach (TreeModel one in treeList)
            {
                foreach (RoutingProductEntity item in novlist)
                {
                    if (one.id.Equals(item.MainMaterialCode))
                    {
                        RoutingProductTreeMode node = new RoutingProductTreeMode();
                        node.id = item.MaterialCode;
                        node.name = item.MaterialName;
                        node.value = item.MaterialCode;
                        node.showcheck = false;
                        node.checkstate = 0;
                        node.isexpand = true;
                        node.parentId = item.MainMaterialCode;
                        node.Resid = item.ID;
                        node.RoutingId = item.RoutingId;
                        node.Version = item.Version;
                        node.ProduceType = item.ProduceType;
                        node.MaterialCode = item.MaterialCode;
                        node.BomVersion = item.BomVersion;
                        node.BomRatio = item.BomRatio;
                        node.remark = item.Remark;

                        one.children.Add(node);
                        one.hasChildren = true;
                    }
                }
            }


            List<RoutingProductEntity> novlist1 = classifyList.GroupBy(a => new { a.MainMaterialCode, a.MaterialCode, a.RoutingId }).Select(a => a.FirstOrDefault()).ToList();
            foreach (TreeModel one in treeList)
            {
                string maincode = one.id;
                foreach (TreeModel two in one.children)
                {
                    foreach (RoutingProductEntity item in novlist1)
                    {
                        if (maincode.Equals(item.MainMaterialCode) && two.id.Equals(item.MaterialCode))
                        {
                            RoutingProductTreeMode node = new RoutingProductTreeMode();
                            node.id = item.RoutingId;
                            node.name = item.RoutingName;
                            node.value = item.RoutingCode;
                            node.showcheck = false;
                            node.checkstate = 0;
                            node.isexpand = true;
                            node.parentId = item.MaterialId;
                            node.Resid = item.ID;
                            node.RoutingId = item.RoutingId;
                            node.Version = item.Version;
                            node.ProduceType = item.ProduceType;
                            node.MaterialCode = item.MaterialCode;
                            node.BomVersion = item.BomVersion;
                            node.BomRatio = item.BomRatio;
                            node.remark = item.Remark;

                            if (two.children == null)
                            {
                                two.children = new List<TreeModel>();
                            }
                            two.children.Add(node);
                            two.hasChildren = true;
                        }

                    }
                }
            }

            var a = treeList.OrderByDescending(a => a.value).ToList();
            foreach (TreeModel one in a)
            {
                string maincode = one.id;
                foreach (TreeModel two in one.children)
                {
                    string mcode = two.id;
                    if (two.children != null)
                        foreach (TreeModel three in two.children)
                        {
                            foreach (RoutingProductEntity item in classifyList)
                            {
                                if (maincode.Equals(item.MainMaterialCode) && mcode.Equals(item.MaterialCode) && three.id.Equals(item.RoutingId))
                                {
                                    RoutingProductTreeMode node = new RoutingProductTreeMode();
                                    node.id = item.ID;
                                    node.name = item.Version;// +item.RoutingName ;
                                    node.value = item.Version;// item.RoutingCode;
                                    node.showcheck = false;
                                    node.checkstate = 0;
                                    node.isexpand = true;
                                    node.parentId = item.RoutingId;
                                    node.Resid = item.ID;
                                    node.RoutingId = item.RoutingId;
                                    node.Version = item.Version;
                                    node.ProduceType = item.ProduceType;
                                    node.MaterialCode = item.MaterialCode;
                                    node.BomVersion = item.BomVersion;
                                    node.BomRatio = item.BomRatio;
                                    node.remark = item.Remark;
                                    if (three.children == null)
                                    {
                                        three.children = new List<TreeModel>();
                                    }
                                    three.children.Add(node);
                                    three.hasChildren = true;
                                }
                            }
                        }
                }
            }

            return Success(a, "获取成功");
        }
        [HttpPost]
        public async Task<MessageModel<string>> test()
        {
            HttpClient _httpClient = new HttpClient();
            HttpContent content = new StringContent("{ID:'123'}");
            content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/json");

            HttpResponseMessage result = await _httpClient.PostAsync("http://localhost:9293/api/RoutingProduct/SaveForm", content);
            result.EnsureSuccessStatusCode();
            string resp = await result.Content.ReadAsStringAsync();

            return Failed("删除失败");
        }
        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> Delete(string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _routingProductServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    public class RoutingProductTreeMode : TreeModel
    {
        public string Resid { get; set; }
        public string RoutingId { get; set; }
        public string Version { get; set; }
        public string BomVersion { get; set; }
        public string ProduceType { get; set; }

        public string MaterialCode { get; set; }

        public decimal? BomRatio { get; set; }
    }
    public class RoutingProductModel : RequestPageModelBase
    {
        public string RoutingId { get; set; }

    }
}