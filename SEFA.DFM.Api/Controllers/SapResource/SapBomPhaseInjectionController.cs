using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;
using SEFA.DFM.Model.ViewModels;
using SqlSugar;
using SEFA.Base.Common.HttpContextUser;
using SEFA.Base.Common.WebApiClients.HttpApis;
using static SEFA.DFM.Model.Models.SapDataType;
using Microsoft.AspNetCore.DataProtection.KeyManagement;

namespace SEFA.DFMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [Authorize(Permissions.Name)]
    public class SapBomPhaseInjectionController : BaseApiController
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly ISapBomPhaseInjectionServices _sapBomPhaseInjectionServices;
        private readonly IMaterialGroupServices _materialGroupServices;
        private readonly IMaterialServices _materialServices;
        private readonly IMaterialVersionServices _materialVersionServices;
        private readonly ISapSegmentServices _sapSegmentServices;
        private readonly IUser _user;

        public SapBomPhaseInjectionController(ISapBomPhaseInjectionServices SapBomPhaseInjectionServices, IMaterialGroupServices materialGroupServices, IMaterialServices materialServices, IMaterialVersionServices materialVersionServices, ISapSegmentServices sapSegmentServices,IUser user)
        {
            _sapBomPhaseInjectionServices = SapBomPhaseInjectionServices;
            _materialGroupServices = materialGroupServices;
            _materialServices = materialServices;
            _materialVersionServices = materialVersionServices;
            _sapSegmentServices = sapSegmentServices;
            _user = user;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<SapBomPhaseInjectionModel>>> GetList(SapBomPhaseInjectionRequestModel reqModel)
        {
            var data = await _sapBomPhaseInjectionServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 根据物料版本Id获取数据
        /// </summary>
        /// <param name="materialVersionId"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<SapBomPhaseInjectionModel>>> GetListbyMaterialVersionId (string materialVersionId) {
            var data = await _sapBomPhaseInjectionServices.GetListbyMaterialVersionId(materialVersionId);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<SapBomPhaseInjectionModel>>> GetPageList([FromBody] SapBomPhaseInjectionRequestModel reqModel)
        {
            var data = await _sapBomPhaseInjectionServices.GetPageList(reqModel);
            return Success(data, "获取成功");

        }

        [HttpGet("{id}")]
        public async Task<MessageModel<SapBomPhaseInjectionEntity>> GetEntity(string id)
        {
            var data = await _sapBomPhaseInjectionServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] SapBomPhaseInjectionEntity request)
        {
            var data = new MessageModel<string>();
            bool haveMaterial = false;

            if (string.IsNullOrEmpty(request.ProductMaterialId))
            {
                return Failed("未传入产出物料信息");
            }
            if (request.Type <= 0)
            {
                return Failed("未传入Type");
            }
            if(request.Type == (int)BomInjectionType.WIP)
            {
                if (string.IsNullOrEmpty(request.ProductSapSegmentId))
                {
                    return Failed("未传入产出工序");
                }
                if (string.IsNullOrEmpty(request.ConsumeSapSegmentId))
                {
                    return Failed("未传入消耗工序");
                }
            }
            else if (request.Type == (int)BomInjectionType.ConsumeOnly)
            {
                if (string.IsNullOrEmpty(request.ProductSapSegmentId))
                {
                    return Failed("未传入产出工序");
                }
            }


            if (!string.IsNullOrEmpty(request.MaterialGroupId))
            {
                var entity = await _materialGroupServices.FindEntity(request.MaterialGroupId);
                if(entity == null)
                {
                    return Failed("传入MaterialGroupId不存在");
                }
                haveMaterial= true;
            }
            if (!string.IsNullOrEmpty(request.MaterialId))
            {
                var entity = await _materialServices.FindEntity(request.MaterialId);
                if (entity == null)
                {
                    return Failed("传入MaterialId不存在");
                }
                haveMaterial = true;
            }
            if (!string.IsNullOrEmpty(request.MaterialVersionId))
            {
                var entity = await _materialVersionServices.FindEntity(request.MaterialVersionId);
                if (entity == null)
                {
                    return Failed("传入MaterialVersionId不存在");
                }
                haveMaterial = true;
            }

            if (!haveMaterial)
            {
                return Failed("未传入物料信息");
            }
          
            var productMaterialEntity = await _materialServices.FindEntity(request.ProductMaterialId);
            if (productMaterialEntity == null)
            {
                return Failed("传入产出物料不存在");
            }
            var ProductSapSegmentEntity = await _sapSegmentServices.FindEntity(request.ProductSapSegmentId);
            if (ProductSapSegmentEntity == null)
            {
                return Failed("传入产出工序不存在");
            }
            var ConsumeSapSegmentEntity = await _sapSegmentServices.FindEntity(request.ConsumeSapSegmentId);
            if (ConsumeSapSegmentEntity == null)
            {
                return Failed("传入消耗工序不存在");
            }

            var whereExpression = Expressionable.Create<SapBomPhaseInjectionEntity>().And(a => a.Deleted == 0)
                .AndIF(!string.IsNullOrEmpty(request.MaterialGroupId), a => a.MaterialGroupId == request.MaterialGroupId)
                .AndIF(!string.IsNullOrEmpty(request.MaterialId), a => a.MaterialId == request.MaterialId)
                .AndIF(!string.IsNullOrEmpty(request.MaterialVersionId), a => a.MaterialVersionId == request.MaterialVersionId)
                .AndIF(!string.IsNullOrEmpty(request.ProductMaterialId), a => a.ProductMaterialId == request.ProductMaterialId)
                .AndIF(!string.IsNullOrEmpty(request.ProductSapSegmentId), a => a.ProductSapSegmentId == request.ProductSapSegmentId)
                .AndIF(!string.IsNullOrEmpty(request.ConsumeSapSegmentId), a => a.ConsumeSapSegmentId == request.ConsumeSapSegmentId)
                .AndIF(request.Priority > 0, a => a.Priority == request.Priority).ToExpression();

            var list = await _sapBomPhaseInjectionServices.FindList(whereExpression);

            list = list.Except(list.Where(a => a.ID == request.ID)).ToList();

            if(list.Count > 0)
            {
                return Failed("已存在相同数据");
            }

            if (string.IsNullOrEmpty(request.ID))
            {
                request.Create(_user.Name);
                data.success = await _sapBomPhaseInjectionServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                } 
                else
                {
                    return Failed( "添加失败");
                }
            }
            else
            {
                request.Modify(request.ID, _user.Name);
                data.success = await _sapBomPhaseInjectionServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                } 
                else
                {
                    return Failed( "更新失败");
                }
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _sapBomPhaseInjectionServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed( "删除失败");
            }
        }
    }
  
}