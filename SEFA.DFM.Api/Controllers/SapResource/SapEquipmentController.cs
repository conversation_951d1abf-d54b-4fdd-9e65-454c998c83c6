using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;
using SEFA.Base.Common.HttpContextUser;
using SEFA.DFM.Services;

namespace SEFA.DFMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [Authorize(Permissions.Name)]
    public class SapEquipmentController : BaseApiController
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly ISapEquipmentServices _sapEquipmentServices;
        private readonly IUser _user;

        public SapEquipmentController(ISapEquipmentServices SapEquipmentServices, IUser user)
        {
            _sapEquipmentServices = SapEquipmentServices;
            _user = user;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<SapEquipmentEntity>>> GetList([FromBody] SapEquipmentRequestModel reqModel)
        {
            var whereExpression = SqlSugar.Expressionable.Create<SapEquipmentEntity>()
                .AndIF(!string.IsNullOrEmpty(reqModel.name), a => (a.Code != null && a.Code.StartsWith(reqModel.key)) || a.Name != null && a.Name.StartsWith(reqModel.name))
                .ToExpression();
            var data = await _sapEquipmentServices.FindList(whereExpression);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<SapEquipmentEntity>>> GetPageList([FromBody] SapEquipmentRequestModel reqModel)
        {
            Expression<Func<SapEquipmentEntity, bool>> whereExpression = a => true;
            if(!string.IsNullOrEmpty(reqModel.name))
            {
                whereExpression = whereExpression.And(a => a.Name.Contains(reqModel.name) || a.Code.Contains(reqModel.name));
            }
            var data = await _sapEquipmentServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<SapEquipmentEntity>> GetEntity(string id)
        {
            var data = await _sapEquipmentServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] SapEquipmentEntity request)
        {
            var data = new MessageModel<string>();

            if (string.IsNullOrEmpty(request.Code))
            {
                return Failed("编码为空");
            }
            if (string.IsNullOrEmpty(request.Name))
            {
                return Failed("名称为空");
            }
         
            var list = await _sapEquipmentServices.FindList(a => a.Code == request.Code);

            list = list.Except(list.Where(a => a.ID == request.ID)).ToList();

            if (list.Count > 0)
            {
                return Failed($"已存在[{request.Code}]");
            }


            if (string.IsNullOrEmpty(request.ID))
            {
                request.CreateCustomGuid(_user.Name);
                data.success = await _sapEquipmentServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                } 
                else
                {
                    return Failed( "添加失败");
                }
            }
            else
            {
                request.Modify(request.ID, _user.Name);
                data.success = await _sapEquipmentServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                } 
                else
                {
                    return Failed( "更新失败");
                }
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _sapEquipmentServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed( "删除失败");
            }
        }
    }
    public class SapEquipmentRequestModel : RequestPageModelBase
    {
        public string key { get; set; }
        public string name { get; set; }
    }
}