using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;
using SqlSugar;
using SEFA.DFM.Model.ViewModels;
using SEFA.DFM.Services;
using SEFA.Base.Common.HttpContextUser;
using SEFA.Base.Common.WebApiClients.HttpApis;
using log4net.Core;

namespace SEFA.DFMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [Authorize(Permissions.Name)]
    public class SapSegmentController : BaseApiController
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly ISapSegmentServices _sapSegmentServices;
        private readonly ISapEquipmentServices _sapEquipmentServices;
        private readonly IUser _user;

        public SapSegmentController(ISapSegmentServices SapSegmentServices, ISapEquipmentServices sapEquipmentServices, IUser user)
        {
            _sapSegmentServices = SapSegmentServices;
            _sapEquipmentServices = sapEquipmentServices;
            _user = user;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<SapSegmentEntity>>> GetList( SapSegmentRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<SapSegmentEntity>().And(a => a.Deleted == 0)
                .AndIF(!string.IsNullOrEmpty(reqModel.key), a => a.SegmentCode != null && a.SegmentCode.StartsWith(reqModel.key))
                .AndIF(!string.IsNullOrEmpty(reqModel.ParentId), a => a.ParentId == reqModel.ParentId)
                .AndIF(!string.IsNullOrEmpty(reqModel.SapEquipmentId), a => a.SapEquipmentId == reqModel.SapEquipmentId)
                .AndIF(reqModel.Level > 0, a => a.Level == reqModel.Level)
                .ToExpression();
            var data = await _sapSegmentServices.FindList(whereExpression);
            return Success(data, "获取成功");

        }

        [HttpPost]
        public async Task<MessageModel<List<SapSegmentEntity>>> GetSegmentList (SapSegmentRequestModel reqModel) {
            var data = await _sapSegmentServices.GetSegmentList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<SapSegmentEntity>>> GetPageList([FromBody] SapSegmentRequestModel reqModel)
        {
             
            Expression<Func<SapSegmentEntity, bool>> whereExpression = a => true;
            var data = await _sapSegmentServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return Success(data, "获取成功");

        }

        [HttpGet("{id}")]
        public async Task<MessageModel<SapSegmentEntity>> GetEntity(string id)
        {
            var data = await _sapSegmentServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveOperation ([FromBody] SapSegmentEntity request) {
            request.Level = 1;
            return await SaveForm(request);
        }
        [HttpPost]
        public async Task<MessageModel<string>> SavePhase ([FromBody] SapSegmentEntity request) {
            request.Level = 2;
            return await SaveForm(request);
        }

        private async Task<MessageModel<string>> SaveForm( SapSegmentEntity request)
        {
            var data = new MessageModel<string>();

            if(string.IsNullOrEmpty(request.SapEquipmentId))
            {
                return Failed("SapEquipmentId为空");
            }
            if (string.IsNullOrEmpty(request.SegmentCode))
            {
                return Failed("SegmentCode为空");
            }
            if (string.IsNullOrEmpty(request.SegmentName))
            {
                return Failed("SegmentName为空");
            }
            var sapEquipmentEntity = await _sapEquipmentServices.FindEntity(request.SapEquipmentId);
            if (sapEquipmentEntity == null)
            {
                return Failed("传入SapEquipmentIdy不存在");
            }


            if (request.Level == 1)
            {

                var list = await _sapSegmentServices.FindList(a => a.SapEquipmentId == request.SapEquipmentId && a.SegmentCode == request.SegmentCode && a.ID != request.ID);

                if (list.Count > 0)
                {
                    return Failed($"已存在[{request.SegmentCode}]");
                }
            }
            if (request.Level == 2)
            {

                if (string.IsNullOrEmpty(request.ParentId))
                {
                    return Failed("ParentId为空");
                }

                var parentEntity = await _sapSegmentServices.FindEntity(request.ParentId);
                if (parentEntity == null)
                {
                    return Failed("传入ParentId不存在");
                }
                if (parentEntity.SapEquipmentId != request.SapEquipmentId)
                {
                    return Failed("传入SapEquipmentId与ParentId不存在关联关系");
                }

                var list = await _sapSegmentServices.FindList(a => a.SapEquipmentId == request.SapEquipmentId && a.ParentId == request.ParentId  && a.SegmentCode == request.SegmentCode && a.ID != request.ID);

                if (list.Count > 0)
                {
                    return Failed($"已存在[{request.SegmentCode}]");
                }
            }


            if (string.IsNullOrEmpty(request.ID))
            {
                request.CreateCustomGuid(_user.Name);
                data.success = await _sapSegmentServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                } 
                else
                {
                    return Failed( "添加失败");
                }
            }
            else
            {
                request.Modify(request.ID,_user.Name);
                data.success = await _sapSegmentServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                } 
                else
                {
                    return Failed( "更新失败");
                }
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            return await _sapSegmentServices.Delete(ids);
        }
    }
   
}