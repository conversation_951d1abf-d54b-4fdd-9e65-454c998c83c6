using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;
using SEFA.Base.Common.HttpContextUser;
using SEFA.DFM.Model.ViewModels;
using SEFA.DFM.Services;

namespace SEFA.DFMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [Authorize(Permissions.Name)]
    public class SapSegmentEquipmentController : BaseApiController
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly ISapSegmentEquipmentServices _sapSegmentEquipmentServices;
        private readonly ISapSegmentServices _sapSegmentServices;
        private readonly IUser _user;

        public SapSegmentEquipmentController(ISapSegmentEquipmentServices SapSegmentEquipmentServices, ISapSegmentServices sapSegmentServices, IUser user)
        {
            _sapSegmentEquipmentServices = SapSegmentEquipmentServices;
            _sapSegmentServices = sapSegmentServices;
            _user = user;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<SapSegmentEquipmentModel>>> GetList([FromBody] SapSegmentEquipmentModel requestMode)
        {
          
            var data = await _sapSegmentEquipmentServices.GetList(requestMode);
            return Success(data, "获取成功");

        }

        [HttpPost]
        public async Task<MessageModel<List<SapSegmentEquipmentEntity>>> GetPhaseEquipmentList ([FromBody] SapSegmentEquipmentRequestModel reqModel) {
            Expression<Func<SapSegmentEquipmentEntity, bool>> whereExpression = a => true;
            var data = await _sapSegmentEquipmentServices.FindList(whereExpression);
            return Success(data, "获取成功");

        }

        [HttpPost]
        public async Task<MessageModel<PageModel<SapSegmentEquipmentEntity>>> GetPageList([FromBody] SapSegmentEquipmentRequestModel reqModel)
        {
             
            Expression<Func<SapSegmentEquipmentEntity, bool>> whereExpression = a => true;
            var data = await _sapSegmentEquipmentServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return Success(data, "获取成功");

        }

        [HttpGet("{id}")]
        public async Task<MessageModel<SapSegmentEquipmentEntity>> GetEntity(string id)
        {
            var data = await _sapSegmentEquipmentServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] SapSegmentEquipmentEntity request)
        {
            var data = new MessageModel<string>();

            var list = await _sapSegmentEquipmentServices.FindList(a => a.SapSegmentId == request.SapSegmentId && a.EquipmentId == request.EquipmentId && a.ID != request.ID);

            if(list.Count > 0)
            {
                return Failed("已存在相同数据");
            }

            if (string.IsNullOrEmpty(request.ID))
            {
                request.CreateCustomGuid(_user.Name);
                data.success = await _sapSegmentEquipmentServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                } 
                else
                {
                    return Failed( "添加失败");
                }
            }
            else
            {
                request.Modify(request.ID,_user.Name);
                data.success = await _sapSegmentEquipmentServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                } 
                else
                {
                    return Failed( "更新失败");
                }
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();

            var list = await _sapSegmentServices.FindList(a => ids.Contains(a.SapEquipmentId));
            if(list.Count > 0)
            {
                return Failed("资源已被引用，不可删除");
            }

            data.success = await _sapSegmentEquipmentServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed( "删除失败");
            }
        }
    }
    public class SapSegmentEquipmentRequestModel : RequestPageModelBase
    {
        public string key { get; set; }
    }
}