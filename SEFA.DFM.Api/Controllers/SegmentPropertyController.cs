using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;
using SEFA.DFM.Services;

namespace SEFA.DFMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [Authorize(Permissions.Name)]
    public class SegmentPropertyController : BaseApiController
    {
        /// <summary>
        /// SegmentProperty
        /// </summary>
        private readonly ISegmentPropertyServices _segmentPropertyServices;
    
        public SegmentPropertyController(ISegmentPropertyServices SegmentPropertyServices)
        {
            _segmentPropertyServices = SegmentPropertyServices;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<SegmentPropertyEntity>>> GetList([FromBody] SegmentPropertyRequestModel reqModel)
        {
            var data = await _segmentPropertyServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<SegmentPropertyEntity>>> GetPageList([FromBody] SegmentPropertyRequestModel reqModel)
        {
            var data = await _segmentPropertyServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<SegmentPropertyEntity>> GetEntity(string id)
        {
            var data = await _segmentPropertyServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] SegmentPropertyEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _segmentPropertyServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _segmentPropertyServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _segmentPropertyServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed( "删除失败");
            }
        }
    }
    //public class SegmentPropertyRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}