using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;

namespace SEFA.DFMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [Authorize(Permissions.Name)]
     public class ShifttimeController : BaseApiController
    {
            /// <summary>
            /// 
            /// </summary>
            private readonly IShifttimeServices _shifttimeServices;
    
            public ShifttimeController(IShifttimeServices ShifttimeServices)
            {
                _shifttimeServices = ShifttimeServices;
            }
    
            [HttpPost]
            public async Task<MessageModel<List<ShifttimeEntity>>> GetList(string key = "")
            {
                Expression<Func<ShifttimeEntity, bool>> whereExpression = a => true;
                var data = await _shifttimeServices.FindList(whereExpression);
                return Success(data, "获取成功");

            }

            [HttpPost]
            public async Task<MessageModel<PageModel<ShifttimeEntity>>> GetPageList(int page = 1, string key = "",int intPageSize = 50)
            {
             
                Expression<Func<ShifttimeEntity, bool>> whereExpression = a => true;
                var data = await _shifttimeServices.QueryPage(whereExpression, page, intPageSize);
                return Success(data, "获取成功");

            }

            [HttpGet("{id}")]
            public async Task<MessageModel<ShifttimeEntity>> GetEntity(string id)
            {
                var data = await _shifttimeServices.QueryById(id);
                return Success(data, "获取成功");
            }

            [HttpPost]
            public async Task<MessageModel<string>> SaveForm([FromBody] ShifttimeEntity request)
            {
                var data = new MessageModel<string>();
                if (string.IsNullOrEmpty(request.ID))
                {
                    data.success = await _shifttimeServices.Add(request) > 0;
                    if (data.success)
                    {
                        return Success("", "添加成功");
                    } 
                    else
                    {
                        return Failed( "添加失败");
                    }
                }
                else
                {
                    data.success = await _shifttimeServices.Update(request);
                    if (data.success)
                    {
                        return Success("", "更新成功");
                    } 
                    else
                    {
                        return Failed( "更新失败");
                    }
                }
            }


            [HttpPost]
            public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
            {
                var data = new MessageModel<string>();
                data.success = await _shifttimeServices.DeleteByIds(ids);
                if (data.success)
                {
                    return Success("", "删除成功");
                }
                else
                {
                    return Failed( "删除失败");
                }
            }
    }
}