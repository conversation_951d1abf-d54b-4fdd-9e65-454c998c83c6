using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;

namespace SEFA.DFMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [AllowAnonymous]
    public class SopDirController : BaseApiController
    {
        /// <summary>
        /// SopDir
        /// </summary>
        private readonly ISopDirServices _sopDirServices;
        private readonly IUserinfoServices _userinfoServices;

        public SopDirController(ISopDirServices SopDirServices, IUserinfoServices userinfoServices)
        {
            _userinfoServices = userinfoServices;
            _sopDirServices = SopDirServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<SopDirEntity>>> GetList([FromBody] SopDirRequestModel reqModel)
        {
            var data = await _sopDirServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<SopDirEntity>>> GetPageList([FromBody] SopDirRequestModel reqModel)
        {
            var data = await _sopDirServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<SopDirEntity>> GetEntity(string id)
        {
            var data = await _sopDirServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] SopDirEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _sopDirServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _sopDirServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> Insert([FromBody] SopDirEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _sopDirServices.Add(request) > 0;
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _sopDirServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }

        [HttpGet]
        public async Task<MessageModel<List<TreeModel>>> GetTreeList(string parentId = "0")
        {
            // 获取所有未删除的目录数据
            var list = await _sopDirServices.GetAllDirs();
            
            // 构建树形结构
            List<TreeModel> treeList = new List<TreeModel>();
            foreach (var item in list)
            {
                treeList.Add(new TreeModel
                {
                    id = item.ID,
                    name = item.DirName,
                    parentId = item.ParentId,
                    value = item.DirCode,
                    remark = item.OwnerUserid,
                    extendField = item.OwnerUser,
                });
            }

            // 如果指定了父节点，则只返回该父节点下的子节点
            if (parentId != "0")
            {
                treeList = treeList.Where(a => a.parentId == parentId).ToList();
            }

            return new MessageModel<List<TreeModel>>()
            {
                success = true,
                msg = "获取成功",
                response = treeList.ToTree() // 使用ToTree扩展方法生成树形结构
            };
        }
    }
}