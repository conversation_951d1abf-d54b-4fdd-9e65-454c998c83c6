using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;

namespace SEFA.DFMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [AllowAnonymous]
    public class SopPermApplyController : BaseApiController
    {
        /// <summary>
        /// SopPermApply
        /// </summary>
        private readonly ISopPermApplyServices _sopPermApplyServices;
    
        public SopPermApplyController(ISopPermApplyServices SopPermApplyServices)
        {
            _sopPermApplyServices = SopPermApplyServices;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<SopPermApplyEntity>>> GetList([FromBody] SopPermApplyRequestModel reqModel)
        {
            var data = await _sopPermApplyServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<SopPermApplyEntity>>> GetPageList([FromBody] SopPermApplyRequestModel reqModel)
        {
            var data = await _sopPermApplyServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<SopPermApplyEntity>> GetEntity(string id)
        {
            var data = await _sopPermApplyServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] SopPermApplyEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _sopPermApplyServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _sopPermApplyServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> Insert([FromBody] SopPermApplyEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _sopPermApplyServices.Add(request) > 0;
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }
        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _sopPermApplyServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed( "删除失败");
            }
        }
    }
    //public class SopPermApplyRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}