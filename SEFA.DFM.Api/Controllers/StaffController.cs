using AutoMapper;
using Magicodes.ExporterAndImporter.Excel;
using Magicodes.ExporterAndImporter.Excel.AspNetCore;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Nacos.V2.Utils;
using SEFA.Base;
using SEFA.Base.Common.HttpContextUser;
using SEFA.Base.Model;
using SEFA.DFM.Controllers;
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using SqlSugar;
using System.Data;

namespace SEFA.DFMApi.Controllers
{
    /// <summary>
    /// 员工控制器
    /// </summary>
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class StaffController : BaseApiController
    {
        private readonly IStaffServices _staffServices;

        //private readonly IStaffSchedulingServices _staffSchedulingServices;
        //private readonly IStaffLineServices _staffLineServices;
        //private readonly IStaffSkillServices _staffSkillServices;
        //private readonly IStaffSiteServices _staffSiteServices;
        //private readonly IScheduleServices _scheduleServices;
        //private readonly IStaffPostServices _staffPostServices;
        private readonly IStaffDutyLineServices _staffDutyLineServices;
        private readonly IUserinfoServices _userinfoServices;
        //private readonly IDFMServices _dfmServices;
        private readonly IUser _user;

        private readonly IMapper _mapper;

        //private readonly IUwbReplaceServices _uwbReplaceServices;
        private readonly IConfigurationBuilder _configuration;


        public StaffController( //IStaffPostServices staffPostServices,
                                //IScheduleServices scheduleServices,
                                //IStaffSkillServices staffSkillServices,
                                //IStaffSchedulingServices staffSchedulingServices,
                                //IStaffLineServices staffLineServices,
            IStaffServices StaffServices,
            IMapper mapper,
            //IStaffSiteServices staffSiteServices,
            IStaffDutyLineServices staffDutyLineServices,
            IUserinfoServices userinfoServices,
            IUser user //,
                       //IUwbReplaceServices uwbReplaceServices,
                       //IConfigurationBuilder configuration,
                       //IDFMServices dfmServices
        )
        {
            //_uwbReplaceServices = uwbReplaceServices;
            _staffDutyLineServices = staffDutyLineServices;
            //_staffPostServices = staffPostServices;
            //_scheduleServices = scheduleServices;
            //_staffLineServices = staffLineServices;
            //_staffSkillServices = staffSkillServices;
            //_staffSiteServices = staffSiteServices;
            _mapper = mapper;
            _user = user;
            _staffServices = StaffServices;
            _userinfoServices = userinfoServices;
            //_dfmServices = dfmServices;
            //_staffSchedulingServices = staffSchedulingServices;
            //_configuration= configuration;
        }

        /// <summary>
        /// 获取数据集合 
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<StaffDto>>> GetList([FromBody] StaffRequestModel reqModel)
        {
            //var test= _configuration.GetConfigValue<string>("ShowStaff");
            var whereExpression = Expressionable.Create<StaffDutyLineEntity>().And(a => a.Deleted == 0)
                .AndIF(reqModel.key.IsNotEmptyOrNull(),
                    x => x.StaffName.Contains(reqModel.key) || x.StaffCode.Contains(reqModel.key))
                //.AndIF(!reqModel.IsSelectAll, x => !x.StaffGroup.StartsWith("国内管理"))//是否查询全部人员数据 2023年6月5日
                .AndIF(reqModel.Name.IsNotEmptyOrNull(), x => x.StaffName.Equals(reqModel.Name))
                .AndIF(reqModel.Code.IsNotEmptyOrNull(), x => reqModel.Codes.Contains(x.StaffCode))
                // .AndIF(reqModel.FeiShu.Count > 0, x => reqModel.FeiShu.Contains(x.Feishu))
                // .AndIF(reqModel.UWB.IsNotEmptyOrNull(), x => reqModel.UWB.Contains(x.Uwb))
                .ToExpression();
            var users = await _userinfoServices.FindList(c => c.Status == 1 && c.Deleted == 0);
            //var data = await _staffDutyLineServices.FindList(whereExpression);

            List<StaffDto> result = new List<StaffDto>();
            if (users.Count > 0)
            {
                foreach (var staff in users)
                {
                    StaffDto staffDto = new StaffDto
                    {
                        ID = staff.ID,
                        //Name = staffDutyLineEntity.StaffCode + " " + staffDutyLineEntity.StaffName,
                        Name = staff.UserName + "(" + staff.LoginName + ")",
                        Code = staff.LoginName,
                        UserAvatar = "",
                        Region = "",
                        StaffGroup = "",
                        Factory = staff.Departmentid,
                        BusinessDesc = "",
                        WorkshopDesc = "",
                        VirtualOrganization = "",
                    };
                    result.Add(staffDto);
                }
            }

            //var result = _mapper.Map<List<StaffDto>>(data);

            return Success(result, "获取成功");
        }

        /// <summary>
        /// 获取 分页_数据
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<PageModel<StaffDto>>> GetPageList([FromBody] StaffRequestModel reqModel)
        {
            //var notDisplay = Appsettings.GetValue("NotDisplay")!=""? Appsettings.GetValue("NotDisplay").Split(",").ToList():new List<string>();
            //var onlyDisplay = Appsettings.GetValue("OnlyDisplay") != "" ? Appsettings.GetValue("OnlyDisplay").Split(",").ToList() : new List<string>();
            var whereExpression = Expressionable.Create<StaffEntity>().And(a => a.Deleted == 0)
                //.AndIF(!reqModel.IsSelectAll,x=>!x.StaffGroup.StartsWith("国内管理"))//是否查询全部人员数据 2023年6月5日
                //.AndIF(notDisplay.Any(), x => !notDisplay.Contains(x.WorkshopDesc) || x.WorkshopDesc == null)
                //.AndIF(onlyDisplay.Any(), x => onlyDisplay.Contains(x.WorkshopDesc)|| x.WorkshopDesc==null)
                .AndIF(reqModel.Name.IsNotEmptyOrNull(), x => x.Name.Equals(reqModel.Name))
                .AndIF(reqModel.key.IsNotEmptyOrNull(),
                    x => x.Name.Contains(reqModel.key) || x.Code.Contains(reqModel.key))
                .AndIF(reqModel.Code.IsNotEmptyOrNull(), x => x.Code.Equals(reqModel.Code))
                .AndIF(reqModel.FeiShu.Count > 0, x => reqModel.FeiShu.Contains(x.Feishu))
                .AndIF(reqModel.Codes.Count > 0, x => reqModel.Codes.Contains(x.Code))
                .AndIF(reqModel.UWB.IsNotEmptyOrNull(), x => reqModel.UWB.Contains(x.Uwb))
                .ToExpression();
            var query = await _staffServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize,
                "CREATEDATE desc");
            var result = _mapper.Map<PageModel<StaffDto>>(query);
            return Success(result, "获取成功");
        }

        /// <summary>
        /// 获取 单个员工信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("{id}")]
        public async Task<MessageModel<StaffDto>> GetEntity(string id)
        {
            var entity = await _staffServices.QueryById(id);
            var result = _mapper.Map<StaffDto>(entity);
            //result.DepartementName = "";//部门名称
            //result.FactoryName = "";//工厂名称
            //result.StarLevelName = "";//;星级名称
            //result.WorkshopSectionName = "";//工段名称
            return Success(result, "获取成功");
        }


        /// <summary>
        /// 保存 员工信息
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] StaffDto dto)
        {
            var data = new MessageModel<string>();
            var entity = _mapper.Map<StaffEntity>(dto);

            if (!entity.Code.IsNotNullOrWhiteSpace() || !entity.Name.IsNotNullOrWhiteSpace())
            {
                return Failed("人员名称和工号必填！");
            }

            var isExistCode = await _staffServices.IsExistCode(entity);
            if (isExistCode)
            {
                return Failed("", $"操作失败:员工编号[{entity.Code}]已经存在");
            }

            if (string.IsNullOrEmpty(entity.ID))
            {
                data.success = await _staffServices.Add(entity) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                var dbEntity = await _staffServices.FindEntity(entity.ID);
                if (dbEntity == null)
                {
                    return Failed("更新失败！数据不存在！");
                }

                _mapper.Map(dto, dbEntity);
                data.success = await _staffServices.Update(dbEntity);
                if (data.success)
                {
                    //设置人员负责的产线信息


                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }


        /// <summary>
        /// 人员信息导入
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> Import([FromForm] FileImportDto input)
        {
            ExcelImporter Importer = new ExcelImporter();
            var stream = input.File.OpenReadStream();
            var import = await Importer.Import<StaffExcelDto>(stream);
            if (import.Data.Count() < 1)
                return Failed("表格中无有效数据");
            // 返回 导入异常信息
            if (import.Exception != null)
                return Failed(import.Exception.ToString(), "出现错误");
            var excelData = import.Data.Where(x => x.Code != null).ToList();
            if (excelData.Count() < 1)
                return Failed("表格中无有效数据");

            var codes = excelData.Select(x => x.Code).ToList();
            var updateStaffInfo = await _staffServices.FindList(x => codes.Contains(x.Code));
            var existCodes = updateStaffInfo.Select(x => x.Code).ToList();
            var updateData = excelData.Where(x => existCodes.Contains(x.Code)).ToList();
            var addData = excelData.Where(x => !existCodes.Contains(x.Code)).ToList();
            var updateList = new List<StaffEntity>();
            //更新数据
            foreach (var item in updateData)
            {
                var entity = updateStaffInfo.Where(x => x.Code == item.Code).FirstOrDefault();
                if (entity == null)
                {
                    continue;
                }

                entity.Code = item.Code;
                entity.Name = item.Name;
                entity.BusinessDesc = item.BusinessDesc;
                entity.WorkshopDesc = item.WorkshopDesc;
                entity.StaffGroup = item.StaffGroup;
                entity.Region = item.Region;
                entity.Factory = item.Factory;
                entity.JobTitle = item.JobTitle;
                entity.Rank = item.Rank;
                entity.StarLevel = item.StarLevel;
                entity.VirtualOrganization = item.VirtualOrganization;
                entity.Uwb = item.Uwb;
                entity.LeaderCode = item.LeaderCode;
                entity.LeaderName = item.LeaderName;
                entity.StartValidTime = item.StartValidTime;
                entity.EndValidTime = item.EndValidTime;
                entity.Price = item.Price;

                updateList.Add(entity);
            }

            if (updateList.Count > 0)
            {
                await _staffServices.Update(updateList);
            }

            var bigList = new List<StaffEntity>();
            //新增数据
            foreach (var addItem in addData)
            {
                var entity = new StaffEntity();
                entity.Code = addItem.Code;
                entity.Name = addItem.Name;
                entity.BusinessDesc = addItem.BusinessDesc;
                entity.WorkshopDesc = addItem.WorkshopDesc;
                entity.StaffGroup = addItem.StaffGroup;
                entity.Region = addItem.Region;
                entity.Factory = addItem.Factory;
                entity.JobTitle = addItem.JobTitle;
                entity.Rank = addItem.Rank;
                entity.StarLevel = addItem.StarLevel;
                entity.VirtualOrganization = addItem.VirtualOrganization;
                entity.Uwb = addItem.Uwb;
                entity.LeaderCode = addItem.LeaderCode;
                entity.LeaderName = addItem.LeaderName;
                entity.StartValidTime = addItem.StartValidTime;
                entity.EndValidTime = addItem.EndValidTime;
                entity.Price = addItem.Price;
                entity.CreateCustomGuid(_user.Name);
                entity.CreateUserId = _user.Name;
                bigList.Add(entity);
            }

            if (bigList.Count > 0)
            {
                var result = await _staffServices.Add(bigList);
            }

            return Success("", "导入成功");
        }

        /// <summary>
        /// 下载模板
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> DownLoadTemplate()
        {
            ExcelImporter Importer = new ExcelImporter();
            var result = await Importer.GenerateTemplateBytes<StaffExcelDto>();
            var fs = new MemoryStream(result);
            return new XlsxFileResult(stream: fs, fileDownloadName: "人员基础信息导入模板");
        }


        /// <summary>
        /// 删除 员工
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();

            //删除 员工
            data.success = await _staffServices.DeleteByIds(ids);
            ////删除关联关系
            ////1、员工_楼层
            //await _staffSiteServices.DeleteByStaffId(ids);

            ////2、员工_技能
            //await _staffSkillServices.DeleteByStaffId(ids);

            ////3、员工_物理线
            //await _staffLineServices.DeleteByStaffId(ids);

            //4、员工_会议（会议是记录表)

            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }

        //#region andon 相关接口暂时屏蔽
        ///// <summary>
        ///// 获取人员数据__(提供给Andon的接口）
        ///// </summary>
        ///// <param name="reqModel"></param>
        ///// <returns></returns>
        //[HttpPost]
        //public async Task<MessageModel<List<AndonStaffDto>>> GetStaffInfoList([FromBody] AndonStaffReuqestModel reqModel)
        //{

        //    //没传产线 在人员产线配置表中查询 2023年5月31日 增加
        //    if (!reqModel.Line.IsNotEmptyOrNull())
        //    {
        //        var baseData = await _staffDutyLineServices.FindList(x => reqModel.AndonPosts.Contains(x.AndonPost)
        //        && x.LineId==null
        //        && x.SegmentId==null
        //        );
        //        var staffCodes = baseData.Select(x => x.StaffCode).ToList();
        //        var staffInfos = await _staffServices.FindList(x => staffCodes.Contains(x.Code));
        //        var result = _mapper.Map<List<AndonStaffDto>>(staffInfos);
        //        return Success(result, "获取成功");
        //    }

        //    var shift = "白班";
        //    var date = DateTime.Now;
        //    if (DateTime.Now.Hour > 0 && DateTime.Now.Hour < 7)
        //    {
        //        date = DateTime.Now.AddDays(-1);
        //        shift = "夜班";
        //    }
        //    if (DateTime.Now.Hour > 19)
        //    {
        //        shift = "夜班";
        //    }
        //    var whereExpression = Expressionable.Create<StaffSchedulingEntity>()
        //        .And(x => x.Deleted == 0)
        //        .And(x => x.Date == date.Date)
        //     .ToExpression();
        //    var staffScheduling = await _staffSchedulingServices.FindList(whereExpression);

        //    #region 旧逻辑
        //    //var staffCodes = staffScheduling.Select(x => x.StaffCode).Distinct().ToList();
        //    ////如果产线加工段加安灯岗位找不到人员，人员那边自动通过产线加安灯岗位进行查询，如果还找不到人员就通过安灯岗位直接查询   2023年3月31日 安灯 需求
        //    ////第一个结果
        //    //var line_Segment_Post = staffScheduling.Where(x => x.LineId == reqModel.Line
        //    //    && x.SegmentId == reqModel.Segment
        //    //    && reqModel.AndonPosts.Contains(x.AndonPost))
        //    //    .ToList();
        //    ////第二个结果
        //    //var line_Post = staffScheduling.Where(x => x.LineId == reqModel.Line
        //    //    && reqModel.AndonPosts.Contains(x.AndonPost))
        //    //    .ToList();
        //    ////第三个结果
        //    //var post = staffDutyLine.Where(x =>reqModel.AndonPosts.Contains(x.AndonPost)).ToList();//取消的通过安灯岗位过滤 2023年5月9日

        //    //最后返回接口
        //    //var query =line_Segment_Post.Count != 0 ? line_Segment_Post: line_Post.Count!=0? line_Post: post;

        //    //var query = line_Segment_Post.Count != 0 ? line_Segment_Post : line_Post;
        //    //if (reqModel.Line.IsNotEmptyOrNull() || !reqModel.Segment.IsNotEmptyOrNull() || reqModel.AndonPosts.Count > 0)
        //    //{
        //    //    staffCodes = query.Select(x => x.StaffCode).ToList();
        //    //}
        //    #endregion

        //    //1.产线+工段+安灯岗位+班次；
        //    var result1 = staffScheduling.Where(x => reqModel.Line == x.LineId
        //        && x.SegmentId == reqModel.Segment
        //        && x.Shift == shift
        //        && reqModel.AndonPosts.Contains(x.AndonPost)
        //        ).ToList();

        //    //2.产线 + 工段 + 安灯岗位 + "无班次"；
        //    var result2 = staffScheduling.Where(x => reqModel.Line == x.LineId
        //       && x.SegmentId == reqModel.Segment
        //       && (x.Shift == "" || x.Shift == null)
        //       && reqModel.AndonPosts.Contains(x.AndonPost)
        //       ).ToList();
        //    // 3. 产线+安灯岗位+班次；
        //    var result3 = staffScheduling.Where(x => reqModel.Line == x.LineId
        //       && reqModel.AndonPosts.Contains(x.AndonPost)
        //       ).ToList();
        //    //4.产线+安灯岗位+"无班次"；
        //    var result4 = staffScheduling.Where(x => reqModel.Line == x.LineId
        //      && reqModel.AndonPosts.Contains(x.AndonPost)
        //      ).ToList();
        //    var lastData = result1.Any() ? result1 : result1.Any() ? result1 : result2.Any() ? result2 : result3.Any() ? result3 : result4.Any() ? result4 : null;
        //    if (lastData == null)
        //    {
        //        return Success(new List<AndonStaffDto>(), "获取成功");
        //    }
        //    else
        //    {
        //        var staffCodes = lastData.Select(x => x.StaffCode).ToList();
        //        var staffInfos = await _staffServices.FindList(x => staffCodes.Contains(x.Code));
        //        var result = _mapper.Map<List<AndonStaffDto>>(staffInfos);
        //        return Success(result, "获取成功");
        //    }

        //}


        ///// <summary>
        ///// 获取人员数据__(提供给Andon的接口）
        ///// </summary>
        ///// <param name="reqModel"></param>
        ///// <returns></returns>
        //[HttpPost]
        //public async Task<MessageModel<List<AndonStaffDto>>> GetAndonStaffInfoList([FromBody] AndonStaffReuqestModel reqModel)
        //{
        //    var shift = "白班";
        //    var date = DateTime.Now;
        //    if (DateTime.Now.Hour > 0 && DateTime.Now.Hour < 7)
        //    {
        //        date = DateTime.Now.AddDays(-1);
        //        shift = "夜班";
        //    }
        //    if (DateTime.Now.Hour > 19)
        //    {
        //        shift = "夜班";
        //    }

        //    var whereExpression = Expressionable.Create<StaffSchedulingEntity>()
        //        .And(x => x.Deleted == 0)
        //        .And(x => x.Date == date.Date)
        //     .ToExpression();
        //    var staffScheduling = await _staffSchedulingServices.FindList(whereExpression);

        //    //1.产线+工段+安灯岗位+职责范围+班次；
        //    var result1 = staffScheduling.Where(x => reqModel.Line == x.LineId
        //        && x.SegmentId == reqModel.Segment
        //        && x.Shift == shift
        //        && reqModel.AndonPosts.Contains(x.AndonPost)
        //        && reqModel.PostDutyRange.Contains(x.AndonPostRange)
        //        ).ToList();

        //    //2.产线 + 工段 + 安灯岗位+ 职责范围 + "无班次"；
        //    var result2 = staffScheduling.Where(x => reqModel.Line == x.LineId
        //       && x.SegmentId == reqModel.Segment
        //       && (x.Shift == "" || x.Shift == null)
        //       && reqModel.AndonPosts.Contains(x.AndonPost)
        //       && reqModel.PostDutyRange.Contains(x.AndonPostRange)
        //       ).ToList();

        //    // 3.产线+安灯岗位+职责范围+班次；
        //    var result3 = staffScheduling.Where(x => reqModel.Line == x.LineId
        //       && reqModel.AndonPosts.Contains(x.AndonPost)
        //       && reqModel.PostDutyRange.Contains(x.AndonPostRange)
        //       ).ToList();

        //    //4.产线+安灯岗位+职责范围+"无班次"；
        //    var result4 = staffScheduling.Where(x => reqModel.Line == x.LineId
        //      && reqModel.AndonPosts.Contains(x.AndonPost)
        //      && reqModel.PostDutyRange.Contains(x.AndonPostRange)
        //      ).ToList();

        //    var lastData = result1.Any() ? result1 : result1.Any() ? result1 : result2.Any() ? result2 : result3.Any() ? result3 : result4.Any() ? result4 : null;
        //    //lastData = lastData.Where(x=>x.)

        //    if (lastData == null)
        //    {
        //        return Success(new List<AndonStaffDto>(), "获取成功");
        //    }
        //    else
        //    {
        //        var staffCodes = lastData.Select(x => x.StaffCode).ToList();
        //        var staffInfos = await _staffServices.FindList(x => staffCodes.Contains(x.Code));
        //        var result = _mapper.Map<List<AndonStaffDto>>(staffInfos);
        //        return Success(result, "获取成功");
        //    }

        //}


        ///// <summary>
        ///// tabel to list
        ///// </summary>
        ///// <typeparam name="T"></typeparam>
        ///// <param name="dt"></param>
        ///// <returns></returns>
        //public static List<T> TableToListModel<T>(DataTable dt) where T : new()
        //{
        //    // 定义集合    
        //    List<T> ts = new List<T>();

        //    // 获得此模型的类型   
        //    Type type = typeof(T);
        //    string tempName = "";

        //    foreach (DataRow dr in dt.Rows)
        //    {
        //        T t = new T();
        //        // 获得此模型的公共属性      
        //        PropertyInfo[] propertys = t.GetType().GetProperties();
        //        foreach (PropertyInfo pi in propertys)
        //        {
        //            tempName = pi.Name;  // 检查DataTable是否包含此列    

        //            if (dt.Columns.Contains(tempName))
        //            {
        //                // 判断此属性是否有Setter      
        //                if (!pi.CanWrite) continue;

        //                object value = dr[tempName];
        //                if (value != DBNull.Value)
        //                    pi.SetValue(t, value, null);
        //            }
        //        }
        //        ts.Add(t);
        //    }
        //    return ts;
        //}
        //#endregion
    }
}