using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Common.HttpContextUser;
using SEFA.Base.Model;
using SEFA.DFM.Controllers;
using SEFA.DFM.IRepository;
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using SqlSugar;
using System.Data;
using System.Linq.Expressions;
using System.Reflection;

namespace SEFA.DFMApi.Controllers
{
    /// <summary>
    /// 人员排班记录表
    /// </summary>
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class StaffSchedulingController : BaseApiController
    {
        private readonly IStaffSchedulingServices _staffSchedulingServices;
        private readonly IStaffSchedulingRepository _staffSchedulingRepository;
        private readonly ICalendarServices _calendarServices;
        private readonly IStaffDutyLineServices _staffDutyLineServices;
        private readonly IStaffServices _staffServices;
        private readonly ITeamServices _teamServices;
        private readonly IUser _user;
        private readonly IMapper _mapper;


        public StaffSchedulingController(IStaffSchedulingServices StaffSchedulingServices,
            IStaffDutyLineServices staffDutyLineServices,
            IStaffSchedulingRepository staffSchedulingRepository,
            IStaffServices staffServices,
            ICalendarServices calendarServices,
            ITeamServices teamServices,
            IMapper mapper,
            IUser user)
        {
            _mapper = mapper;
            _staffServices = staffServices;
            _user = user;
            _calendarServices = calendarServices;
            _teamServices = teamServices;
            _staffDutyLineServices = staffDutyLineServices;
            _staffSchedulingServices = StaffSchedulingServices;
            _staffSchedulingRepository = staffSchedulingRepository;
        }

        /// <summary>
        /// 获取人员排班信息
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<StaffSchedulingEntity>>> GetList(string key = "")
        {
            Expression<Func<StaffSchedulingEntity, bool>> whereExpression = a => true;
            var data = await _staffSchedulingServices.FindList(whereExpression);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 获取人员排班记录
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<PageModel<StaffSchedulingDto>>> GetPageList(
            [FromBody] GetStaffSchedulingDto reqModel)
        {
            if (reqModel.Date.ToString("HH:mm:ss") != "00:00:00" && reqModel.Date.Hour < 7)
            {
                reqModel.Date = reqModel.Date.AddDays(-1);
            }

            var data = await _staffSchedulingRepository.GetGroupPaged(reqModel);
            return Success(data, "获取成功");
        }


        /// <summary>
        /// 获取人员排班信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("{id}")]
        public async Task<MessageModel<StaffSchedulingEntity>> GetEntity(string id)
        {
            var data = await _staffSchedulingServices.QueryById(id);
            return Success(data, "获取成功");
        }


        /// <summary>
        /// 添加/修改数据
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] StaffSchedulingEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _staffSchedulingServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _staffSchedulingServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }


        /// <summary>
        /// 确认工时
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> ConfirmTimes([FromBody] StaffSchedulingEntity request)
        {
            var entity = await _staffSchedulingServices.FindEntity(request.ID);
            if (entity != null)
            {
                entity.ConfirmTimes = request.ConfirmTimes;

                await _staffSchedulingServices.Update(entity);
                return Success("", "操作成功");
            }
            else
            {
                return Failed("操作失败！数据不存在！");
            }
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _staffSchedulingServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }


        /// <summary>
        /// 设置人员班组
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> SetStaffTeam([FromBody] SetStaffTeamDto request)
        {
            //选中的数据
            var entitys = await _staffSchedulingServices.FindList(x => request.Ids.Contains(x.ID));
            var segmentIds = entitys.Select(x => x.SegmentId).ToList();
            //数据日期
            var date = entitys.FirstOrDefault()?.Date;
            //日历信息
            var allcalendarInfos =
                _calendarServices.QueryListByModelids(segmentIds.ToArray(), date.Value.ToLongDateString());

            //获取所有的班组
            var allTeams = await _teamServices.Query();

            //删除数据集合
            var updateList = new List<StaffSchedulingEntity>();

            foreach (var item in entitys)
            {
                if (request.Teams.Count > 0)
                {
                    //设置多个班组
                    var teamInfos = allTeams.Where(x => x.Departmentid == item.SegmentId
                                                        && request.Teams.Contains(x.Name));
                    var teamNames = teamInfos.Select(x => x.Name).ToList();
                    var teamIds = teamInfos.Select(x => x.ID).ToList();
                    item.Team = string.Join(",", teamNames);
                    item.TeamId = string.Join(",", teamIds);
                    var calendarInfo = allcalendarInfos
                        .Where(x => x.LineID == item.SegmentId && teamNames.Contains(x.Team)).OrderBy(x => x.StartTime)
                        .FirstOrDefault();
                    //确定白夜班
                    item.Shift = calendarInfo?.Shift;
                    item.ShiftId = calendarInfo?.ShiftId;
                }
                else
                {
                    var teamInfo = allTeams.Where(x => x.Departmentid == item.SegmentId
                                                       && x.Name == request.Team
                    ).FirstOrDefault();
                    if (teamInfo != null)
                    {
                        item.Team = teamInfo.Name;
                        item.TeamId = teamInfo.ID;
                    }

                    var calendarInfo = allcalendarInfos.Where(x => x.LineID == item.SegmentId && x.Team == request.Team)
                        .FirstOrDefault();
                    if (calendarInfo != null)
                    {
                        //确定白夜班
                        item.Shift = calendarInfo.Shift;
                        item.ShiftId = calendarInfo.ShiftId;
                    }
                }

                item.ClockInTime = null;
                item.ClockOffTime = null;
                item.HrTimes = null;
                updateList.Add(item);
            }

            if (updateList.Count > 0)
            {
                await _staffSchedulingServices.Update(updateList);
            }

            return Success("", "设置成功");
        }

        /// <summary>
        /// 生成人员排班
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> GenerateStaffScheduling(GenerateStaffScheduingModel model)
        {
            if (model.Date == null || model.Date.Value.Year < 2000)
            {
                model.Date = DateTime.Now;
            }

            await _staffSchedulingServices.GenerateStaffScheduling(model.Date);
            return Success("", "生成成功");
        }

        /// <summary>
        /// 根据安灯岗位获取人员信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<StaffSchedulingEntity>>> GetStaffSchedulingFromAndon(
            AndonStaffReuqestModel andonStaffReuqestModel)
        {
            var result = await _staffSchedulingServices.GetStaffSchedulingFromAndon(andonStaffReuqestModel);
            return Success(result, "根据安灯岗位获取人员信息成功");
        }

        /// <summary>
        /// 获取是否员工出勤数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<MessageModel2<string>> PushStaffAttendance()
        {
            return await _staffSchedulingServices.PushStaffAttendance();
        }


        public class GenerateStaffScheduingModel
        {
            public DateTime? Date { get; set; }
        }

        /// <summary>
        /// tabel to list
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="dt"></param>
        /// <returns></returns>
        public static List<T> TableToListModel<T>(DataTable dt) where T : new()
        {
            // 定义集合    
            List<T> ts = new List<T>();

            // 获得此模型的类型   
            Type type = typeof(T);
            string tempName = "";

            foreach (DataRow dr in dt.Rows)
            {
                T t = new T();
                // 获得此模型的公共属性      
                PropertyInfo[] propertys = t.GetType().GetProperties();
                foreach (PropertyInfo pi in propertys)
                {
                    tempName = pi.Name; // 检查DataTable是否包含此列    

                    if (dt.Columns.Contains(tempName))
                    {
                        // 判断此属性是否有Setter      
                        if (!pi.CanWrite) continue;

                        object value = dr[tempName];
                        if (value != DBNull.Value)
                            pi.SetValue(t, value, null);
                    }
                }

                ts.Add(t);
            }

            return ts;
        }
    }


    public class GenerateStaffSchedulingRequestModel
    {
        public string LineCode { get; set; }
    }


    public class StaffSchedulingRequestModel : RequestPageModelBase
    {
        public string key { get; set; }


        /// <summary>
        /// 人员分类
        /// </summary>
        public string Type { get; set; }


        /// <summary>
        /// 类型
        /// </summary>
        public string Type2 { get; set; }


        /// <summary>
        /// 类别
        /// </summary>
        public string Type3 { get; set; }


        /// <summary>
        /// 类型
        /// </summary>
        public string Source { get; set; }

        /// <summary>
        /// 班组
        /// </summary>
        public string Team { get; set; }

        /// <summary>
        /// 时间 （默认时间为当前时间）
        /// </summary>
        public DateTime Date { get; set; } = DateTime.Now;

        /// <summary>
        /// 产线ID
        /// </summary>
        public string LineId { get; set; }

        /// <summary>
        /// 工段ID
        /// </summary>
        public string SegmentId { get; set; }

        /// <summary>
        ///  班组ID
        /// </summary>
        public string TeamId { get; set; }


        /// <summary>
        ///  班次
        /// </summary>
        public string Shift { get; set; }
    }
}