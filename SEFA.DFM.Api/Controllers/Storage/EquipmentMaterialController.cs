using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;
using SEFA.Base.Common.HttpContextUser;
using SqlSugar;
using Magicodes.ExporterAndImporter.Excel.AspNetCore;
using Magicodes.ExporterAndImporter.Excel;
using SEFA.DFM.Services;
using AutoMapper;

namespace SEFA.DFMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [Authorize(Permissions.Name)]
    public class EquipmentMaterialController : BaseApiController
    {
        /// <summary>
        /// EquipmentMaterial
        /// </summary>
        private readonly IEquipmentMaterialServices _equipmentMaterialServices;
        private readonly IMaterialServices _materialServices;
        private readonly IMaterialGroupServices _materialGroupServices;
        private readonly IUser _user;
        private readonly IMapper _mapper;
        public EquipmentMaterialController(IEquipmentMaterialServices EquipmentMaterialServices, IUser user, IMaterialServices materialServices, IMaterialGroupServices materialGroupServices , IMapper mapper)
        {
            _equipmentMaterialServices = EquipmentMaterialServices;
            _user = user;
            _materialServices = materialServices;
            _materialGroupServices = materialGroupServices;
            _mapper = mapper;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<EquipmentMaterialRequestModel>>> GetList([FromBody] EquipmentMaterialRequestModel reqModel)
        {
            var data = await _equipmentMaterialServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<EquipmentMaterialRequestModel>>> GetPageList([FromBody] EquipmentMaterialRequestModel reqModel)
        {
            var data = await _equipmentMaterialServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<EquipmentMaterialEntity>> GetEntity(string id)
        {
            var data = await _equipmentMaterialServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] EquipmentMaterialEntity request)
        {
            var data = new MessageModel<string>();

            if(string.IsNullOrEmpty(request.MaterialId) && string.IsNullOrEmpty(request.GroupId))
            {
                return Failed("MaterialId和GroupId为空");
            }
            if (string.IsNullOrEmpty(request.EquipmentId))
            {
                return Failed("EquipmentId为空");
            }
            if (string.IsNullOrEmpty(request.Type))
            {
                return Failed("Type为空");
            }
            if (!string.IsNullOrEmpty(request.MaterialId))
            {
               var material =  await _materialServices.FindEntity(request.MaterialId);
                if(material == null)
                {
                    return Failed("MaterialId不存在");
                }    
            }
            if (!string.IsNullOrEmpty(request.GroupId))
            {
                var materialGroup = await _materialGroupServices.FindEntity(request.GroupId);
                if (materialGroup == null)
                {
                    return Failed("GroupId不存在");
                }
            }
            /*防止数据重复*/
            var where = Expressionable.Create<EquipmentMaterialEntity>()
                .And(a => a.EquipmentId == request.EquipmentId)
                .AndIF(!string.IsNullOrEmpty(request.MaterialId), a => a.MaterialId == request.MaterialId)
                .AndIF(!string.IsNullOrEmpty(request.GroupId), a => a.GroupId == request.GroupId)
                .ToExpression();
            var list = await _equipmentMaterialServices.FindList(where);
            
            list = list.Except(list.Where(a => a.ID == request.ID)).ToList();
            if (list.Count > 0)
            {
                return Failed("已存在该物料数据");
            }

            if (string.IsNullOrEmpty(request.ID))
            {
                request.CreateCustomGuid(_user.Name);
                data.success = await _equipmentMaterialServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                request.Modify(request.ID, _user.Name);
                data.success = await _equipmentMaterialServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> Insert([FromBody] EquipmentMaterialEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _equipmentMaterialServices.Add(request) > 0;
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }
        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _equipmentMaterialServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed( "删除失败");
            }
        }


        #region 数据导入、导出
        /// <summary>
        /// 数据导入
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Consumes("multipart/form-data")]
        [HttpPost]
        public async Task<MessageModel<string>> ImportData ([FromForm] FileImportDto input) {
            var result = await _equipmentMaterialServices.ImportData(input);
            if (result.Succeed)
                return Success(result.Data, "导入成功");
            else
                return Failed("导入失败：" + result.Error.Text);
        }

        /// <summary>
        /// 数据导出
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<ActionResult> ExportData ([FromBody] EquipmentMaterialRequestModel reqModel) {
            ExcelExporter exporter = new ExcelExporter();
            var query = await _equipmentMaterialServices.GetList(reqModel);

            var data = _mapper.Map<List<EquipmentMaterialExcelDto>>(query);

            var result = await exporter.ExportAsByteArray<EquipmentMaterialExcelDto>(data);
            var fs = new MemoryStream(result);
            return new XlsxFileResult(stream: fs, fileDownloadName: $"物料属性-{DateTime.Now.ToString("yyyy-MM-dd HH-mm-ss")}");
        }

        /// <summary>
        /// 下载模板
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> DownLoadTemplate () {
            ExcelImporter Importer = new ExcelImporter();
            var result = await Importer.GenerateTemplateBytes<EquipmentMaterialExcelDto>();
            var fs = new MemoryStream(result);
            return new XlsxFileResult(stream: fs, fileDownloadName: "导入模板");
        }

        #endregion
    }
    //public class EquipmentMaterialRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}