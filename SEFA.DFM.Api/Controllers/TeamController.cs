using System.Linq.Expressions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.DFM.Controllers;
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using SqlSugar;

namespace SEFA.DFMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class TeamController : BaseApiController
    {
        /// <summary>
        /// 班组设置
        /// </summary>
        private readonly ITeamServices _teamServices;

        public TeamController(ITeamServices TeamServices)
        {
            _teamServices = TeamServices;
        }

        /// <summary>
        /// 获取集合
        /// </summary>
        /// <param name="teamrequest"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<TeamEntity>>> GetList([FromBody] TeamRequestModel teamrequest)
        {
            var data = await _teamServices.GetList(teamrequest);
            return Success(data, "获取成功");
        }


        /// <summary>
        /// 获取集合
        /// </summary>
        /// <param name="teamrequest"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<TeamEntity>>> GetBOXCodeList([FromBody] TeamRequestModel teamrequest)
        {
            var whereExpression = Expressionable.Create<TeamEntity>().And(a => a.Deleted == 0)
                .AndIF(!string.IsNullOrEmpty(teamrequest.name), a => a.Name.Contains(teamrequest.name))
                .AndIF(!string.IsNullOrEmpty(teamrequest.departmentid),
                    a => a.Departmentid.Contains(teamrequest.departmentid))
                .ToExpression();
            var data = await _teamServices.FindList(whereExpression);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 分页
        /// </summary>
        /// <param name="teamrequest"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<PageModel<TeamEntity>>> GetPageList([FromBody] TeamRequestModel teamrequest)
        {
            Expression<Func<TeamEntity, bool>> whereExpression = a => true;
            var data = await _teamServices.QueryPage(whereExpression, teamrequest.pageIndex, teamrequest.pageSize);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 根据ID 获取
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("{id}")]
        public async Task<MessageModel<TeamEntity>> GetEntity(string id)
        {
            var data = await _teamServices.QueryById(id);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 提交信息
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] TeamViewModel request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                var eqstring = request.EQList.Select(it => it.EquipmentCode).ToArray();
                var thiswhereExpression = Expressionable.Create<TeamEntity>()
                    .And(a => a.Deleted == 0 && eqstring.Contains(a.Departmentid)).ToExpression();
                var thisdata = await _teamServices.FindList(thiswhereExpression);
                List<TeamEntity> teams = new List<TeamEntity>();

                for (int i = 0; i < request.EQList.Count; i++)
                {
                    if (thisdata.Where(it =>
                                it.Departmentid == request.EQList[i].EquipmentCode && it.Name == request.Name).ToList()
                            .Count() <= 0)
                    {
                        TeamEntity team = new TeamEntity();
                        team.Departmentid = request.EQList[i].EquipmentCode;
                        team.Name = request.Name;
                        team.BOXTeamCode = request.BOXTeamCode;
                        team.Shortname = request.Shortname;
                        team.Sequence = request.Sequence;
                        team.Description = request.Description;
                        team.CreateCustomGuid("System");
                        teams.Add(team);
                    }
                }

                data.success = teams.Count() > 0 ? await _teamServices.Add(teams) > 0 : false;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                List<TeamEntity> teams = new List<TeamEntity>();

                for (int i = 0; i < request.EQList.Count; i++)
                {
                    TeamEntity team = new TeamEntity();
                    team.Departmentid = request.EQList[i].EquipmentCode;
                    team.Name = request.Name;
                    team.BOXTeamCode = request.BOXTeamCode;
                    team.Shortname = request.Shortname;
                    team.Sequence = request.Sequence;
                    team.Description = request.Description;
                    team.ID = request.ID;
                    team.ModifyDate = DateTime.Now;
                    team.ModifyUserId = "admin";
                    teams.Add(team);
                }

                data.success = await _teamServices.Update(teams);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _teamServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
}