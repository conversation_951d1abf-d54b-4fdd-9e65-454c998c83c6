using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;
using SqlSugar;

namespace SEFA.DFMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class UnitmanageController : BaseApiController
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly IUnitmanageServices _unitmanageServices;

        public UnitmanageController(IUnitmanageServices UnitmanageServices)
        {
            _unitmanageServices = UnitmanageServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<UnitMangeDto>>> GetList([FromBody] UnitmanageRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<UnitmanageEntity>().And(a => a.Deleted == 0)
              .AndIF(!string.IsNullOrEmpty(reqModel.unitName), a => a.Name.Contains(reqModel.unitName))
              .ToExpression();
            var data = await _unitmanageServices.FindList(whereExpression);
            List<UnitMangeDto> result = new List<UnitMangeDto>();
            foreach (var unit in data)
            {
                result.Add(new UnitMangeDto
                {
                    Value1 = unit.ID,
                    Unit1 = unit.Name,
                    Value2 = unit.ID,
                    Unit2 = unit.Name
                });
            }
            return Success(result, "获取成功");

        }

        [HttpPost]
        public async Task<MessageModel<PageModel<UnitmanageEntity>>> GetPageList([FromBody] UnitmanageRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<UnitmanageEntity>().And(a => a.Deleted == 0)
               .AndIF(!string.IsNullOrEmpty(reqModel.unitName), a => a.Name.Contains(reqModel.unitName))
               .ToExpression();
            var data = await _unitmanageServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return Success(data, "获取成功");

        }

        [HttpGet("{id}")]
        public async Task<MessageModel<UnitmanageEntity>> GetEntity(string id)
        {
            var data = await _unitmanageServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] UnitmanageEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                var exist = await _unitmanageServices.isExist(request);
                if (!exist)
                {
                    return Failed("数据已存在！");
                }

                data.success = await _unitmanageServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _unitmanageServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _unitmanageServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    public class UnitmanageRequestModel : RequestPageModelBase
    {
        public string key { get; set; }
        public string unitName { get; set; }
    }
}