using SEFA.DFM.IServices;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.DFM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;
using SEFA.DFM.Model.ViewModels;
using SqlSugar;
using Newtonsoft.Json;

namespace SEFA.DFMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class UserRoleController : BaseApiController
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly IUserRoleServices _userRoleServices;

        private readonly IAuditLogServices _auditLogServices;

        public UserRoleController(IUserRoleServices UserRoleServices, IAuditLogServices auditLogServices)
        {
            _userRoleServices = UserRoleServices;
            _auditLogServices = auditLogServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<UserRoleEntity>>> GetList([FromBody] UserRoleRequestModel reqModel)
        {
            UserRoleEntity userrole = new UserRoleEntity();
            userrole.RoleId = reqModel.RoleId;

            var data = await _userRoleServices.GetUserRole(userrole);

            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<UserRoleEntity>>> GetPageList([FromBody] UserRoleRequestModel reqModel)
        {
            Expression<Func<UserRoleEntity, bool>> whereExpression = a => true;
            var data = await _userRoleServices.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);
            return Success(data, "获取成功");
        }


        [HttpGet("{id}")]
        public async Task<MessageModel<UserRoleEntity>> GetEntity(string id)
        {
            var data = await _userRoleServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] List<UserRoleEntity> request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request[0].ID))
            {
                //var datarole = await _userRoleServices.GetUserRoleListByRoleId(request[0].RoleId);
                //if (datarole.Count > 0)
                //{
                //    string[] ids = new string[datarole.Count + 1];
                //    for (int i = 0; i < datarole.Count; i++)
                //    {
                //        ids[i] = datarole[i].ID;
                //    }

                //    data.success = await _userRoleServices.DeleteByIds(ids);
                //}

                List<UserRoleEntity> userRoleEntities = new List<UserRoleEntity>();
                for (int i = 0; i < request.Count; i++)
                {
                    UserRoleEntity userRole = new UserRoleEntity();
                    userRole.CreateCustomGuid("System");
                    userRole.RoleId = request[i].RoleId;
                    userRole.UserId = request[i].UserId;
                    userRoleEntities.Add(userRole);
                }

                List<UserRoleEntity> userRoleAll = await _userRoleServices.FindList(a => a.Deleted == 0);

                // 创建一个 HashSet 来存储 userRoleAll 的元素，以提高查找效率
                HashSet<(string UserId, string RoleId)> userRoleAllSet = new HashSet<(string, string)>();
                foreach (var item in userRoleAll)
                {
                    userRoleAllSet.Add((item.UserId, item.RoleId));
                }

                // 使用 List<T>.FindAll 方法来过滤不需要的元素
                userRoleEntities.RemoveAll(entity =>
                {
                    return userRoleAllSet.Contains((entity.UserId, entity.RoleId));
                });

                data.success = await _userRoleServices.Add(userRoleEntities) > 0;
                AuditLogEntity auditLog = _auditLogServices.GetMethodInfo();
                auditLog.MethodName = "UserRole/SaveForm";
                auditLog.ServiceName = "DFM";
                auditLog.MethodRemark = $"新增用户角色";
                auditLog.Parameters = JsonConvert.SerializeObject(request);
                //auditLog.Parameters = JsonConvert.SerializeObject(request).Length > 250 ? JsonConvert.SerializeObject(request).Substring(0, 250) : JsonConvert.SerializeObject(request);
                auditLog.ReturnValue = data.success ? "添加成功" : "添加失败";
                await _auditLogServices.Add(auditLog);
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                for (int i = 0; i < request.Count; i++)
                {
                    UserRoleEntity userRole = new UserRoleEntity();
                    userRole.RoleId = request[i].RoleId;
                    userRole.UserId = request[i].UserId;
                    userRole.ID = request[i].ID;
                    data.success = await _userRoleServices.Update(userRole);
                }

                AuditLogEntity auditLog = _auditLogServices.GetMethodInfo();
                auditLog.MethodName = "UserRole/SaveForm";
                auditLog.ServiceName = "DFM";
                auditLog.MethodRemark = $"更新用户角色";
                auditLog.Parameters = JsonConvert.SerializeObject(request);
                //auditLog.Parameters = JsonConvert.SerializeObject(request).Length > 250 ? JsonConvert.SerializeObject(request).Substring(0, 250) : JsonConvert.SerializeObject(request);
                auditLog.ReturnValue = data.success ? "更新成功" : "更新失败";
                await _auditLogServices.Add(auditLog);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _userRoleServices.DeleteByIds(ids);
            AuditLogEntity auditLog = _auditLogServices.GetMethodInfo();
            auditLog.MethodName = "UserRole/Delete";
            auditLog.ServiceName = "DFM";
            auditLog.MethodRemark = $"删除用户角色";
            auditLog.Parameters = JsonConvert.SerializeObject(ids);
            //auditLog.Parameters = JsonConvert.SerializeObject(ids).Length > 250 ? JsonConvert.SerializeObject(ids).Substring(0, 250) : JsonConvert.SerializeObject(ids);
            auditLog.ReturnValue = data.success ? "删除成功" : "删除失败";
            await _auditLogServices.Add(auditLog);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }

    public class UserRoleRequestModel : RequestPageModelBase
    {
        public string Key { get; set; }
        public string RoleId { get; set; }
        public string UserId { get; set; }
    }
}