using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using SEFA.Base;
using SEFA.Base.Common.Helper;
using SEFA.Base.Common.LogHelper;
using SEFA.Base.Model;
using SEFA.DFM.Common.AdCommon;
using SEFA.DFM.Controllers;
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using SqlSugar;
using System.Text.RegularExpressions;

namespace SEFA.DFMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class UserinfoController : BaseApiController
    {
        /// <summary>
        /// 用户管理
        /// </summary>
        private readonly IUserinfoServices _userinfoServices;

        private readonly IDepartmentServices _departmentServices;
        private readonly IAuditLogServices _auditLogServices;

        private readonly ITeamServices _teamServices;

        private readonly IEquipmentServices _equipmentServices;

        public UserinfoController(IUserinfoServices UserinfoServices, IDepartmentServices departmentServices,
            ITeamServices teamServices,
            IEquipmentServices equipmentServices,
            IAuditLogServices auditLogServices)
        {
            _userinfoServices = UserinfoServices;
            _departmentServices = departmentServices;
            _teamServices = teamServices;
            _equipmentServices = equipmentServices;
            _auditLogServices = auditLogServices;
        }

        /// <summary>
        /// 获取用户列表
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<UserInfoViewModel>>> GetList([FromBody] UserinfoRequestModel reqModel)
        {
            UserinfoEntity userinfo = new UserinfoEntity();
            userinfo.Postid = reqModel.Postid;
            userinfo.Departmentid = reqModel.Departmentid;
            userinfo.UserName = reqModel.UserName;
            userinfo.UserNo = reqModel.UserNo;
            var data = await _userinfoServices.GetUserinfos(userinfo);

            return Success(data, "获取成功");
        }

        /// <summary>
        /// 获取用户列表
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<UserInfoViewModel>>> GetFilteredUsersList([FromBody] UserinfoRequestModel reqModel)
        {
            UserinfoEntity userinfo = new UserinfoEntity();
            userinfo.Postid = reqModel.Postid;
            userinfo.Departmentid = reqModel.Departmentid;
            userinfo.UserName = reqModel.UserName;
            userinfo.UserNo = reqModel.UserNo;
            var data = await _userinfoServices.GetFilteredUsersList(userinfo);

            return Success(data, "获取成功");
        }


        /// <summary>
        /// 获取用户列表
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<UserinfoEntity>>> GetUserList([FromBody] UserinfoRequestModel reqModel)
        {
            UserinfoEntity userinfo = new UserinfoEntity();
            userinfo.Postid = reqModel.Postid;
            userinfo.Departmentid = reqModel.Departmentid;
            userinfo.UserName = reqModel.UserName;
            userinfo.UserNo = reqModel.UserNo;
            var data = await _userinfoServices.GetUserList(userinfo);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 获取用户列表
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<MessageModel<List<UserinfoEntity>>> GetDepartUserList()
        {
            var data = await _userinfoServices.GetDepartUserList();
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 获取用户列表 通过 RoleId
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<UserinfoEntity>>> GetUserListByRoleId(
            [FromBody] UserinfoRequestModel reqModel)
        {
            UserRoleEntity userRole = new UserRoleEntity();
            userRole.RoleId = reqModel.RoleId;
            userRole.UserId = reqModel.UserName;
            var data = await _userinfoServices.GetUserListByRoleId(userRole);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 获取用户列表 分页
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<PageModel<UserInfoViewModel>>> GetPageList(
            [FromBody] UserinfoRequestModel reqModel)
        {
            UserinfoEntity userinfo = new UserinfoEntity();
            userinfo.Postid = reqModel.Departmentid;
            userinfo.Departmentid = reqModel.Departmentid;
            userinfo.DepartmentCode = reqModel.DepartmentCode;
            userinfo.LoginName = reqModel.loginName;
            userinfo.UserName = reqModel.UserName;
            userinfo.UserNo = reqModel.UserNo;
            var data = await _userinfoServices.GetUserinfosPageList(userinfo, reqModel.pageIndex, reqModel.pageSize);

            //return Success(data, "获取成功");
            return new MessageModel<PageModel<UserInfoViewModel>>()
            {
                msg = "获取成功",
                success = true,
                response = data,
            };
        }

        /// <summary>
        /// 设置班组下的用户信息
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> SetTeamInfo([FromBody] List<UserTeamView> reqModel)
        {
            return await _userinfoServices.SetTeamInfo(reqModel);
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<UserinfoEntity>> GetEntity([FromBody] string[] id)
        {
            var data = await _userinfoServices.QueryById(id);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 修改密码
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> UpdatePassword([FromBody] UserInfoViewModel request)
        {
            var data = new MessageModel<string>();
            request.Password = MD5Helper.MD5Encrypt32(request.Password);
            request.OldPassword = MD5Helper.MD5Encrypt32(request.OldPassword);
            var whereExpression = Expressionable.Create<UserinfoEntity>().And(a => a.Deleted == 0)
                .AndIF(!string.IsNullOrEmpty(request.OldPassword), a => a.Password.Contains(request.OldPassword))
                .AndIF(!string.IsNullOrEmpty(request.ID), a => a.ID == request.ID)
                .ToExpression();
            var list = await _userinfoServices.FindList(whereExpression);

            if (list.Count > 0)
            {
                data.success = (await _userinfoServices.UpdatePassWord(request)) > 0;
                AuditLogEntity auditLog = _auditLogServices.GetMethodInfo();
                auditLog.MethodName = "UserInfo/UpdatePassword";
                auditLog.ServiceName = "DFM";
                auditLog.Parameters = JsonConvert.SerializeObject(request);
                auditLog.MethodRemark = $"修改[{request.UserName}]的密码";
                //auditLog.Parameters = JsonConvert.SerializeObject(request).Substring(0, 250);
                auditLog.ReturnValue = data.success ? "更新成功" : "更新失败";
                await _auditLogServices.Add(auditLog);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
            else
            {
                return Failed("原始密码错误");
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> UpdateStatus([FromBody] UserInfoViewModel request)
        {
            var data = new MessageModel<string>();
            data.success = await _userinfoServices.Update(new UserinfoEntity { ID = request.ID, Status = request.Status }, new List<string> { "Status" });
            if (data.success)
            {
                return Success("", "更新成功");
            }
            else
            {
                return Failed("更新失败");
            }
        }

        /// <summary>
        /// 提交角色信息
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] UserInfoViewModel request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.Postid))
            {
                return Failed("请选择组织");
            }

            if (ActiveDirectoryHelper.AccountExists(request.LoginName) == false)
            {
                /*非AD用户,校验密码复杂度*/
                if (string.IsNullOrEmpty(request.Password))
                {
                    return Failed("请输入密码");
                }
                string pattern = @"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{6,}$";
                bool isValid = Regex.IsMatch(request.Password, pattern);
                if (isValid == false)
                {
                    return Failed("密码复杂度过低,密码需包含大小写字母和数字,长度需大于6位");
                }
            }

            if (string.IsNullOrEmpty(request.ID))
            {
                if (request.LEVEL == "Team")
                {
                    TeamEntity teamEntity = await _teamServices.FindEntity(a => a.ID == request.Postid);
                    if (teamEntity != null)
                    {
                        request.Departmentid = teamEntity.BOXTeamCode;
                        if (string.IsNullOrWhiteSpace(teamEntity.Companyid))
                        {
                            if (!string.IsNullOrWhiteSpace(teamEntity.Departmentid))
                            {
                                Dictionary<string, EquipmentEntity> equipmentEntities = await
                                    _equipmentServices.FindEquipmentTreeByEquipmentCode(teamEntity.Departmentid);
                                EquipmentEntity equipmentEntity = new EquipmentEntity();
                                equipmentEntities.TryGetValue("Plant", out equipmentEntity);
                                if (equipmentEntity != null)
                                {
                                    request.Companyid = equipmentEntity.EquipmentCode;
                                }
                            }
                        }
                    }
                }

                if (request.LEVEL == "Plant")
                {
                    EquipmentEntity equipment = await _equipmentServices.FindEntity(a => a.ID == request.Postid);
                    if (equipment != null)
                    {
                        request.Companyid = equipment.EquipmentCode;
                    }
                }

                if (request.LEVEL != "Team" && request.LEVEL != "Plant")
                {
                    request.Departmentid = request.Postid;
                    EquipmentEntity equipment = await _equipmentServices.FindEntity(a => a.ID == request.Postid);
                    if (equipment != null)
                    {
                        request.Departmentid = equipment.EquipmentCode;
                    }

                    if (!string.IsNullOrWhiteSpace(request.Departmentid))
                    {
                        Dictionary<string, EquipmentEntity> equipmentEntities = await
                            _equipmentServices.FindEquipmentTreeByEquipmentCode(request.Departmentid);
                        EquipmentEntity equipmentEntity = new EquipmentEntity();
                        equipmentEntities.TryGetValue("Plant", out equipmentEntity);
                        if (equipmentEntity != null)
                        {
                            request.Companyid = equipmentEntity.EquipmentCode;
                        }
                    }
                }

                UserinfoEntity userinfoEntity = new UserinfoEntity();
                string json = JsonConvert.SerializeObject(request);
                userinfoEntity = JsonConvert.DeserializeObject<UserinfoEntity>(json);

               
                

                //通过 Logname 和 UserNo 验证是否存在相同信息
                var isex = await _userinfoServices.isExist(userinfoEntity);
                if (isex == true)
                {
                    return Failed("请勿重复添加");
                }

                userinfoEntity.Password = MD5Helper.MD5Encrypt32(string.IsNullOrWhiteSpace(userinfoEntity.Password) ? "Mes@123456" : userinfoEntity.Password);
                data.success = await _userinfoServices.Add(userinfoEntity) > 0;
                AuditLogEntity auditLog = _auditLogServices.GetMethodInfo();
                auditLog.MethodName = "UserInfo/SaveForm";
                auditLog.ServiceName = "DFM";
                auditLog.Parameters = JsonConvert.SerializeObject(request);
                auditLog.MethodRemark = $"添加用户[{request.UserName}]";
                //auditLog.Parameters = JsonConvert.SerializeObject(request).Length > 250 ? JsonConvert.SerializeObject(request).Substring(0, 250) : JsonConvert.SerializeObject(request);
                auditLog.ReturnValue = data.success ? "添加成功" : "添加失败";
                await _auditLogServices.Add(auditLog);
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                if (request.LEVEL == "Team")
                {
                    TeamEntity teamEntity = await _teamServices.FindEntity(a => a.ID == request.Postid);
                    if (teamEntity != null)
                    {
                        request.Departmentid = teamEntity.BOXTeamCode;
                        if (string.IsNullOrWhiteSpace(teamEntity.Companyid))
                        {
                            if (!string.IsNullOrWhiteSpace(teamEntity.Departmentid))
                            {
                                Dictionary<string, EquipmentEntity> equipmentEntities = await
                                    _equipmentServices.FindEquipmentTreeByEquipmentCode(teamEntity.Departmentid);
                                EquipmentEntity equipmentEntity = new EquipmentEntity();
                                equipmentEntities.TryGetValue("Plant", out equipmentEntity);
                                if (equipmentEntity != null)
                                {
                                    request.Companyid = equipmentEntity.EquipmentCode;
                                }
                            }
                        }
                    }
                }

                if (request.LEVEL == "Plant")
                {
                    EquipmentEntity equipment = await _equipmentServices.FindEntity(a => a.ID == request.Postid);
                    if (equipment != null)
                    {
                        request.Companyid = equipment.EquipmentCode;
                    }
                }

                if (request.LEVEL != "Team" && request.LEVEL != "Plant")
                {
                    request.Departmentid = request.Postid;
                    EquipmentEntity equipment = await _equipmentServices.FindEntity(a => a.ID == request.Postid);
                    if (equipment != null)
                    {
                        request.Departmentid = equipment.EquipmentCode;
                    }

                    if (!string.IsNullOrWhiteSpace(request.Departmentid))
                    {
                        Dictionary<string, EquipmentEntity> equipmentEntities = await
                            _equipmentServices.FindEquipmentTreeByEquipmentCode(request.Departmentid);
                        EquipmentEntity equipmentEntity = new EquipmentEntity();
                        if (equipmentEntities != null)
                        {
                            equipmentEntities.TryGetValue("Plant", out equipmentEntity);
                            request.Companyid = equipmentEntity.EquipmentCode;
                        }
                    }
                }

                UserinfoEntity userinfoEntity = new UserinfoEntity();
                string json = JsonConvert.SerializeObject(request);
                userinfoEntity = JsonConvert.DeserializeObject<UserinfoEntity>(json);
                userinfoEntity.Password = MD5Helper.MD5Encrypt32(string.IsNullOrWhiteSpace(userinfoEntity.Password) ? "Mes@123456" : userinfoEntity.Password);
                data.success = await _userinfoServices.UpdateData(userinfoEntity) > 0;
                AuditLogEntity auditLog = _auditLogServices.GetMethodInfo();
                auditLog.MethodName = "UserInfo/SaveForm";
                auditLog.ServiceName = "DFM";
                auditLog.Parameters = JsonConvert.SerializeObject(request);
                //auditLog.Parameters = JsonConvert.SerializeObject(request).Length > 250 ? JsonConvert.SerializeObject(request).Substring(0, 250) : JsonConvert.SerializeObject(request);
                auditLog.MethodRemark = $"修改用户信息[{request.UserName}]";
                auditLog.ReturnValue = data.success ? "更新成功" : "更新失败";
                await _auditLogServices.Add(auditLog);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            var userNameStr = "";
            var userInfoList = await _userinfoServices.QueryByIDs(ids);
            if (userInfoList.Count > 0)
            {
                userNameStr = string.Join(",", userInfoList.Select(a => a.UserName).ToArray());
            }

            data.success = await _userinfoServices.DeleteByIds(ids);
            AuditLogEntity auditLog = _auditLogServices.GetMethodInfo();
            auditLog.MethodName = "UserInfo/Delete";
            auditLog.ServiceName = "DFM";
            auditLog.Parameters = JsonConvert.SerializeObject(ids);
            auditLog.MethodRemark = $"删除用户[{userNameStr}]信息";
            //auditLog.Parameters = JsonConvert.SerializeObject(ids).Length > 250 ? JsonConvert.SerializeObject(ids).Substring(0, 250) : JsonConvert.SerializeObject(ids);
            auditLog.ReturnValue = data.success ? "删除成功" : "删除失败";
            await _auditLogServices.Add(auditLog);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }

        /// <summary>
        /// 导入用户
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel2<string>> ImportBatchUser([FromBody] List<UserInfoImportModel> request)
        {
            return await _userinfoServices.ImportBatchUser(request);
        }

        /// <summary>
        /// 导入用户
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel2<string>> ImportUser([FromBody] UserInfoImportModel request)
        {
            SerilogServer.LogDebug($"接收到用户同步信息:[{JsonConvert.SerializeObject(request)}]", "人员同步日志");
            return await _userinfoServices.ImportUser(request);
        }
    }

    public class UserinfoRequestModel : RequestPageModelBase
    {
        public string key { get; set; }
        public string Postid { get; set; }
        public string Departmentid { get; set; }
        public string DepartmentCode { get; set; }
        public string RoleId { get; set; }
        public string UserName { get; set; }
        public string UserNo { get; set; }
        public string loginName { get; set; }
    }
}