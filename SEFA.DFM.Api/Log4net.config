<?xml version="1.0" encoding="utf-8"?>
<log4net>
	<!--文件记录 begin######################################################################################################################################## -->
	<!-- 将日志以回滚文件的形式写到文件中 -->
	<!-- 按日期切分日志文件，并将日期作为日志文件的名字 -->
	<!--Error-->
	<appender name="ErrorLog" type="log4net.Appender.RollingFileAppender">
		<!--不加utf-8编码格式，中文字符将显示成乱码-->
		<param name="Encoding" value="utf-8" />
		<file value="Log\\"/>
		<appendToFile value="true" />
		<rollingStyle value="Date" />
		<!--日期的格式，每天换一个文件记录，如不设置则永远只记录一天的日志，需设置-->
		<datePattern value="&quot;GlobalExceptionLogs_&quot;yyyyMMdd&quot;.log&quot;" />
		<!--日志文件名是否为静态-->
		<StaticLogFileName value="false"/>
		<!--多线程时采用最小锁定-->
		<lockingModel type="log4net.Appender.FileAppender+MinimalLock" />
		<!--布局（向用户显示最后经过格式化的输出信息）-->
		<layout type="log4net.Layout.PatternLayout">
			<conversionPattern value="%date| %-5level %newline%message%newline--------------------------------%newline" />
		</layout>
		<filter type="log4net.Filter.LevelRangeFilter">
			<levelMin value="ERROR" />
			<levelMax value="FATAL" />
		</filter>
	</appender>
	<!--Error-->
	<!--Info-->
	<appender name="InfoLog" type="log4net.Appender.RollingFileAppender">
		<!--不加utf-8编码格式，中文字符将显示成乱码-->
		<param name="Encoding" value="utf-8" />
		<!--定义文件存放位置-->
		<file value="Log\\"/>
		<appendToFile value="true" />
		<rollingStyle value="Date" />
		<!--日志文件名是否为静态-->
		<StaticLogFileName value="false"/>
		<!--日期的格式，每天换一个文件记录，如不设置则永远只记录一天的日志，需设置-->
		<datePattern value="&quot;GlobalInfoLogs_&quot;yyyyMMdd&quot;.log&quot;" />
		<!--多线程时采用最小锁定-->
		<lockingModel type="log4net.Appender.FileAppender+MinimalLock" />
		<!--布局（向用户显示最后经过格式化的输出信息）-->
		<layout type="log4net.Layout.PatternLayout">
			<conversionPattern value="%date| %-5level%c %newline%message%newline--------------------------------%newline" />
		</layout>
		<filter type="log4net.Filter.LevelRangeFilter">
			<levelMin value="DEBUG" />
			<levelMax value="WARN" />
		</filter>
	</appender>
	<!--Info-->
	<!--文件记录 end######################################################################################################################################## -->

	<!--Mogodb记录 begin######################################################################################################################################## -->
	<!--IP访问记录,请求资源记录-->
	<appender name="MongoDBAppenderinfo" type="Log4Mongo.MongoDBAppender, Log4Mongo">
		<!--填写连接方式 格式 数据库:密码@地址-->
		<connectionString value="mongodb://xxxx:<EMAIL>/?socketTimeout=1s" />

		<connectionStringName value="mongo-log4net" />

		<certificateFriendlyName value="Certificate Friendly Name"/>

		<expireAfterSeconds value="2592000" />

		<collectionName value="AOPSQLLOG" />



		<field>
			<name value="timestamp" />
			<layout type="log4net.Layout.RawTimeStampLayout" />
		</field>
		<field>
			<name value="level" />
			<layout type="log4net.Layout.PatternLayout" value="%level" />
		</field>
		<field>
			<name value="thread" />
			<layout type="log4net.Layout.PatternLayout" value="%thread" />
		</field>
		<field>
			<name value="logger" />
			<layout type="log4net.Layout.PatternLayout" value="%logger" />
		</field>
		<field>
			<name value="message" />
			<layout type="log4net.Layout.PatternLayout" value="%message" />
		</field>
		<field>
			<name value="mycustomproperty" />
			<layout type="log4net.Layout.RawPropertyLayout">
				<key value="mycustomproperty" />
			</layout>
		</field>
		<layout type="log4net.Layout.PatternLayout">
			<conversionPattern value=" &#13;&#10; &#13;&#10;【日志时间】： %d 【线程ID】： %thread &#13;&#10;【日志级别】： %-5p &#13;&#10;【日志对象】：%logger &#13;&#10;【日志内容】：%m &#13;&#10; " />
		</layout>
		<filter type="log4net.Filter.LevelRangeFilter">
			<levelMin value="INFO" />
			<levelMax value="INFO" />
		</filter>
	</appender>
	<!--访问结果记录,Sql记录-->
	<appender name="MongoDBAppenderdebug" type="Log4Mongo.MongoDBAppender, Log4Mongo">
		<!--填写连接方式 格式 数据库:密码@地址-->
		<connectionString value="mongodb://xxxx:<EMAIL>/?socketTimeout=1s" />

		<connectionStringName value="mongo-log4net" />

		<certificateFriendlyName value="Certificate Friendly Name"/>

		<expireAfterSeconds value="2592000" />

		<collectionName value="IPACCESSLOG" />



		<field>
			<name value="timestamp" />
			<layout type="log4net.Layout.RawTimeStampLayout" />
		</field>
		<field>
			<name value="level" />
			<layout type="log4net.Layout.PatternLayout" value="%level" />
		</field>
		<field>
			<name value="thread" />
			<layout type="log4net.Layout.PatternLayout" value="%thread" />
		</field>
		<field>
			<name value="logger" />
			<layout type="log4net.Layout.PatternLayout" value="%logger" />
		</field>
		<field>
			<name value="message" />
			<layout type="log4net.Layout.PatternLayout" value="%message" />
		</field>
		<field>
			<name value="mycustomproperty" />
			<layout type="log4net.Layout.RawPropertyLayout">
				<key value="mycustomproperty" />
			</layout>
		</field>
		<layout type="log4net.Layout.PatternLayout">
			<conversionPattern value=" &#13;&#10; &#13;&#10;【日志时间】： %d 【线程ID】： %thread &#13;&#10;【日志级别】： %-5p &#13;&#10;【日志对象】：%logger &#13;&#10;【日志内容】：%m &#13;&#10; " />
		</layout>
		<filter type="log4net.Filter.LevelRangeFilter">
			<levelMin value="DEBUG" />
			<levelMax value="DEBUG" />
		</filter>
	</appender>
	<!--访问结果错误记录-->
	<appender name="MongoDBAppendererror" type="Log4Mongo.MongoDBAppender, Log4Mongo">
		<!--填写连接方式 格式 数据库:密码@地址-->
		<connectionString value="mongodb://xxxx:<EMAIL>/?socketTimeout=1s" />

		<connectionStringName value="mongo-log4net" />

		<certificateFriendlyName value="Certificate Friendly Name"/>

		<expireAfterSeconds value="2592000" />

		<collectionName value="AOPERRORLOG" />

		<!--<newCollectionMaxSize value='999999' />
		<newCollectionMaxDocs value='999999' />-->

		<field>
			<name value="timestamp" />
			<layout type="log4net.Layout.RawTimeStampLayout" />
		</field>
		<field>
			<name value="level" />
			<layout type="log4net.Layout.PatternLayout" value="%level" />
		</field>
		<field>
			<name value="thread" />
			<layout type="log4net.Layout.PatternLayout" value="%thread" />
		</field>
		<field>
			<name value="logger" />
			<layout type="log4net.Layout.PatternLayout" value="%logger" />
		</field>
		<field>
			<name value="message" />
			<layout type="log4net.Layout.PatternLayout" value="%message" />
		</field>
		<field>
			<name value="mycustomproperty" />
			<layout type="log4net.Layout.RawPropertyLayout">
				<key value="mycustomproperty" />
			</layout>
		</field>
		<layout type="log4net.Layout.PatternLayout">
			<conversionPattern value=" &#13;&#10; &#13;&#10;【日志时间】： %d 【线程ID】： %thread &#13;&#10;【日志级别】： %-5p &#13;&#10;【日志对象】：%logger &#13;&#10;【日志内容】：%m &#13;&#10; " />
		</layout>
		<filter type="log4net.Filter.LevelRangeFilter">
			<levelMin value="ERROR" />
			<levelMax value="FATAL" />
		</filter>
	</appender>
	<!--Mogodb记录 end######################################################################################################################################## -->

	<!--MSSQL记录 begin######################################################################################################################################## -->
	<!--访问结果错误记录-->
	<appender name="AdoNetAppenderError" type="MicroKnights.Logging.AdoNetAppender, MicroKnights.Log4NetAdoNetAppender">
		<bufferSize value="1" />
		<connectionType value="Microsoft.Data.SqlClient.SqlConnection, Microsoft.Data.SqlClient, Version=*******,Culture=neutral,PublicKeyToken=23ec7fc2d6eaa4a5"/>
		<connectionStringName value="sqlserver" />
		<connectionStringFile value="connectionstrings.json" />
		<!--两者二选一(connectionStringName/connectionStringFile)，使用上面的方式需要添加一个connectionstrings.json文件-->
		<!--<connectionString value="Server=xxxxxx;Database=xxxxxx;User Id=;Password=;" />-->
		<commandText value="INSERT INTO GblLogError ([Date],[Thread],[Level],[Logger],[Message],[Exception]) VALUES (@log_date, @thread, @log_level, @logger, @message, @exception)" />
		<parameter>
			<parameterName value="@log_date" />
			<dbType value="DateTime" />
			<layout type="log4net.Layout.RawTimeStampLayout" />
		</parameter>
		<parameter>
			<parameterName value="@thread" />
			<dbType value="String" />
			<size value="255" />
			<layout type="log4net.Layout.PatternLayout">
				<conversionPattern value="%thread" />
			</layout>
		</parameter>
		<parameter>
			<parameterName value="@log_level" />
			<dbType value="String" />
			<size value="50" />
			<layout type="log4net.Layout.PatternLayout">
				<conversionPattern value="%level" />
			</layout>
		</parameter>
		<parameter>
			<parameterName value="@logger" />
			<dbType value="String" />
			<size value="255" />
			<layout type="log4net.Layout.PatternLayout">
				<conversionPattern value="%logger" />
			</layout>
		</parameter>
		<parameter>
			<parameterName value="@number" />
			<dbType value="String" />
			<size value="20" />
			<layout type="log4net.Layout.PatternLayout">
				<conversionPattern value="%property{number}" />
			</layout>
		</parameter>
		<parameter>
			<parameterName value="@message" />
			<dbType value="String" />
			<size value="4000" />
			<layout type="log4net.Layout.PatternLayout">
				<conversionPattern value="%message" />
			</layout>
		</parameter>
		<parameter>
			<parameterName value="@exception" />
			<dbType value="String" />
			<size value="4000" />
			<layout type="log4net.Layout.ExceptionLayout" />
		</parameter>
		<filter type="log4net.Filter.LevelRangeFilter">
			<levelMin value="ERROR" />
			<levelMax value="FATAL" />
		</filter>
	</appender>
	<!--访问结果记录,Sql记录-->
	<appender name="AdoNetAppenderDebug" type="MicroKnights.Logging.AdoNetAppender, MicroKnights.Log4NetAdoNetAppender">
		<bufferSize value="1" />
		<connectionType value="Microsoft.Data.SqlClient.SqlConnection, Microsoft.Data.SqlClient, Version=*******,Culture=neutral,PublicKeyToken=23ec7fc2d6eaa4a5"/>
		<connectionStringName value="sqlserver" />
		<connectionStringFile value="connectionstrings.json" />
		<!--两者二选一(connectionStringName/connectionStringFile)，使用上面的方式需要添加一个connectionstrings.json文件-->
		<!--<connectionString value="Server=xxxxxx;Database=xxxxxx;User Id=;Password=;" />-->
		<commandText value="INSERT INTO GblLogDebug ([Date],[Thread],[Level],[Logger],[Message],[Exception]) VALUES (@log_date, @thread, @log_level, @logger, @message, @exception)" />
		<parameter>
			<parameterName value="@log_date" />
			<dbType value="DateTime" />
			<layout type="log4net.Layout.RawTimeStampLayout" />
		</parameter>
		<parameter>
			<parameterName value="@thread" />
			<dbType value="String" />
			<size value="255" />
			<layout type="log4net.Layout.PatternLayout">
				<conversionPattern value="%thread" />
			</layout>
		</parameter>
		<parameter>
			<parameterName value="@log_level" />
			<dbType value="String" />
			<size value="50" />
			<layout type="log4net.Layout.PatternLayout">
				<conversionPattern value="%level" />
			</layout>
		</parameter>
		<parameter>
			<parameterName value="@logger" />
			<dbType value="String" />
			<size value="255" />
			<layout type="log4net.Layout.PatternLayout">
				<conversionPattern value="%logger" />
			</layout>
		</parameter>
		<parameter>
			<parameterName value="@number" />
			<dbType value="String" />
			<size value="20" />
			<layout type="log4net.Layout.PatternLayout">
				<conversionPattern value="%property{number}" />
			</layout>
		</parameter>
		<parameter>
			<parameterName value="@message" />
			<dbType value="String" />
			<size value="4000" />
			<layout type="log4net.Layout.PatternLayout">
				<conversionPattern value="%message" />
			</layout>
		</parameter>
		<parameter>
			<parameterName value="@exception" />
			<dbType value="String" />
			<size value="4000" />
			<layout type="log4net.Layout.ExceptionLayout" />
		</parameter>
		<filter type="log4net.Filter.LevelRangeFilter">
			<levelMin value="DEBUG" />
			<levelMax value="DEBUG" />
		</filter>
	</appender>
	<!--IP访问记录,请求资源记录-->
	<appender name="AdoNetAppenderInfo" type="MicroKnights.Logging.AdoNetAppender, MicroKnights.Log4NetAdoNetAppender">
		<bufferSize value="1" />
		<connectionType value="Microsoft.Data.SqlClient.SqlConnection, Microsoft.Data.SqlClient, Version=*******,Culture=neutral,PublicKeyToken=23ec7fc2d6eaa4a5"/>
		<connectionStringName value="sqlserver" />
		<connectionStringFile value="connectionstrings.json" />
		<!--两者二选一(connectionStringName/connectionStringFile)，使用上面的方式需要添加一个connectionstrings.json文件-->
		<!--<connectionString value="Server=xxxxxx;Database=xxxxxx;User Id=;Password=;" />-->
		<commandText value="INSERT INTO GblLogInfo ([Date],[Thread],[Level],[Logger],[Message],[Exception]) VALUES (@log_date, @thread, @log_level, @logger, @message, @exception)" />
		<parameter>
			<parameterName value="@log_date" />
			<dbType value="DateTime" />
			<layout type="log4net.Layout.RawTimeStampLayout" />
		</parameter>
		<parameter>
			<parameterName value="@thread" />
			<dbType value="String" />
			<size value="255" />
			<layout type="log4net.Layout.PatternLayout">
				<conversionPattern value="%thread" />
			</layout>
		</parameter>
		<parameter>
			<parameterName value="@log_level" />
			<dbType value="String" />
			<size value="50" />
			<layout type="log4net.Layout.PatternLayout">
				<conversionPattern value="%level" />
			</layout>
		</parameter>
		<parameter>
			<parameterName value="@logger" />
			<dbType value="String" />
			<size value="255" />
			<layout type="log4net.Layout.PatternLayout">
				<conversionPattern value="%logger" />
			</layout>
		</parameter>
		<parameter>
			<parameterName value="@number" />
			<dbType value="String" />
			<size value="20" />
			<layout type="log4net.Layout.PatternLayout">
				<conversionPattern value="%property{number}" />
			</layout>
		</parameter>
		<parameter>
			<parameterName value="@message" />
			<dbType value="String" />
			<size value="4000" />
			<layout type="log4net.Layout.PatternLayout">
				<conversionPattern value="%message" />
			</layout>
		</parameter>
		<parameter>
			<parameterName value="@exception" />
			<dbType value="String" />
			<size value="4000" />
			<layout type="log4net.Layout.ExceptionLayout" />
		</parameter>
		<filter type="log4net.Filter.LevelRangeFilter">
			<levelMin value="INFO" />
			<levelMax value="INFO" />
		</filter>
	</appender>
	<!--MSSQL记录 end######################################################################################################################################## -->

	<!--启用记录器-->
	<root>
		<!-- 控制级别，由低到高：ALL|DEBUG|INFO|WARN|ERROR|FATAL|OFF -->
		<!-- 比如定义级别为INFO，则INFO级别向下的级别，比如DEBUG日志将不会被记录 -->
		<!-- 如果没有定义LEVEL的值，则缺省为DEBUG -->
		<level value="ALL" />


		<!-- 按日期切分日志文件，并将日期作为日志文件的名字 -->
		<!--文件日志、暂不使用-->
		<!--<appender-ref ref="ErrorLog" />-->
		<!--<appender-ref ref="InfoLog" />-->

		<!--MongoDB日志、暂不使用-->
		<!--<appender-ref ref="MongoDBAppenderinfo" />
		<appender-ref ref="MongoDBAppenderdebug" />
		<appender-ref ref="MongoDBAppendererror" />-->

		<!--MSSQL日志-->
		<appender-ref ref="AdoNetAppenderError" />
		<appender-ref ref="AdoNetAppenderDebug" />
		<appender-ref ref="AdoNetAppenderInfo" />
	</root>
	<!--启用记录器-->
</log4net>

<!--建表语句 Mongo不用建表，文件不需要，MSSQL建表语句如下-->
<!-- 建表语句 Mongo不用建表，文件不需要，MSSQL建表语句如下

/*
 Navicat Premium Data Transfer

 Source Server         : Nine 何拾玖
 Source Server Type    : SQL Server
 Source Server Version : 14001000
 Source Host           : fangding.picp
 Source Catalog        : WitCloud_pdman
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 14001000
 File Encoding         : 65001

 Date: 15/10/2021 14:54:44
*/

CREATE TABLE GblLogError(
    Id INT NOT NULL IDENTITY(1,1)   ,
    Date DATETIME NOT NULL    ,
    Thread VARCHAR(255) NOT NULL    ,
    Level VARCHAR(50) NOT NULL    ,
    Logger VARCHAR(255) NOT NULL    ,
    Message VARCHAR(4000) NOT NULL    ,
    Exception VARCHAR(2000)     ,
    CONSTRAINT PK_GblLogError PRIMARY KEY CLUSTERED (Id ASC ) ON [PRIMARY] 
) ;;

EXECUTE sp_addextendedproperty N'MS_Description', '错误日志记录表', N'user', N'dbo', N'table', N'GblLogError', NULL, NULL;;
EXECUTE sp_addextendedproperty N'MS_Description', 'ID', N'user', N'dbo', N'table', N'GblLogError', N'column', N'Id';;
EXECUTE sp_addextendedproperty N'MS_Description', '时间', N'user', N'dbo', N'table', N'GblLogError', N'column', N'Date';;
EXECUTE sp_addextendedproperty N'MS_Description', '线程', N'user', N'dbo', N'table', N'GblLogError', N'column', N'Thread';;
EXECUTE sp_addextendedproperty N'MS_Description', '等级', N'user', N'dbo', N'table', N'GblLogError', N'column', N'Level';;
EXECUTE sp_addextendedproperty N'MS_Description', '记录器', N'user', N'dbo', N'table', N'GblLogError', N'column', N'Logger';;
EXECUTE sp_addextendedproperty N'MS_Description', '错误信息', N'user', N'dbo', N'table', N'GblLogError', N'column', N'Message';;
EXECUTE sp_addextendedproperty N'MS_Description', '异常', N'user', N'dbo', N'table', N'GblLogError', N'column', N'Exception';;

CREATE TABLE GblLogDebug(
    Id INT NOT NULL IDENTITY(1,1)   ,
    Date DATETIME NOT NULL    ,
    Thread VARCHAR(255) NOT NULL    ,
    Level VARCHAR(50) NOT NULL    ,
    Logger VARCHAR(255) NOT NULL    ,
    Message VARCHAR(4000) NOT NULL    ,
    Exception VARCHAR(2000)     ,
    CONSTRAINT PK_GblLogDebug PRIMARY KEY CLUSTERED (Id ASC ) ON [PRIMARY] 
) ;;

EXECUTE sp_addextendedproperty N'MS_Description', '调试日志记录表', N'user', N'dbo', N'table', N'GblLogDebug', NULL, NULL;;
EXECUTE sp_addextendedproperty N'MS_Description', 'ID', N'user', N'dbo', N'table', N'GblLogDebug', N'column', N'Id';;
EXECUTE sp_addextendedproperty N'MS_Description', '时间', N'user', N'dbo', N'table', N'GblLogDebug', N'column', N'Date';;
EXECUTE sp_addextendedproperty N'MS_Description', '线程', N'user', N'dbo', N'table', N'GblLogDebug', N'column', N'Thread';;
EXECUTE sp_addextendedproperty N'MS_Description', '等级', N'user', N'dbo', N'table', N'GblLogDebug', N'column', N'Level';;
EXECUTE sp_addextendedproperty N'MS_Description', '记录器', N'user', N'dbo', N'table', N'GblLogDebug', N'column', N'Logger';;
EXECUTE sp_addextendedproperty N'MS_Description', '错误信息', N'user', N'dbo', N'table', N'GblLogDebug', N'column', N'Message';;
EXECUTE sp_addextendedproperty N'MS_Description', '异常', N'user', N'dbo', N'table', N'GblLogDebug', N'column', N'Exception';;

CREATE TABLE GblLogInfo(
    Id INT NOT NULL IDENTITY(1,1)   ,
    Date DATETIME NOT NULL    ,
    Thread VARCHAR(255) NOT NULL    ,
    Level VARCHAR(50) NOT NULL    ,
    Logger VARCHAR(255) NOT NULL    ,
    Message VARCHAR(4000) NOT NULL    ,
    Exception VARCHAR(2000)     ,
    CONSTRAINT PK_GblLogInfo PRIMARY KEY CLUSTERED (Id ASC ) ON [PRIMARY] 
) ;;

EXECUTE sp_addextendedproperty N'MS_Description', '信息日志记录表', N'user', N'dbo', N'table', N'GblLogInfo', NULL, NULL;;
EXECUTE sp_addextendedproperty N'MS_Description', 'ID', N'user', N'dbo', N'table', N'GblLogInfo', N'column', N'Id';;
EXECUTE sp_addextendedproperty N'MS_Description', '时间', N'user', N'dbo', N'table', N'GblLogInfo', N'column', N'Date';;
EXECUTE sp_addextendedproperty N'MS_Description', '线程', N'user', N'dbo', N'table', N'GblLogInfo', N'column', N'Thread';;
EXECUTE sp_addextendedproperty N'MS_Description', '等级', N'user', N'dbo', N'table', N'GblLogInfo', N'column', N'Level';;
EXECUTE sp_addextendedproperty N'MS_Description', '记录器', N'user', N'dbo', N'table', N'GblLogInfo', N'column', N'Logger';;
EXECUTE sp_addextendedproperty N'MS_Description', '错误信息', N'user', N'dbo', N'table', N'GblLogInfo', N'column', N'Message';;
EXECUTE sp_addextendedproperty N'MS_Description', '异常', N'user', N'dbo', N'table', N'GblLogInfo', N'column', N'Exception';;


-->