2025-05-15 14:55:22.462 +08:00 [ERR] 
AutoMapper.AutoMapperMappingException: Missing type map configuration or unsupported mapping.

Mapping types:
SopDirEntity -> SopDirDto
SEFA.DFM.Model.Models.SopDirEntity -> SEFA.DFM.Model.ViewModels.SopDirDto
   at lambda_method702(Closure , SopDirEntity , SopDirDto , ResolutionContext )
   at lambda_method701(Closure , Object , List`1 , ResolutionContext )
2025-05-15 14:55:22.681 +08:00 [ERR] Error mapping types.

Mapping types:
Object -> List`1
System.Object -> System.Collections.Generic.List`1[[SEFA.DFM.Model.ViewModels.SopDirDto, SEFA.DFM.Model, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]
【自定义错误】：Error mapping types.

Mapping types:
Object -> List`1
System.Object -> System.Collections.Generic.List`1[[SEFA.DFM.Model.ViewModels.SopDirDto, SEFA.DFM.Model, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]] 
【异常类型】：AutoMapperMappingException 
【异常信息】：Error mapping types.

Mapping types:
Object -> List`1
System.Object -> System.Collections.Generic.List`1[[SEFA.DFM.Model.ViewModels.SopDirDto, SEFA.DFM.Model, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]] 
【堆栈调用】：   at lambda_method701(Closure , Object , List`1 , ResolutionContext )
   at SEFA.DFM.Services.SopDirServices.GetAllDirs() in C:\work\syngentagroup\SEFA_XZD\SEFA.DFM\SEFA.DFM.Services\SopDirServices.cs:line 154
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.DFMApi.Controllers.SopDirController.GetTreeList(String parentId) in C:\work\syngentagroup\SEFA_XZD\SEFA.DFM\SEFA.DFM.Api\Controllers\SopDirController.cs:line 115
   at lambda_method673(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
AutoMapper.AutoMapperMappingException: Error mapping types.

Mapping types:
Object -> List`1
System.Object -> System.Collections.Generic.List`1[[SEFA.DFM.Model.ViewModels.SopDirDto, SEFA.DFM.Model, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]
 ---> AutoMapper.AutoMapperMappingException: Missing type map configuration or unsupported mapping.

Mapping types:
SopDirEntity -> SopDirDto
SEFA.DFM.Model.Models.SopDirEntity -> SEFA.DFM.Model.ViewModels.SopDirDto
   at lambda_method702(Closure , SopDirEntity , SopDirDto , ResolutionContext )
   at lambda_method701(Closure , Object , List`1 , ResolutionContext )
   --- End of inner exception stack trace ---
   at lambda_method701(Closure , Object , List`1 , ResolutionContext )
   at SEFA.DFM.Services.SopDirServices.GetAllDirs() in C:\work\syngentagroup\SEFA_XZD\SEFA.DFM\SEFA.DFM.Services\SopDirServices.cs:line 154
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.DFMApi.Controllers.SopDirController.GetTreeList(String parentId) in C:\work\syngentagroup\SEFA_XZD\SEFA.DFM\SEFA.DFM.Api\Controllers\SopDirController.cs:line 115
   at lambda_method673(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
2025-05-15 14:55:57.726 +08:00 [ERR] 
AutoMapper.AutoMapperMappingException: Missing type map configuration or unsupported mapping.

Mapping types:
SopDirEntity -> SopDirDto
SEFA.DFM.Model.Models.SopDirEntity -> SEFA.DFM.Model.ViewModels.SopDirDto
   at lambda_method702(Closure , SopDirEntity , SopDirDto , ResolutionContext )
   at lambda_method701(Closure , Object , List`1 , ResolutionContext )
2025-05-15 14:55:57.844 +08:00 [ERR] Error mapping types.

Mapping types:
Object -> List`1
System.Object -> System.Collections.Generic.List`1[[SEFA.DFM.Model.ViewModels.SopDirDto, SEFA.DFM.Model, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]
【自定义错误】：Error mapping types.

Mapping types:
Object -> List`1
System.Object -> System.Collections.Generic.List`1[[SEFA.DFM.Model.ViewModels.SopDirDto, SEFA.DFM.Model, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]] 
【异常类型】：AutoMapperMappingException 
【异常信息】：Error mapping types.

Mapping types:
Object -> List`1
System.Object -> System.Collections.Generic.List`1[[SEFA.DFM.Model.ViewModels.SopDirDto, SEFA.DFM.Model, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]] 
【堆栈调用】：   at lambda_method701(Closure , Object , List`1 , ResolutionContext )
   at SEFA.DFM.Services.SopDirServices.GetAllDirs() in C:\work\syngentagroup\SEFA_XZD\SEFA.DFM\SEFA.DFM.Services\SopDirServices.cs:line 154
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.DFMApi.Controllers.SopDirController.GetTreeList(String parentId) in C:\work\syngentagroup\SEFA_XZD\SEFA.DFM\SEFA.DFM.Api\Controllers\SopDirController.cs:line 115
   at lambda_method673(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
AutoMapper.AutoMapperMappingException: Error mapping types.

Mapping types:
Object -> List`1
System.Object -> System.Collections.Generic.List`1[[SEFA.DFM.Model.ViewModels.SopDirDto, SEFA.DFM.Model, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]
 ---> AutoMapper.AutoMapperMappingException: Missing type map configuration or unsupported mapping.

Mapping types:
SopDirEntity -> SopDirDto
SEFA.DFM.Model.Models.SopDirEntity -> SEFA.DFM.Model.ViewModels.SopDirDto
   at lambda_method702(Closure , SopDirEntity , SopDirDto , ResolutionContext )
   at lambda_method701(Closure , Object , List`1 , ResolutionContext )
   --- End of inner exception stack trace ---
   at lambda_method701(Closure , Object , List`1 , ResolutionContext )
   at SEFA.DFM.Services.SopDirServices.GetAllDirs() in C:\work\syngentagroup\SEFA_XZD\SEFA.DFM\SEFA.DFM.Services\SopDirServices.cs:line 154
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.DFMApi.Controllers.SopDirController.GetTreeList(String parentId) in C:\work\syngentagroup\SEFA_XZD\SEFA.DFM\SEFA.DFM.Api\Controllers\SopDirController.cs:line 115
   at lambda_method673(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
2025-05-15 15:03:35.694 +08:00 [ERR] 
AutoMapper.AutoMapperMappingException: Missing type map configuration or unsupported mapping.

Mapping types:
SopDirEntity -> SopDirDto
SEFA.DFM.Model.Models.SopDirEntity -> SEFA.DFM.Model.ViewModels.SopDirDto
   at lambda_method589(Closure , SopDirEntity , SopDirDto , ResolutionContext )
   at lambda_method588(Closure , Object , List`1 , ResolutionContext )
2025-05-15 15:03:35.885 +08:00 [ERR] Error mapping types.

Mapping types:
Object -> List`1
System.Object -> System.Collections.Generic.List`1[[SEFA.DFM.Model.ViewModels.SopDirDto, SEFA.DFM.Model, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]
【自定义错误】：Error mapping types.

Mapping types:
Object -> List`1
System.Object -> System.Collections.Generic.List`1[[SEFA.DFM.Model.ViewModels.SopDirDto, SEFA.DFM.Model, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]] 
【异常类型】：AutoMapperMappingException 
【异常信息】：Error mapping types.

Mapping types:
Object -> List`1
System.Object -> System.Collections.Generic.List`1[[SEFA.DFM.Model.ViewModels.SopDirDto, SEFA.DFM.Model, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]] 
【堆栈调用】：   at lambda_method588(Closure , Object , List`1 , ResolutionContext )
   at SEFA.DFM.Services.SopDirServices.GetAllDirs() in C:\work\syngentagroup\SEFA_XZD\SEFA.DFM\SEFA.DFM.Services\SopDirServices.cs:line 154
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.DFMApi.Controllers.SopDirController.GetTreeList(String parentId) in C:\work\syngentagroup\SEFA_XZD\SEFA.DFM\SEFA.DFM.Api\Controllers\SopDirController.cs:line 115
   at lambda_method550(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
AutoMapper.AutoMapperMappingException: Error mapping types.

Mapping types:
Object -> List`1
System.Object -> System.Collections.Generic.List`1[[SEFA.DFM.Model.ViewModels.SopDirDto, SEFA.DFM.Model, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]
 ---> AutoMapper.AutoMapperMappingException: Missing type map configuration or unsupported mapping.

Mapping types:
SopDirEntity -> SopDirDto
SEFA.DFM.Model.Models.SopDirEntity -> SEFA.DFM.Model.ViewModels.SopDirDto
   at lambda_method589(Closure , SopDirEntity , SopDirDto , ResolutionContext )
   at lambda_method588(Closure , Object , List`1 , ResolutionContext )
   --- End of inner exception stack trace ---
   at lambda_method588(Closure , Object , List`1 , ResolutionContext )
   at SEFA.DFM.Services.SopDirServices.GetAllDirs() in C:\work\syngentagroup\SEFA_XZD\SEFA.DFM\SEFA.DFM.Services\SopDirServices.cs:line 154
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.DFMApi.Controllers.SopDirController.GetTreeList(String parentId) in C:\work\syngentagroup\SEFA_XZD\SEFA.DFM\SEFA.DFM.Api\Controllers\SopDirController.cs:line 115
   at lambda_method550(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
