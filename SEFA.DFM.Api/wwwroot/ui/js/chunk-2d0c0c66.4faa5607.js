(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0c0c66"],{"42e7":function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("section",[a("el-col",{staticClass:"toolbar",staticStyle:{"padding-bottom":"0px"},attrs:{span:24}},[a("el-form",{attrs:{inline:!0},nativeOn:{submit:function(e){e.preventDefault()}}},[a("el-form-item",[a("el-select",{attrs:{placeholder:"请选择要操作的公众号"},model:{value:e.selectWeChat,callback:function(t){e.selectWeChat=t},expression:"selectWeChat"}},e._l(e.wechats,function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}},[a("span",{staticStyle:{float:"left"}},[e._v(e._s(t.label))]),a("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[e._v(e._s(t.value))])])}),1)],1),e.selectWeChat?a("el-form-item",[a("el-select",{attrs:{placeholder:"请选择要操作的客户"},model:{value:e.selectCompany,callback:function(t){e.selectCompany=t},expression:"selectCompany"}},e._l(e.companys,function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}},[a("span",{staticStyle:{float:"left"}},[e._v(e._s(t.label))]),a("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[e._v(e._s(t.value))])])}),1)],1):e._e(),a("el-form-item",[a("el-button",{attrs:{type:"primary",disabled:""==e.selectWeChat},on:{click:e.searchWeChatAccount}},[e._v("刷新")])],1)],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:e.tableData,"highlight-current-row":""},on:{"selection-change":e.selsChange}},[a("el-table-column",{attrs:{type:"index",width:"80"}}),a("el-table-column",{attrs:{prop:"PushLogPublicAccount",label:"微信公众号",width:"100"}}),a("el-table-column",{attrs:{prop:"PushLogCompanyID",label:"客户",width:""}}),a("el-table-column",{attrs:{prop:"PushLogToUserID",label:"员工ID",width:""}}),a("el-table-column",{attrs:{prop:"PushLogOpenid",label:"微信ID",width:"300"}}),a("el-table-column",{attrs:{prop:"PushLogTime",label:"推送时间",width:"250"}}),a("el-table-column",{attrs:{prop:"PushLogStatus",label:"推送状态",width:""}}),a("el-table-column",{attrs:{prop:"PushLogRemark",label:"信息",width:""}}),a("el-table-column",{attrs:{prop:"PushLogIP",label:"推送IP",width:""}}),a("el-table-column",{attrs:{prop:"PushLogTemplateID",label:"推送模板ID",width:"150"}}),a("el-table-column",{attrs:{prop:"PushLogContent",label:"推送内容",width:"500"}})],1),a("div",{staticClass:"block"},[a("el-pagination",{attrs:{"current-page":e.page.pageIndex,"hide-on-single-page":!0,"page-sizes":[10,100,500,1e3],"page-size":e.page.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.page.pageTotal},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1)},n=[],s=(a("ac6a"),a("cadf"),a("551c"),a("097d"),a("4ec3")),o={name:"WeChatCompany",data:function(){return{wechats:[],companys:[],selectWeChat:"",selectCompany:"",listLoading:!1,tableData:[],sels:[],page:{pageSize:10,pageIndex:1,pageTotal:0}}},created:function(){this.getWeChats(),this.getWeCompanys()},methods:{selsChange:function(e){this.sels=e},handleCurrentChange:function(e){this.page.pageIndex=e,this.searchWeChatAccount()},handleSizeChange:function(e){this.page.pageIndex=1,this.page.pageSize=e,this.searchWeChatAccount()},searchWeChatAccount:function(){var e=this;this.listLoading=!0;var t={intPageIndex:this.page.pageIndex,intPageSize:this.page.pageSize,strOrderByFileds:"PushLogTime desc",conditions:"PushLogPublicAccount = "+this.selectWeChat};this.selectCompany&&(t.conditions+=" & PushLogCompanyID = "+this.selectCompany),Object(s["W"])(t).then(function(t){e.listLoading=!1,console.log(t),t.data.success&&(e.tableData=t.data.response.data,e.page.pageTotal=t.data.response.dataCount,e.$message({type:"success",message:"获取成功!"}))})},getWeCompanys:function(){var e=this;Object(s["U"])().then(function(t){e.companys=[],console.log(t),t.data.response.data.forEach(function(t){e.companys.push({value:t.CompanyID,label:t.CompanyName})})})},getWeChats:function(){var e=this;Object(s["S"])().then(function(t){e.wechats=[],t.data.response.data.forEach(function(t){e.wechats.push({value:t.publicAccount,label:t.publicNick})})})}},mounted:function(){},watch:{selectWeChat:function(e,t){this.searchWeChatAccount()},selectCompany:function(e,t){this.searchWeChatAccount()}}},c=o,i=a("2877"),p=Object(i["a"])(c,l,n,!1,null,null,null);p.options.__file="PushLog.vue";t["default"]=p.exports}}]);
//# sourceMappingURL=chunk-2d0c0c66.4faa5607.js.map