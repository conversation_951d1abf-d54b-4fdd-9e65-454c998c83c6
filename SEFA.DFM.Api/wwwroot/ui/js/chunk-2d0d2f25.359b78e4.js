(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0d2f25"],{"5b57":function(e,t,l){"use strict";l.r(t);var a=function(){var e=this,t=e.$createElement,l=e._self._c||t;return l("section",[l("el-col",{staticClass:"toolbar",staticStyle:{"padding-bottom":"0px"},attrs:{span:24}},[l("el-form",{attrs:{inline:!0},nativeOn:{submit:function(e){e.preventDefault()}}},[l("el-form-item",[l("el-select",{attrs:{placeholder:"请选择要操作的公众号"},model:{value:e.selectWeChat,callback:function(t){e.selectWeChat=t},expression:"selectWeChat"}},e._l(e.wechats,function(t){return l("el-option",{key:t.value,attrs:{label:t.label,value:t.value}},[l("span",{staticStyle:{float:"left"}},[e._v(e._s(t.label))]),l("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[e._v(e._s(t.value))])])}),1)],1),l("el-form-item",[l("el-select",{attrs:{placeholder:"请选择要发送的集合"},model:{value:e.selectOperate,callback:function(t){e.selectOperate=t},expression:"selectOperate"}},e._l(e.operate,function(t){return l("el-option",{key:t.value,attrs:{label:t.label,value:t.value}},[l("span",{staticStyle:{float:"left"}},[e._v(e._s(t.label))]),l("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[e._v(e._s(t.value))])])}),1)],1),l("el-form-item",[l("el-select",{attrs:{placeholder:"请选择要发送的对象"},model:{value:e.selectBindOrSub,callback:function(t){e.selectBindOrSub=t},expression:"selectBindOrSub"}},e._l(e.bindOrSub,function(t){return l("el-option",{key:t.value,attrs:{label:t.label,value:t.value}},[l("span",{staticStyle:{float:"left"}},[e._v(e._s(t.label))]),l("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[e._v(e._s(t.value))])])}),1)],1),"bind"==e.selectBindOrSub?l("el-form-item",[l("el-select",{attrs:{placeholder:"请选择要操作的客户"},model:{value:e.selectCompany,callback:function(t){e.selectCompany=t},expression:"selectCompany"}},e._l(e.companys,function(t){return l("el-option",{key:t.value,attrs:{label:t.label,value:t.value}},[l("span",{staticStyle:{float:"left"}},[e._v(e._s(t.label))]),l("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[e._v(e._s(t.value))])])}),1)],1):e._e(),l("el-form-item",[l("el-select",{attrs:{placeholder:"请选择消息类型"},model:{value:e.selectMsgType,callback:function(t){e.selectMsgType=t},expression:"selectMsgType"}},e._l(e.msgTypes,function(t){return l("el-option",{key:t.value,attrs:{label:t.label,value:t.value}},[l("span",{staticStyle:{float:"left"}},[e._v(e._s(t.label))]),l("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[e._v(e._s(t.value))])])}),1)],1),"one"==e.selectOperate?l("el-form-item",[l("el-input",{attrs:{placeholder:"请输入要发送的用户"},model:{value:e.selectUser,callback:function(t){e.selectUser=t},expression:"selectUser"}})],1):e._e(),l("el-form-item",[l("el-button",{attrs:{type:"primary",disabled:0==e.checkIsOk()},on:{click:e.searchWeChatAccount}},[e._v("发送消息")])],1),"text"==e.selectMsgType?l("el-card",{staticClass:"box-card"},[l("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[l("span",[e._v("推送文本")])]),l("el-form-item",{attrs:{label:"文本内容"}},[l("el-input",{model:{value:e.textContent.text,callback:function(t){e.$set(e.textContent,"text",t)},expression:"textContent.text"}})],1)],1):e._e(),"image"==e.selectMsgType?l("el-card",{staticClass:"box-card"},[l("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[l("span",[e._v("推送图片")])]),l("el-form-item",{attrs:{label:"图片mediaID"}},[l("el-input",{model:{value:e.pictureContent.pictureMediaID,callback:function(t){e.$set(e.pictureContent,"pictureMediaID",t)},expression:"pictureContent.pictureMediaID"}})],1)],1):e._e(),"voice"==e.selectMsgType?l("el-card",{staticClass:"box-card"},[l("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[l("span",[e._v("推送语音")])]),l("el-form-item",{attrs:{label:"语音mediaID"}},[l("el-input",{model:{value:e.voiceContent.voiceMediaID,callback:function(t){e.$set(e.voiceContent,"voiceMediaID",t)},expression:"voiceContent.voiceMediaID"}})],1)],1):e._e(),"mpvideo"==e.selectMsgType?l("el-card",{staticClass:"box-card"},[l("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[l("span",[e._v("推送视频")])]),l("el-form-item",{attrs:{label:"视频mediaID"}},[l("el-input",{model:{value:e.videoContent.videoMediaID,callback:function(t){e.$set(e.videoContent,"videoMediaID",t)},expression:"videoContent.videoMediaID"}})],1)],1):e._e()],1)],1)],1)},s=[],n=(l("ac6a"),l("cadf"),l("551c"),l("097d"),l("4ec3")),c={name:"WeChatCompany",data:function(){return{wechats:[],companys:[],operate:[{value:"one",label:"单个用户"},{value:"all",label:"所有用户"}],bindOrSub:[{value:"sub",label:"订阅用户"},{value:"bind",label:"绑定用户"}],msgTypes:[{value:"text",label:"文本消息"},{value:"image",label:"图片消息"},{value:"voice",label:"语音消息"},{value:"mpvideo",label:"视频消息"}],selectWeChat:"",selectOperate:"",selectBindOrSub:"",selectCompany:"",selectMsgType:"",selectUser:"",textContent:{text:""},pictureContent:{pictureMediaID:""},voiceContent:{voiceMediaID:""},videoContent:{title:"",pictureMediaID:"",videoMediaID:""},linkMsgContent:{title:"",description:"",viewUrl:"",pictureUrl:""}}},created:function(){this.getWeChats(),this.getWeCompanys()},methods:{checkIsOk:function(){return""!=this.selectWeChat&&""!=this.selectOperate&&""!=this.selectBindOrSub&&""!=this.selectMsgType&&("one"==this.selectOperate&&"sub"==this.selectBindOrSub&&""!=this.selectUser||("one"==this.selectOperate&&"bind"==this.selectBindOrSub&&""!=this.selectCompany&&""!=this.selectUser||("all"==this.selectOperate&&"sub"==this.selectBindOrSub||"all"==this.selectOperate&&"bind"==this.selectBindOrSub&&""!=this.selectCompany)))},searchWeChatAccount:function(){var e=this;Object(n["bb"])({selectWeChat:this.selectWeChat,selectOperate:this.selectOperate,selectBindOrSub:this.selectBindOrSub,selectCompany:this.selectCompany,selectMsgType:this.selectMsgType,selectUser:this.selectUser,textContent:this.textContent,pictureContent:this.pictureContent,voiceContent:this.voiceContent,videoContent:this.videoContent,linkMsgContent:this.linkMsgContent}).then(function(t){t.data.success?e.$message.success(t.data.msg):e.$message.error(t.data.msg)})},getWeChats:function(){var e=this;Object(n["S"])().then(function(t){e.wechats=[],t.data.response.data.forEach(function(t){e.wechats.push({value:t.publicAccount,label:t.publicNick})})})},getWeCompanys:function(){var e=this;Object(n["U"])().then(function(t){e.companys=[],t.data.response.data.forEach(function(t){e.companys.push({value:t.CompanyID,label:t.CompanyName})})})}},mounted:function(){},watch:{selectWeChat:function(e,t){},selectCompany:function(e,t){}}},i=c,o=l("2877"),r=Object(o["a"])(i,a,s,!1,null,null,null);r.options.__file="SendMessage.vue";t["default"]=r.exports}}]);
//# sourceMappingURL=chunk-2d0d2f25.359b78e4.js.map