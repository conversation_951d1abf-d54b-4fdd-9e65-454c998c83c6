(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-47211100"],{"24a1":function(e,t,n){},"386d":function(e,t,n){"use strict";var s=n("cb7c"),a=n("83a1"),r=n("5f1b");n("214f")("search",1,function(e,t,n,i){return[function(n){var s=e(this),a=void 0==n?void 0:n[t];return void 0!==a?a.call(n,s):new RegExp(n)[t](String(s))},function(e){var t=i(n,e,this);if(t.done)return t.value;var o=s(e),l=String(this),c=o.lastIndex;a(c,0)||(o.lastIndex=0);var d=r(o,l);return a(o.lastIndex,c)||(o.lastIndex=c),null===d?-1:d.index}]})},"3b2b":function(e,t,n){var s=n("7726"),a=n("5dbc"),r=n("86cc").f,i=n("9093").f,o=n("aae3"),l=n("0bfb"),c=s.RegExp,d=c,u=c.prototype,h=/a/g,g=/a/g,f=new c(h)!==h;if(n("9e1e")&&(!f||n("79e5")(function(){return g[n("2b4c")("match")]=!1,c(h)!=h||c(g)==g||"/a/i"!=c(h,"i")}))){c=function(e,t){var n=this instanceof c,s=o(e),r=void 0===t;return!n&&s&&e.constructor===c&&r?e:a(f?new d(s&&!r?e.source:e,t):d((s=e instanceof c)?e.source:e,s&&r?l.call(e):t),n?this:u,c)};for(var v=function(e){e in c||r(c,e,{configurable:!0,get:function(){return d[e]},set:function(t){d[e]=t}})},p=i(d),m=0;p.length>m;)v(p[m++]);u.constructor=c,c.prototype=u,n("2aba")(s,"RegExp",c)}n("7a56")("RegExp")},4917:function(e,t,n){"use strict";var s=n("cb7c"),a=n("9def"),r=n("0390"),i=n("5f1b");n("214f")("match",1,function(e,t,n,o){return[function(n){var s=e(this),a=void 0==n?void 0:n[t];return void 0!==a?a.call(n,s):new RegExp(n)[t](String(s))},function(e){var t=o(n,e,this);if(t.done)return t.value;var l=s(e),c=String(this);if(!l.global)return i(l,c);var d=l.unicode;l.lastIndex=0;var u,h=[],g=0;while(null!==(u=i(l,c))){var f=String(u[0]);h[g]=f,""===f&&(l.lastIndex=r(c,a(l.lastIndex),d)),g++}return 0===g?null:h}]})},"5f67":function(e,t,n){"use strict";n.r(t);var s=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("section",[n("el-col",{staticClass:"toolbar roles",attrs:{span:8}},[n("el-card",{staticClass:"box-card"},[n("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[n("span",[e._v("权限")]),n("el-button",{staticStyle:{float:"right",padding:"3px 0"},attrs:{type:"text"},on:{click:e.getRoles}},[e._v("刷新")])],1),e._l(e.roles,function(t){return n("div",{key:t.Id,staticClass:"text item role-item",class:t.Id==e.roleid?"active":"",on:{click:function(n){e.operate(t.Id)}}},[e._v("\n                "+e._s(t.Name)+"\n            ")])})],2)],1),n("el-col",{staticClass:"toolbar perms morechildren",attrs:{span:16}},[n("el-card",{staticClass:"box-card"},[n("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[n("span",[e._v("菜单")]),n("el-button",{staticStyle:{float:"right",padding:"3px 0"},attrs:{loading:e.loadingSave,type:"text"},on:{click:e.saveAssign}},[e._v(e._s(e.loadingSaveStr))])],1),n("div",{staticClass:"block"},[n("el-tree",{ref:"tree",attrs:{data:e.data5,"show-checkbox":"","node-key":"value","default-expand-all":"","expand-on-click-node":!0,"check-strictly":!0},scopedSlots:e._u([{key:"default",fn:function(t){var s=t.node,a=t.data;return n("span",{staticClass:"custom-tree-node"},[n("span",[e._v(e._s(s.label)),a.btns&&a.btns.length>1?n("el-button",{staticStyle:{padding:"5px 8px","margin-left":"5px"},attrs:{size:"mini",type:"plain"},on:{click:function(t){t.preventDefault(),e.reverse(a.btns)}}},[e._v("反选")]):e._e()],1),n("span",[n("el-checkbox-group",{model:{value:e.assignBtns,callback:function(t){e.assignBtns=t},expression:"assignBtns"}},e._l(a.btns,function(t){return n("el-checkbox",{key:t.value,attrs:{label:t.value.toString()}},[e._v("\n                                 "+e._s(t.label)+"\n                                 ")])}),1)],1)])}}])})],1)])],1)],1)},a=[],r=(n("7f7f"),n("ac6a"),n("f499")),i=n.n(r),o=(n("20d6"),n("cadf"),n("551c"),n("097d"),n("a6dc")),l=n("4ec3"),c=1e3,d={data:function(){return{roles:[],roleid:0,data5:[],btns:[],assigns:[],checked1:!1,loadingSaveStr:"保存",loadingSave:!1,assignBtns:[],defaultProps:{children:"children",label:"label",btns:"btns"},selectedPermissions:[],currentRoleCode:"",stores:{role:{data:[]},permissionTree:{data:[]}},buttonProps:{type:"default",size:"small"},selectRole:{},menuData:[],menuSelections:[],menuLoading:!1,authLoading:!1,checkAll:!1,currentRoleMenus:[]}},methods:{reverse:function(e){var t=this;if(console.log(this.data5),console.log(e),e&&e.length)for(var n=function(n){var s=e[n],a=t.assignBtns.findIndex(function(e){return e==s.value});a>-1?t.assignBtns.splice(a,1):t.assignBtns.push(""+s.value)},s=0;s<e.length;s++)n(s)},formatEnabled:function(e,t){return e.Enabled?"正常":"未知"},formatCreateTime:function(e,t){return e.CreateTime&&""!=e.CreateTime?o["a"].formatDate.format(new Date(e.CreateTime),"yyyy-MM-dd"):""},getRoles:function(){var e=this;Object(l["M"])().then(function(t){e.roles=t.data.response.data,e.getPermissions()})},getPermissions:function(){var e=this,t=this,n={needbtn:!1};Object(l["J"])(n).then(function(n){t.loadingSave=!1,t.loadingSaveStr="保存",e.data=n.data.response.children,e.data5=JSON.parse(i()(e.data))})},getPermissionIds:function(e){var t=this,n=this;this.assigns=[],this.assignBtns=[];var s={rid:e};Object(l["I"])(s).then(function(e){n.loadingSave=!1,n.loadingSaveStr="保存",t.$refs.tree.setCheckedKeys(e.data.response.permissionids),t.assignBtns=e.data.response.assignbtns})},operate:function(e){this.loadingSave=!0,this.loadingSaveStr="加载中...",this.roleid=e,this.getPermissionIds(e)},saveAssign:function(){var e=this,t=this;this.loadingSave=!0,this.loadingSaveStr="保存中...";var n=this.$refs.tree.getCheckedKeys();try{if(!(this.assignBtns.length>0))return this.loadingSaveStr="保存",this.loadingSave=!1,this.$message({message:"参数错误",type:"error"}),!1;for(var s=0;s<this.assignBtns.length;s++){var a=this.assignBtns[s];a&&a>0&&n.push(a)}}catch(i){return this.$message({message:"操作异常",type:"error"}),!1}console.log(this.assignBtns),console.log(n);var r={pids:n,rid:this.roleid};r.rid>0&&r.pids.length>0?Object(l["g"])(r).then(function(n){if(t.loadingSave=!1,t.loadingSaveStr="保存",n.data.success){e.$message({message:n.data.msg,type:"success"});var s={rid:e.roleid};Object(l["I"])(s).then(function(t){e.$refs.tree.setCheckedKeys(t.data.response.permissionids),e.assignBtns=t.data.response.assignbtns,e.$message({message:"数据更新成功",type:"success"})})}else e.$message({message:n.data.msg,type:"error"})}):(this.loadingSaveStr="保存",this.loadingSave=!1,this.$message({message:"参数错误",type:"error"}))},append:function(e){var t={id:c++,label:"testtest",children:[]};e.children||this.$set(e,"children",[]),e.children.push(t)},remove:function(e,t){var n=e.parent,s=n.data.children||n.data,a=s.findIndex(function(e){return e.id===t.id});s.splice(a,1)},findTreeData:function(){var e=this;this.menuLoading=!0,this.$api.menu.findMenuTree().then(function(t){e.menuData=t.data,e.menuLoading=!1})},handleRoleSelectChange:function(e){var t=this;null!=e&&null!=e.val&&(this.selectRole=e.val,this.$api.role.findRoleMenus({roleId:e.val.id}).then(function(e){t.currentRoleMenus=e.data,t.$refs.menuTree.setCheckedNodes(e.data)}))},handleMenuCheckChange:function(e,t,n){var s=this;if(t){var a=e.parentId;this.$refs.menuTree.setChecked(a,!0,!1)}else null!=e.children&&e.children.forEach(function(e){s.$refs.menuTree.setChecked(e.id,!1,!1)})},checkAllMenu:function(e,t){var n=this;e.forEach(function(e){t.push(e),e.children&&n.checkAllMenu(e.children,t)})},submitAuthForm:function(){var e=this,t=this.selectRole.id;if("admin"!=this.selectRole.name){this.authLoading=!0;for(var n=this.$refs.menuTree.getCheckedNodes(!1,!0),s=[],a=0,r=n.length;a<r;a++){var i={roleId:t,menuId:n[a].id};s.push(i)}this.$api.role.saveRoleMenus(s).then(function(t){200==t.code?e.$message({message:"操作成功",type:"success"}):e.$message({message:"操作失败, "+t.msg,type:"error"}),e.authLoading=!1})}else this.$message({message:"超级管理员拥有所有菜单权限，不允许修改！",type:"error"})},renderContent:function(e,t){t.node;var n=t.data;t.store;return e("div",{class:"column-container"},[e("span",{style:"text-algin:center;margin-right:80px;"},[n.label]),e("span",{style:"text-algin:center;margin-right:80px;"},[e("el-tag",{attrs:{type:n.isbtn?"success":"info",size:"small"}},[n.isbtn?"按钮":"菜单"])])])},dateFormat:function(e,t,n,s){return format(e[t.property])}},mounted:function(){this.loadingSave=!0,this.loadingSaveStr="加载中...",this.getRoles()}},u=d,h=(n("b5bb"),n("2877")),g=Object(h["a"])(u,s,a,!1,null,null,null);g.options.__file="Assign.vue";t["default"]=g.exports},"83a1":function(e,t){e.exports=Object.is||function(e,t){return e===t?0!==e||1/e===1/t:e!=e&&t!=t}},a6dc:function(e,t,n){"use strict";var s=n("e814"),a=n.n(s),r=(n("a481"),n("386d"),n("4917"),n("3b2b"),/([yMdhsm])(\1*)/g),i="yyyy-MM-dd";function o(e,t){t-=(e+"").length;for(var n=0;n<t;n++)e="0"+e;return e}t["a"]={getQueryStringByName:function(e){var t=new RegExp("(^|&)"+e+"=([^&]*)(&|$)","i"),n=window.location.search.substr(1).match(t),s="";return null!=n&&(s=n[2]),t=null,n=null,null==s||""==s||"undefined"==s?"":s},formatDate:{format:function(e,t){return t=t||i,t.replace(r,function(t){switch(t.charAt(0)){case"y":return o(e.getFullYear(),t.length);case"M":return o(e.getMonth()+1,t.length);case"d":return o(e.getDate(),t.length);case"w":return e.getDay()+1;case"h":return o(e.getHours(),t.length);case"m":return o(e.getMinutes(),t.length);case"s":return o(e.getSeconds(),t.length)}})},parse:function(e,t){var n=t.match(r),s=e.match(/(\d)+/g);if(n.length==s.length){for(var i=new Date(1970,0,1),o=0;o<n.length;o++){var l=a()(s[o]),c=n[o];switch(c.charAt(0)){case"y":i.setFullYear(l);break;case"M":i.setMonth(l-1);break;case"d":i.setDate(l);break;case"h":i.setHours(l);break;case"m":i.setMinutes(l);break;case"s":i.setSeconds(l);break}}return i}return null}},isEmt:{format:function(e){return"undefined"==typeof e||null==e||""==e}}}},aae3:function(e,t,n){var s=n("d3f4"),a=n("2d95"),r=n("2b4c")("match");e.exports=function(e){var t;return s(e)&&(void 0!==(t=e[r])?!!t:"RegExp"==a(e))}},b5bb:function(e,t,n){"use strict";var s=n("24a1"),a=n.n(s);a.a}}]);
//# sourceMappingURL=chunk-47211100.16761898.js.map