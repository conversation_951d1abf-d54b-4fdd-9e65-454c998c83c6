(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4b6066be"],{"0a92":function(e,t,s){"use strict";s.r(t);var a=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("section",[s("el-col",{staticClass:"toolbar",staticStyle:{"padding-bottom":"0px"},attrs:{span:24}},[s("el-form",{attrs:{inline:!0},nativeOn:{submit:function(e){e.preventDefault()}}},[s("el-form-item",[s("el-select",{attrs:{placeholder:"请选择要操作的公众号"},model:{value:e.selectWeChat,callback:function(t){e.selectWeChat=t},expression:"selectWeChat"}},e._l(e.wechats,function(t){return s("el-option",{key:t.value,attrs:{label:t.label,value:t.value}},[s("span",{staticStyle:{float:"left"}},[e._v(e._s(t.label))]),s("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[e._v(e._s(t.value))])])}),1)],1),s("el-form-item",[s("el-button",{attrs:{type:"primary",disabled:""==e.selectWeChat},on:{click:function(t){e.getWxMenu(e.selectWeChat)}}},[e._v("刷新")])],1)],1)],1),s("div",{staticClass:"public-account-management clearfix"},[s("div",{staticClass:"left"},[s("div",{staticClass:"menu clearfix"},[e._l(e.menu.button,function(t,a){return s("div",{key:a,staticClass:"menu_bottom"},[s("div",{staticClass:"menu_item",class:{active:e.isActive==a},on:{click:function(s){e.menuFun(a,t)}}},[e._v(e._s(t.name))]),e.isSubMenuFlag==a?s("div",{staticClass:"submenu"},[e._l(t.sub_button,function(i,n){return s("div",{key:n,staticClass:"subtitle"},[s("div",{staticClass:"menu_subItem",class:{active:e.isSubMenuActive==a+""+n},on:{click:function(s){e.subMenuFun(t,i,a,n)}}},[e._v(e._s(i.name))])])}),t.sub_button.length<5?s("div",{staticClass:"menu_bottom menu_addicon",on:{click:function(s){e.addSubMenu(t)}}},[s("i",{staticClass:"el-icon-plus"})]):e._e()],2):e._e()])}),e.menuKeyLength<3?s("div",{staticClass:"menu_bottom menu_addicon",on:{click:e.addMenu}},[s("i",{staticClass:"el-icon-plus"})]):e._e()],2),s("el-button",{staticClass:"save_btn",attrs:{disabled:""==e.selectWeChat,type:"success"},on:{click:function(t){e.saveFun(e.selectWeChat)}}},[e._v("保存并发布至菜单")])],1),e.showRightFlag?e._e():s("div",{staticClass:"right"},[s("div",{staticClass:"configure_page"},[s("div",{staticClass:"delete_btn"},[s("el-button",{attrs:{size:"mini",type:"danger",icon:"el-icon-delete"},on:{click:function(t){e.deleteMenu(e.tempObj)}}},[e._v("删除当前菜单")])],1),s("div",[s("span",[e._v("菜单名称：")]),s("el-input",{staticClass:"input_width",staticStyle:{"margin-top":"12px"},attrs:{placeholder:"请输入菜单名称",clearable:""},model:{value:e.tempObj.name,callback:function(t){e.$set(e.tempObj,"name",t)},expression:"tempObj.name"}})],1),s("div",[s("div",{staticClass:"menu_content"},[s("span",[e._v("菜单内容：")]),s("el-radio-group",{model:{value:e.tempObj.type,callback:function(t){e.$set(e.tempObj,"type",t)},expression:"tempObj.type"}},[s("el-radio",{attrs:{label:"media_id"}},[e._v("发送素材")]),s("el-radio",{attrs:{label:"view"}},[e._v("跳转链接")]),s("el-radio",{attrs:{label:"click"}},[e._v("发送关键词")]),s("el-radio",{attrs:{label:"miniprogram"}},[e._v("小程序")]),s("el-radio",{attrs:{label:"event"}},[e._v("事件功能")])],1)],1),s("div",{staticClass:"configur_content"},["media_id"==e.tempObj.type?s("div",{staticClass:"material"},[s("span",[e._v("素材内容：")]),s("el-input",{staticClass:"input_width",attrs:{disabled:!0,placeholder:"素材名称"},model:{value:e.tempObj.media_id,callback:function(t){e.$set(e.tempObj,"media_id",t)},expression:"tempObj.media_id"}}),s("el-popover",{attrs:{placement:"top"},model:{value:e.visible2,callback:function(t){e.visible2=t},expression:"visible2"}},[s("el-table",{staticStyle:{width:"100%"},attrs:{data:e.tableData}},[s("el-table-column",{attrs:{label:"文件名",width:"600"},scopedSlots:e._u([{key:"default",fn:function(t){return[s("el-popover",{attrs:{trigger:"hover",placement:"top"}},[s("p",[e._v("文件名: "+e._s(t.row.name))]),s("div",{staticClass:"name-wrapper",attrs:{slot:"reference"},slot:"reference"},[s("el-tag",{attrs:{size:"medium"}},[e._v(e._s(t.row.name))])],1)])]}}])}),s("el-table-column",{attrs:{label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){return[s("el-button",{attrs:{size:"mini"},on:{click:function(s){e.handleEdit(t.$index,t.row)}}},[e._v("选择")])]}}])})],1),s("el-button",{attrs:{slot:"reference",type:"success"},slot:"reference"},[e._v("选择素材")])],1),s("p",{staticClass:"blue"},[e._v("tips:需要调后台获取到内容，弹框出来，然后选择，把名字赋值上去！")])],1):e._e(),"view"==e.tempObj.type?s("div",[s("span",[e._v("跳转链接：")]),s("el-input",{staticClass:"input_width",attrs:{placeholder:"请输入链接",clearable:""},model:{value:e.tempObj.url,callback:function(t){e.$set(e.tempObj,"url",t)},expression:"tempObj.url"}})],1):e._e(),"click"==e.tempObj.type?s("div",[s("div",[s("span",[e._v("关键词：")]),s("el-input",{staticClass:"input_width",attrs:{placeholder:"请输入关键词",clearable:""},model:{value:e.tempObj.key,callback:function(t){e.$set(e.tempObj,"key",t)},expression:"tempObj.key"}})],1),s("p",{staticClass:"blue"},[e._v("tips:这里需要配合关键词推送消息一起使用")])]):e._e(),"miniprogram"==e.tempObj.type?s("div",[s("div",{staticClass:"applet"},[s("span",[e._v("小程序的appid：")]),s("el-input",{staticClass:"input_width",attrs:{placeholder:"请输入小程序的appid",clearable:""},model:{value:e.tempObj.appid,callback:function(t){e.$set(e.tempObj,"appid",t)},expression:"tempObj.appid"}})],1),s("div",[s("span",[e._v("小程序的页面路径：")]),s("el-input",{staticClass:"input_width",attrs:{placeholder:"请输入小程序的页面路径，如：pages/index",clearable:""},model:{value:e.tempObj.pagepath,callback:function(t){e.$set(e.tempObj,"pagepath",t)},expression:"tempObj.pagepath"}})],1),s("p",{staticClass:"blue"},[e._v("tips:需要和公众号进行关联才可以把小程序绑定带微信菜单上哟！")])]):e._e(),"event"==e.tempObj.type?s("div",[s("span",[e._v("功能事件：")]),s("el-radio-group",{model:{value:e.tempObj.key,callback:function(t){e.$set(e.tempObj,"key",t)},expression:"tempObj.key"}},[s("el-radio",{attrs:{label:"pic_weixin"}},[e._v("微信相册")]),s("el-radio",{attrs:{label:"pic_sysphoto"}},[e._v("拍照发图")]),s("el-radio",{attrs:{label:"location_select"}},[e._v("位置选择")]),s("el-radio",{attrs:{label:"scancode_push"}},[e._v("微信扫码")])],1)],1):e._e()])])]),s("div",[e._v("menu对象值："+e._s(e.menu))])]),e.showRightFlag?s("div",{staticClass:"right"},[s("p",[e._v("请选择菜单配置")])]):e._e()])],1)},i=[],n=(s("7f7f"),s("ac6a"),s("cadf"),s("551c"),s("097d"),s("4ec3")),l={name:"WeChatMenu",data:function(){return{showRightFlag:!0,wechats:[],selectWeChat:"",menu:{button:[{type:"click",name:"菜单1",key:"menu1",url:"",media_id:"",sub_button:[{name:"子菜单1"}]}]},isActive:-1,isSubMenuActive:-1,isSubMenuFlag:-1,tempObj:{},tempSelfObj:{},visible2:!1,tableData:[]}},created:function(){this.getWeChats(),this.mockMediaFun(),this.mockMenuFun()},methods:{getWeChats:function(){var e=this;Object(n["S"])().then(function(t){e.wechats=[],t.data.success?t.data.response.data.forEach(function(t){e.wechats.push({value:t.publicAccount,label:t.publicNick})}):e.$message.error(t.data.msg)})},getWxMenu:function(e){var t=this;Object(n["V"])({id:e}).then(function(e){console.log(e),e.data.success?(t.menu=e.data.response.menu,t.$message.success("菜单获取成功!")):t.$message.error(e.data.msg)})},handleEdit:function(e,t){this.visible2=!1,this.tempObj.media_id=t.name},saveFun:function(e){var t=this;Object(n["xb"])({id:e,menu:this.menu}).then(function(e){e.data.success?0==e.data.response.errcode?t.$message.success("设置成功!"):t.$message.error(e.data.response.errmsg):t.$message.error(e.data.msg)})},menuFun:function(e,t){console.log(e),console.log(t),this.showRightFlag=!1,this.tempObj=t,this.tempSelfObj.grand="1",this.tempSelfObj.index=e,this.isActive=e,this.isSubMenuFlag=e,this.isSubMenuActive=-1},subMenuFun:function(e,t,s,a){this.showRightFlag=!1,this.tempObj=t,this.tempSelfObj.grand="2",this.tempSelfObj.index=s,this.tempSelfObj.secondIndex=a,this.isSubMenuActive=s+""+a,this.isActive=-1},addMenu:function(){console.log("addMenu"),2==this.menuKeyLength&&this.$set(this.menu.button,"2",{name:"菜单3",type:"click",key:"菜单3",sub_button:[]}),1==this.menuKeyLength&&this.$set(this.menu.button,"1",{name:"菜单2",type:"click",key:"菜单2",sub_button:[]}),0==this.menuKeyLength&&this.$set(this.menu.button,"0",{name:"菜单1",type:"click",key:"菜单1",sub_button:[]})},addSubMenu:function(e){var t=e.sub_button.length;4==t&&this.$set(e.sub_button,"4",{name:"子菜单5",type:"click",key:"子菜单5"}),3==t&&this.$set(e.sub_button,"3",{name:"子菜单4",type:"click",key:"子菜单4"}),2==t&&this.$set(e.sub_button,"2",{name:"子菜单3",type:"click",key:"子菜单3"}),1==t&&this.$set(e.sub_button,"1",{name:"子菜单2",type:"click",key:"子菜单2"}),0==t&&this.$set(e.sub_button,"0",{name:"子菜单1",type:"click",key:"子菜单1"})},deleteMenu:function(e){var t=this,s=this;this.$confirm("正在删除当前选中菜单, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){s.deleteData(e)}).catch(function(e){t.$message({type:"error",message:e})})},deleteData:function(e){this.showRightFlag&&this.$message({type:"info",message:"请选择要删除的菜单!"}),"1"==this.tempSelfObj.grand?(this.menu.button.splice(this.tempSelfObj.index,1),this.isActive=-1):"2"==this.tempSelfObj.grand?(this.menu.button[this.tempSelfObj.index].sub_button.splice(this.tempSelfObj.secondIndex,1),this.isSubMenuActive=-1):console.log("没有找到对应的值:"+this.tempSelfObj.grand),this.showRightFlag=!0,this.$message({type:"success",message:"删除成功!"})}},mounted:function(){},computed:{menuKeyLength:function(){return this.menu.button.length}},watch:{selectWeChat:function(e,t){console.log(e),this.getWxMenu(e)}}},c=l,u=(s("61c2"),s("2877")),o=Object(u["a"])(c,a,i,!1,null,"f4008632",null);o.options.__file="Menu.vue";t["default"]=o.exports},"61c2":function(e,t,s){"use strict";var a=s("bf16"),i=s.n(a);i.a},bf16:function(e,t,s){}}]);
//# sourceMappingURL=chunk-4b6066be.f63d0f19.js.map