(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-bf843d8a"],{"28a5":function(e,t,a){"use strict";var r=a("aae3"),i=a("cb7c"),o=a("ebd6"),n=a("0390"),s=a("9def"),l=a("5f1b"),d=a("520a"),c=Math.min,u=[].push,m="split",f="length",h="lastIndex",p=!!function(){try{return new RegExp("x","y")}catch(e){}}();a("214f")("split",2,function(e,t,a,b){var g;return g="c"=="abbc"[m](/(b)*/)[1]||4!="test"[m](/(?:)/,-1)[f]||2!="ab"[m](/(?:ab)*/)[f]||4!="."[m](/(.?)(.?)/)[f]||"."[m](/()()/)[f]>1||""[m](/.?/)[f]?function(e,t){var i=String(this);if(void 0===e&&0===t)return[];if(!r(e))return a.call(i,e,t);var o,n,s,l=[],c=(e.ignoreCase?"i":"")+(e.multiline?"m":"")+(e.unicode?"u":"")+(e.sticky?"y":""),m=0,p=void 0===t?4294967295:t>>>0,b=new RegExp(e.source,c+"g");while(o=d.call(b,i)){if(n=b[h],n>m&&(l.push(i.slice(m,o.index)),o[f]>1&&o.index<i[f]&&u.apply(l,o.slice(1)),s=o[0][f],m=n,l[f]>=p))break;b[h]===o.index&&b[h]++}return m===i[f]?!s&&b.test("")||l.push(""):l.push(i.slice(m)),l[f]>p?l.slice(0,p):l}:"0"[m](void 0,0)[f]?function(e,t){return void 0===e&&0===t?[]:a.call(this,e,t)}:a,[function(a,r){var i=e(this),o=void 0==a?void 0:a[t];return void 0!==o?o.call(a,i,r):g.call(String(i),a,r)},function(e,t){var r=b(g,e,this,t,g!==a);if(r.done)return r.value;var d=i(e),u=String(this),m=o(d,RegExp),f=d.unicode,h=(d.ignoreCase?"i":"")+(d.multiline?"m":"")+(d.unicode?"u":"")+(p?"y":"g"),v=new m(p?d:"^(?:"+d.source+")",h),y=void 0===t?4294967295:t>>>0;if(0===y)return[];if(0===u.length)return null===l(v,u)?[u]:[];var F=0,w=0,x=[];while(w<u.length){v.lastIndex=p?w:0;var D,k=l(v,p?u:u.slice(w));if(null===k||(D=c(s(v.lastIndex+(p?0:w)),u.length))===F)w=n(u,w,f);else{if(x.push(u.slice(F,w)),x.length===y)return x;for(var S=1;S<=k.length-1;S++)if(x.push(k[S]),x.length===y)return x;w=F=D}}return x.push(u.slice(F)),x}]})},"386d":function(e,t,a){"use strict";var r=a("cb7c"),i=a("83a1"),o=a("5f1b");a("214f")("search",1,function(e,t,a,n){return[function(a){var r=e(this),i=void 0==a?void 0:a[t];return void 0!==i?i.call(a,r):new RegExp(a)[t](String(r))},function(e){var t=n(a,e,this);if(t.done)return t.value;var s=r(e),l=String(this),d=s.lastIndex;i(d,0)||(s.lastIndex=0);var c=o(s,l);return i(s.lastIndex,d)||(s.lastIndex=d),null===c?-1:c.index}]})},"3b2b":function(e,t,a){var r=a("7726"),i=a("5dbc"),o=a("86cc").f,n=a("9093").f,s=a("aae3"),l=a("0bfb"),d=r.RegExp,c=d,u=d.prototype,m=/a/g,f=/a/g,h=new d(m)!==m;if(a("9e1e")&&(!h||a("79e5")(function(){return f[a("2b4c")("match")]=!1,d(m)!=m||d(f)==f||"/a/i"!=d(m,"i")}))){d=function(e,t){var a=this instanceof d,r=s(e),o=void 0===t;return!a&&r&&e.constructor===d&&o?e:i(h?new c(r&&!o?e.source:e,t):c((r=e instanceof d)?e.source:e,r&&o?l.call(e):t),a?this:u,d)};for(var p=function(e){e in d||o(d,e,{configurable:!0,get:function(){return c[e]},set:function(t){c[e]=t}})},b=n(c),g=0;b.length>g;)p(b[g++]);u.constructor=d,d.prototype=u,a("2aba")(r,"RegExp",d)}a("7a56")("RegExp")},4917:function(e,t,a){"use strict";var r=a("cb7c"),i=a("9def"),o=a("0390"),n=a("5f1b");a("214f")("match",1,function(e,t,a,s){return[function(a){var r=e(this),i=void 0==a?void 0:a[t];return void 0!==i?i.call(a,r):new RegExp(a)[t](String(r))},function(e){var t=s(a,e,this);if(t.done)return t.value;var l=r(e),d=String(this);if(!l.global)return n(l,d);var c=l.unicode;l.lastIndex=0;var u,m=[],f=0;while(null!==(u=n(l,d))){var h=String(u[0]);m[f]=h,""===h&&(l.lastIndex=o(d,i(l.lastIndex),c)),f++}return 0===f?null:m}]})},5176:function(e,t,a){e.exports=a("51b6")},6908:function(e,t,a){"use strict";var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return null!=e.buttonList&&e.buttonList.length>0?a("el-col",{staticClass:"toolbar",staticStyle:{"padding-bottom":"0px"},attrs:{span:24}},[a("el-form",{attrs:{inline:!0},nativeOn:{submit:function(e){e.preventDefault()}}},[a("el-form-item",[a("el-input",{attrs:{placeholder:"请输入内容"},model:{value:e.searchVal,callback:function(t){e.searchVal=t},expression:"searchVal"}})],1),e._l(e.buttonList,function(t){return a("el-form-item",{key:t.id},[t.IsHide?e._e():a("el-button",{attrs:{type:!t.Func||-1==t.Func.toLowerCase().indexOf("handledel")&&-1==t.Func.toLowerCase().indexOf("stop")?"primary":"danger"},on:{click:function(a){e.callFunc(t)}}},[e._v(e._s(t.name))])],1)})],2)],1):e._e()},i=[],o=(a("cadf"),a("551c"),a("097d"),{name:"Toolbar",data:function(){return{searchVal:""}},props:["buttonList"],methods:{callFunc:function(e){e.search=this.searchVal,this.$emit("callFunction",e)}}}),n=o,s=a("2877"),l=Object(s["a"])(n,r,i,!1,null,null,null);l.options.__file="Toolbar.vue";t["a"]=l.exports},"83a1":function(e,t){e.exports=Object.is||function(e,t){return e===t?0!==e||1/e===1/t:e!=e&&t!=t}},a6dc:function(e,t,a){"use strict";var r=a("e814"),i=a.n(r),o=(a("a481"),a("386d"),a("4917"),a("3b2b"),/([yMdhsm])(\1*)/g),n="yyyy-MM-dd";function s(e,t){t-=(e+"").length;for(var a=0;a<t;a++)e="0"+e;return e}t["a"]={getQueryStringByName:function(e){var t=new RegExp("(^|&)"+e+"=([^&]*)(&|$)","i"),a=window.location.search.substr(1).match(t),r="";return null!=a&&(r=a[2]),t=null,a=null,null==r||""==r||"undefined"==r?"":r},formatDate:{format:function(e,t){return t=t||n,t.replace(o,function(t){switch(t.charAt(0)){case"y":return s(e.getFullYear(),t.length);case"M":return s(e.getMonth()+1,t.length);case"d":return s(e.getDate(),t.length);case"w":return e.getDay()+1;case"h":return s(e.getHours(),t.length);case"m":return s(e.getMinutes(),t.length);case"s":return s(e.getSeconds(),t.length)}})},parse:function(e,t){var a=t.match(o),r=e.match(/(\d)+/g);if(a.length==r.length){for(var n=new Date(1970,0,1),s=0;s<a.length;s++){var l=i()(r[s]),d=a[s];switch(d.charAt(0)){case"y":n.setFullYear(l);break;case"M":n.setMonth(l-1);break;case"d":n.setDate(l);break;case"h":n.setHours(l);break;case"m":n.setMinutes(l);break;case"s":n.setSeconds(l);break}}return n}return null}},isEmt:{format:function(e){return"undefined"==typeof e||null==e||""==e}}}},aae3:function(e,t,a){var r=a("d3f4"),i=a("2d95"),o=a("2b4c")("match");e.exports=function(e){var t;return r(e)&&(void 0!==(t=e[o])?!!t:"RegExp"==i(e))}},dd68:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("section",[a("toolbar",{attrs:{buttonList:e.buttonList},on:{callFunction:e.callFunction}}),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:e.users,"highlight-current-row":""},on:{"current-change":e.selectCurrentRow}},[a("el-table-column",{attrs:{type:"index",width:"80"}}),a("el-table-column",{attrs:{prop:"Name",label:"角色名",width:"",sortable:""}}),a("el-table-column",{attrs:{prop:"AuthorityScope",label:"权限范围",width:"",sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){return[-1==t.row.AuthorityScope?a("el-tag",{attrs:{type:"danger"}},[e._v("无任何数据权限")]):e._e(),1==t.row.AuthorityScope?a("el-tag",[e._v("自定义数据权限 "),a("br"),e._v(e._s(t.row.Dids))]):e._e(),2==t.row.AuthorityScope?a("el-tag",[e._v("本部门数据权限")]):e._e(),3==t.row.AuthorityScope?a("el-tag",{attrs:{type:"warning"}},[e._v("本部门及以下所有部门")]):e._e(),4==t.row.AuthorityScope?a("el-tag",[e._v("仅自己数据权限")]):e._e(),9==t.row.AuthorityScope?a("el-tag",{attrs:{type:"success"}},[e._v("全部数据权限")]):e._e()]}}])}),a("el-table-column",{attrs:{prop:"Description",label:"说明",width:"",sortable:""}}),a("el-table-column",{attrs:{prop:"CreateTime",label:"创建时间",formatter:e.formatCreateTime,width:"",sortable:""}}),a("el-table-column",{attrs:{prop:"Enabled",label:"状态",width:"200",sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-tag",{attrs:{type:t.row.Enabled?"success":"danger","disable-transitions":""}},[e._v(e._s(t.row.Enabled?"正常":"禁用"))])]}}])})],1),a("el-col",{staticClass:"toolbar",attrs:{span:24}},[a("el-button",{attrs:{type:"danger",disabled:0===this.sels.length},on:{click:e.batchRemove}},[e._v("批量删除")]),a("el-pagination",{staticStyle:{float:"right"},attrs:{layout:"prev, pager, next","page-size":50,total:e.total},on:{"current-change":e.handleCurrentChange}})],1),a("el-dialog",{attrs:{title:"编辑",visible:e.editFormVisible,"close-on-click-modal":!1},on:{"update:visible":function(t){e.editFormVisible=t}},model:{value:e.editFormVisible,callback:function(t){e.editFormVisible=t},expression:"editFormVisible"}},[a("el-form",{ref:"editForm",attrs:{model:e.editForm,"label-width":"80px",rules:e.editFormRules}},[a("el-form-item",{attrs:{label:"角色名",prop:"Name"}},[a("el-input",{attrs:{disabled:"","auto-complete":"off"},model:{value:e.editForm.Name,callback:function(t){e.$set(e.editForm,"Name",t)},expression:"editForm.Name"}})],1),a("el-form-item",{attrs:{label:"权限范围",prop:"AuthorityScope"}},[a("el-select",{attrs:{placeholder:"请选择权限范围"},model:{value:e.editForm.AuthorityScope,callback:function(t){e.$set(e.editForm,"AuthorityScope",t)},expression:"editForm.AuthorityScope"}},[a("el-option",{attrs:{label:"无任何数据权限",value:-1}}),a("el-option",{attrs:{label:"自定义数据权限",value:1}}),a("el-option",{attrs:{label:"本部门数据权限",value:2}}),a("el-option",{attrs:{label:"本部门及以下所有部门",value:3}}),a("el-option",{attrs:{label:"仅自己数据权限",value:4}}),a("el-option",{attrs:{label:"全部数据权限",value:9}})],1)],1),1==e.editForm.AuthorityScope?a("el-form-item",{attrs:{prop:"Dids",label:"部门权限",width:"",sortable:""}},[a("el-tree",{ref:"treeEdit",attrs:{data:e.options,"default-expand-all":"","show-checkbox":"","node-key":"value","expand-on-click-node":!0,"check-strictly":!0,props:e.defaultProps}})],1):e._e(),a("el-form-item",{attrs:{label:"状态",prop:"Enabled"}},[a("el-select",{attrs:{placeholder:"请选择角色状态"},model:{value:e.editForm.Enabled,callback:function(t){e.$set(e.editForm,"Enabled",t)},expression:"editForm.Enabled"}},e._l(e.statusList,function(e){return a("el-option",{key:e.value,attrs:{label:e.name,value:e.value}})}),1)],1),a("el-form-item",{attrs:{label:"说明",prop:"Description"}},[a("el-input",{attrs:{"auto-complete":"off"},model:{value:e.editForm.Description,callback:function(t){e.$set(e.editForm,"Description",t)},expression:"editForm.Description"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{nativeOn:{click:function(t){e.editFormVisible=!1}}},[e._v("取消")]),a("el-button",{attrs:{type:"primary",loading:e.editLoading},nativeOn:{click:function(t){return e.editSubmit(t)}}},[e._v("提交")])],1)],1),a("el-dialog",{attrs:{title:"新增",visible:e.addFormVisible,"close-on-click-modal":!1},on:{"update:visible":function(t){e.addFormVisible=t}},model:{value:e.addFormVisible,callback:function(t){e.addFormVisible=t},expression:"addFormVisible"}},[a("el-form",{ref:"addForm",attrs:{model:e.addForm,"label-width":"80px",rules:e.addFormRules}},[a("el-form-item",{attrs:{label:"角色名",prop:"Name"}},[a("el-input",{attrs:{"auto-complete":"off"},model:{value:e.addForm.Name,callback:function(t){e.$set(e.addForm,"Name",t)},expression:"addForm.Name"}})],1),a("el-form-item",{attrs:{label:"权限范围",prop:"AuthorityScope"}},[a("el-select",{attrs:{placeholder:"请选择权限范围"},model:{value:e.addForm.AuthorityScope,callback:function(t){e.$set(e.addForm,"AuthorityScope",t)},expression:"addForm.AuthorityScope"}},[a("el-option",{attrs:{label:"无任何数据权限",value:-1}}),a("el-option",{attrs:{label:"自定义数据权限",value:1}}),a("el-option",{attrs:{label:"本部门数据权限",value:2}}),a("el-option",{attrs:{label:"本部门及以下所有部门",value:3}}),a("el-option",{attrs:{label:"仅自己数据权限",value:4}}),a("el-option",{attrs:{label:"全部数据权限",value:9}})],1)],1),1==e.addForm.AuthorityScope?a("el-form-item",{attrs:{prop:"Dids",label:"部门权限",width:"",sortable:""}},[a("el-tree",{ref:"treeAdd",attrs:{data:e.options,"default-expand-all":"","show-checkbox":"","node-key":"value","expand-on-click-node":!0,"check-strictly":!0,props:e.defaultProps}})],1):e._e(),a("el-form-item",{attrs:{label:"状态",prop:"Enabled"}},[a("el-select",{attrs:{placeholder:"请选择角色状态"},model:{value:e.addForm.Enabled,callback:function(t){e.$set(e.addForm,"Enabled",t)},expression:"addForm.Enabled"}},[a("el-option",{attrs:{label:"激活",value:"true"}}),a("el-option",{attrs:{label:"禁用",value:"false"}})],1)],1),a("el-form-item",{attrs:{label:"说明",prop:"Description"}},[a("el-input",{attrs:{"auto-complete":"off"},model:{value:e.addForm.Description,callback:function(t){e.$set(e.addForm,"Description",t)},expression:"addForm.Description"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{nativeOn:{click:function(t){e.addFormVisible=!1}}},[e._v("取消")]),a("el-button",{attrs:{type:"primary",loading:e.addLoading},nativeOn:{click:function(t){return e.addSubmit(t)}}},[e._v("提交")])],1)],1)],1)},i=[],o=(a("ac6a"),a("5176")),n=a.n(o),s=(a("7f7f"),a("386d"),a("a481"),a("28a5"),a("cadf"),a("551c"),a("097d"),a("a6dc")),l=a("4ec3"),d=a("cdc6"),c=a("6908"),u={components:{Toolbar:c["a"]},data:function(){return{filters:{name:""},buttonList:[],options:[],users:[],defaultProps:{children:"children",label:"label"},statusList:[{name:"激活",value:!0},{name:"禁用",value:!1}],total:0,page:1,listLoading:!1,sels:[],currentRow:null,addDialogFormVisible:!1,editFormVisible:!1,editLoading:!1,editFormRules:{Name:[{required:!0,message:"请输入角色名",trigger:"blur"}],Enabled:[{required:!0,message:"请选择状态",trigger:"blur"}]},editForm:{Id:0,CreateBy:"",Name:"",Enabled:!1,AuthorityScope:-1,Dids:""},addFormVisible:!1,addLoading:!1,addFormRules:{Name:[{required:!0,message:"请输入角色名",trigger:"blur"}],Enabled:[{required:!0,message:"请选择状态",trigger:"blur"}]},addForm:{CreateBy:"",CreateId:"",Name:"",AuthorityScope:-1,Dids:"",Enabled:!0},isResouceShow:0}},computed:{showTags:function(){return this.editForm.Dids?this.editForm.Dids.split(","):[]}},methods:{handleCheckChangeAdd:function(e,t,a){this.addForm.Dids=this.addForm.Dids.replace(e.value+",",""),t&&(this.addForm.Dids+=e.value+","),console.log(e,t,a)},handleCheckChangeEdit:function(e,t,a){this.editForm.Dids?this.editForm.Dids=this.editForm.Dids.replace(e.value+",",""):this.editForm.Dids="",t&&(this.editForm.Dids+=e.value+","),console.log(e,t,a)},selectCurrentRow:function(e){this.currentRow=e},callFunction:function(e){this.filters={name:e.search},this[e.Func].apply(this,e)},formatEnabled:function(e,t){return e.Enabled?"正常":"未知"},formatCreateTime:function(e,t){return e.CreateTime&&""!=e.CreateTime?s["a"].formatDate.format(new Date(e.CreateTime),"yyyy-MM-dd"):""},handleCurrentChange:function(e){this.page=e,this.getRoles()},getRoles:function(){var e=this,t={page:this.page,key:this.filters.name};this.listLoading=!0,Object(l["M"])(t).then(function(t){e.total=t.data.response.dataCount,e.users=t.data.response.data,e.listLoading=!1})},handleDel:function(){var e=this,t=this.currentRow;t?this.$confirm("确认删除该记录吗?","提示",{type:"warning"}).then(function(){e.listLoading=!0;var a={id:t.Id};Object(l["jb"])(a).then(function(t){s["a"].isEmt.format(t)?e.listLoading=!1:(e.listLoading=!1,t.data.success?e.$message({message:"删除成功",type:"success"}):e.$message({message:t.data.msg,type:"error"}),e.getRoles())})}).catch(function(){}):this.$message({message:"请选择要编辑的一行数据！",type:"error"})},handleEdit:function(){var e=this,t=this.currentRow;if(this.options=[],t){var a={pid:0};Object(l["D"])(a).then(function(a){++e.isResouceShow,e.options.push(a.data.response),setTimeout(function(){e.$refs.treeEdit&&e.$refs.treeEdit.setCheckedKeys(t.Dids.split(","))},100)}),this.editFormVisible=!0,this.editForm=n()({},t)}else this.$message({message:"请选择要编辑的一行数据！",type:"error"})},handleAdd:function(){var e=this;this.addFormVisible=!0,this.options=[],this.addForm={CreateBy:"",Name:"",Enabled:"",AuthorityScope:-1,Dids:""};var t={pid:0};Object(l["D"])(t).then(function(t){++e.isResouceShow,e.options.push(t.data.response)})},editSubmit:function(){var e=this;this.$refs.editForm.validate(function(t){t&&e.$confirm("确认提交吗？","提示",{}).then(function(){e.editLoading=!0;var t=n()({},e.editForm);if(t.birth=t.birth&&""!=t.birth?s["a"].formatDate.format(new Date(t.birth),"yyyy-MM-dd"):s["a"].formatDate.format(new Date,"yyyy-MM-dd"),e.$refs.treeEdit){var a=e.$refs.treeEdit.getCheckedKeys();t.Dids=a.join(",")}Object(l["t"])(t).then(function(t){s["a"].isEmt.format(t)?e.editLoading=!1:t.data.success?(e.editLoading=!1,e.$message({message:t.data.msg,type:"success"}),e.$refs["editForm"].resetFields(),e.editFormVisible=!1,e.getRoles()):e.$message({message:t.data.msg,type:"error"})})})})},addSubmit:function(){var e=this,t=this;this.$refs.addForm.validate(function(a){a&&e.$confirm("确认提交吗？","提示",{}).then(function(){e.addLoading=!0;var a=n()({},e.addForm);a.birth=a.birth&&""!=a.birth?s["a"].formatDate.format(new Date(a.birth),"yyyy-MM-dd"):s["a"].formatDate.format(new Date,"yyyy-MM-dd");var r=JSON.parse(window.localStorage.user);if(r&&r.uID>0?(a.CreateId=r.uID,a.CreateBy=r.uRealName):(e.$message({message:"用户信息为空，先登录",type:"error"}),t.$router.replace(t.$route.query.redirect?t.$route.query.redirect:"/")),e.$refs.treeAdd){var i=e.$refs.treeAdd.getCheckedKeys();a.Dids=i.join(",")}Object(l["f"])(a).then(function(t){s["a"].isEmt.format(t)?e.addLoading=!1:t.data.success?(e.addLoading=!1,e.$message({message:t.data.msg,type:"success"}),e.$refs["addForm"].resetFields(),e.addFormVisible=!1,e.getRoles()):e.$message({message:t.data.msg,type:"error"})})})})},selsChange:function(e){this.sels=e},batchRemove:function(){this.$message({message:"该功能未开放",type:"warning"})},getButtonList2:function(e){var t=this,a=this;e.forEach(function(e){var r=t.$route.path.toLowerCase();e.path&&e.path.toLowerCase()==r?a.buttonList=e.children:e.children&&a.getButtonList(e.children)})}},mounted:function(){this.getRoles();var e=window.localStorage.router?JSON.parse(window.localStorage.router):[];this.buttonList=Object(d["a"])(this.$route.path,e)}},m=u,f=a("2877"),h=Object(f["a"])(m,r,i,!1,null,"6145e8cc",null);h.options.__file="Roles.vue";t["default"]=h.exports}}]);
//# sourceMappingURL=chunk-bf843d8a.ed731235.js.map