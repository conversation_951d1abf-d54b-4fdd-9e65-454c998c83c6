{"version": 3, "sources": ["webpack:///./src/views/I18n/index.vue?a42e", "webpack:///./src/views/I18n/local.js", "webpack:///src/views/I18n/index.vue", "webpack:///./src/views/I18n/index.vue?dbfe", "webpack:///./src/views/I18n/index.vue", "webpack:///./src/views/I18n/index.vue?4ed3"], "names": ["render", "_vm", "this", "_h", "$createElement", "_c", "_self", "staticClass", "staticStyle", "margin-top", "attrs", "slot", "margin-left", "_v", "_s", "$t", "size", "model", "value", "callback", "$$v", "lang", "expression", "label", "border", "display", "type", "margin", "gutter", "span", "xs", "placeholder", "date", "_l", "item", "key", "width", "data", "tableData", "fit", "highlight-current-row", "prop", "align", "staticRenderFns", "local", "zh", "i18nView", "title", "note", "datePlaceholder", "selectPlaceholder", "tableDate", "tableName", "tableAddress", "default", "primary", "success", "info", "warning", "danger", "one", "two", "three", "en", "es", "viewName", "I18nvue_type_script_lang_js_", "name", "address", "options", "computed", "get", "$store", "state", "language", "set", "$i18n", "locale", "dispatch", "watch", "setOptions", "created", "getLocaleMessage", "mergeLocaleMessage", "methods", "views_I18nvue_type_script_lang_js_", "component", "Object", "componentNormalizer", "__file", "__webpack_exports__", "_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_node_modules_css_loader_index_js_ref_6_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_2_node_modules_cache_loader_dist_cjs_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_5495408c_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__", "__webpack_require__", "_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_node_modules_css_loader_index_js_ref_6_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_2_node_modules_cache_loader_dist_cjs_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_5495408c_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0___default", "n"], "mappings": "yHAAA,IAAAA,EAAA,WAA0B,IAAAC,EAAAC,KAAaC,EAAAF,EAAAG,eAA0BC,EAAAJ,EAAAK,MAAAD,IAAAF,EAAwB,OAAAE,EAAA,OAAAA,EAAA,WAA+BE,YAAA,WAAAC,YAAA,CAAoCC,aAAA,SAAqB,CAAAJ,EAAA,OAAYE,YAAA,WAAAG,MAAA,CAA8BC,KAAA,UAAgBA,KAAA,UAAe,CAAAN,EAAA,QAAaG,YAAA,CAAaI,cAAA,SAAsB,CAAAX,EAAAY,GAAAZ,EAAAa,GAAAb,EAAAc,GAAA,wBAAAV,EAAA,OAAAA,EAAA,kBAA8EK,MAAA,CAAOM,KAAA,SAAeC,MAAA,CAAQC,MAAAjB,EAAA,KAAAkB,SAAA,SAAAC,GAA0CnB,EAAAoB,KAAAD,GAAaE,WAAA,SAAoB,CAAAjB,EAAA,YAAiBK,MAAA,CAAOa,MAAA,KAAAC,OAAA,KAA0B,CAAAvB,EAAAY,GAAA,UAAAR,EAAA,YAAkCK,MAAA,CAAOa,MAAA,KAAAC,OAAA,KAA0B,CAAAvB,EAAAY,GAAA,aAAAR,EAAA,YAAqCK,MAAA,CAAOa,MAAA,KAAAC,OAAA,KAA0B,CAAAvB,EAAAY,GAAA,iBAAAR,EAAA,UAAuCG,YAAA,CAAaC,aAAA,OAAAgB,QAAA,SAAsCf,MAAA,CAAQgB,KAAA,SAAe,CAAAzB,EAAAY,GAAAZ,EAAAa,GAAAb,EAAAc,GAAA,2BAAAV,EAAA,UAA+DG,YAAA,CAAamB,OAAA,mBAA2BjB,MAAA,CAAQkB,OAAA,KAAa,CAAAvB,EAAA,UAAeK,MAAA,CAAOmB,KAAA,GAAAC,GAAA,KAAmB,CAAAzB,EAAA,OAAYE,YAAA,SAAoB,CAAAF,EAAA,kBAAuBK,MAAA,CAAOqB,YAAA9B,EAAAc,GAAA,4BAAAW,KAAA,QAA+DT,MAAA,CAAQC,MAAAjB,EAAA,KAAAkB,SAAA,SAAAC,GAA0CnB,EAAA+B,KAAAZ,GAAaE,WAAA,WAAoB,GAAAjB,EAAA,OAAgBE,YAAA,SAAoB,CAAAF,EAAA,aAAkBK,MAAA,CAAOqB,YAAA9B,EAAAc,GAAA,+BAAmDE,MAAA,CAAQC,MAAAjB,EAAA,MAAAkB,SAAA,SAAAC,GAA2CnB,EAAAiB,MAAAE,GAAcE,WAAA,UAAqBrB,EAAAgC,GAAAhC,EAAA,iBAAAiC,GAAqC,OAAA7B,EAAA,aAAuB8B,IAAAD,EAAAhB,MAAAR,MAAA,CAAsBa,MAAAW,EAAAX,MAAAL,MAAAgB,EAAAhB,WAAyC,OAAAb,EAAA,OAAmBE,YAAA,SAAoB,CAAAF,EAAA,aAAkBE,YAAA,WAAAG,MAAA,CAA8BM,KAAA,UAAgB,CAAAf,EAAAY,GAAAZ,EAAAa,GAAAb,EAAAc,GAAA,wBAAAV,EAAA,aAA+DE,YAAA,WAAAG,MAAA,CAA8BM,KAAA,QAAAU,KAAA,YAAiC,CAAAzB,EAAAY,GAAAZ,EAAAa,GAAAb,EAAAc,GAAA,wBAAAV,EAAA,aAA+DE,YAAA,WAAAG,MAAA,CAA8BM,KAAA,QAAAU,KAAA,YAAiC,CAAAzB,EAAAY,GAAAZ,EAAAa,GAAAb,EAAAc,GAAA,wBAAAV,EAAA,aAA+DE,YAAA,WAAAG,MAAA,CAA8BM,KAAA,QAAAU,KAAA,SAA8B,CAAAzB,EAAAY,GAAAZ,EAAAa,GAAAb,EAAAc,GAAA,qBAAAV,EAAA,aAA4DE,YAAA,WAAAG,MAAA,CAA8BM,KAAA,QAAAU,KAAA,YAAiC,CAAAzB,EAAAY,GAAAZ,EAAAa,GAAAb,EAAAc,GAAA,wBAAAV,EAAA,aAA+DE,YAAA,WAAAG,MAAA,CAA8BM,KAAA,QAAAU,KAAA,WAAgC,CAAAzB,EAAAY,GAAAZ,EAAAa,GAAAb,EAAAc,GAAA,6BAAAV,EAAA,UAAiEK,MAAA,CAAOmB,KAAA,GAAAC,GAAA,KAAmB,CAAAzB,EAAA,YAAiBG,YAAA,CAAa4B,MAAA,QAAe1B,MAAA,CAAQ2B,KAAApC,EAAAqC,UAAAC,IAAA,GAAAC,wBAAA,GAAAhB,OAAA,KAAsE,CAAAnB,EAAA,mBAAwBK,MAAA,CAAOa,MAAAtB,EAAAc,GAAA,sBAAA0B,KAAA,OAAAL,MAAA,MAAAM,MAAA,YAAmFrC,EAAA,mBAAwBK,MAAA,CAAOa,MAAAtB,EAAAc,GAAA,sBAAA0B,KAAA,OAAAL,MAAA,MAAAM,MAAA,YAAmFrC,EAAA,mBAAwBK,MAAA,CAAOa,MAAAtB,EAAAc,GAAA,yBAAA0B,KAAA,cAA0D,gBAC5qFE,EAAA,GCAeC,EAAA,CACbC,GAAI,CACFC,SAAU,CACRC,MAAO,OACPC,KAAM,oBACNC,gBAAiB,QACjBC,kBAAmB,MACnBC,UAAW,KACXC,UAAW,KACXC,aAAc,KACdC,QAAS,OACTC,QAAS,OACTC,QAAS,OACTC,KAAM,OACNC,QAAS,OACTC,OAAQ,OACRC,IAAK,IACLC,IAAK,IACLC,MAAO,MAGXC,GAAI,CACFjB,SAAU,CACRC,MAAO,kBACPC,KAAM,gEACNC,gBAAiB,aACjBC,kBAAmB,SACnBC,UAAW,YACXC,UAAW,YACXC,aAAc,eACdC,QAAS,WACTC,QAAS,UACTC,QAAS,UACTC,KAAM,OACNC,QAAS,UACTC,OAAQ,SACRC,IAAK,MACLC,IAAK,MACLC,MAAO,UAGXE,GAAI,CACFlB,SAAU,CACRC,MAAO,kBACPC,KAAM,gEACNC,gBAAiB,aACjBC,kBAAmB,SACnBC,UAAW,YACXC,UAAW,YACXC,aAAc,eACdC,QAAS,WACTC,QAAS,UACTC,QAAS,UACTC,KAAM,OACNC,QAAS,UACTC,OAAQ,SACRC,IAAK,MACLC,IAAK,MACLC,MAAO,WCNbG,EAAA,WAEAC,EAAA,CACAC,KAAA,OACA9B,KAFA,WAGA,OACAL,KAAA,GACAM,UAAA,EACAN,KAAA,aACAmC,KAAA,MACAC,QAAA,kCAEA,CACApC,KAAA,aACAmC,KAAA,MACAC,QAAA,kCAEA,CACApC,KAAA,aACAmC,KAAA,MACAC,QAAA,kCAEA,CACApC,KAAA,aACAmC,KAAA,MACAC,QAAA,mCAEAC,QAAA,GACAnD,MAAA,KAGAoD,SAAA,CACAjD,KAAA,CACAkD,IADA,WAEA,OAAArE,KAAAsE,OAAAC,MAAAC,UAEAC,IAJA,SAIAtD,GACAnB,KAAA0E,MAAAC,OAAAxD,EACAnB,KAAAsE,OAAAM,SAAA,cAAAzD,MAIA0D,MAAA,CACA1D,KADA,WAEAnB,KAAA8E,eAGAC,QA7CA,WA8CA/E,KAAA0E,MAAAM,iBAAA,MAAAjB,KACA/D,KAAA0E,MAAAO,mBAAA,KAAAvC,EAAAmB,IACA7D,KAAA0E,MAAAO,mBAAA,KAAAvC,EAAAC,IACA3C,KAAA0E,MAAAO,mBAAA,KAAAvC,EAAAoB,KAEA9D,KAAA8E,cAEAI,QAAA,CACAJ,WADA,WAEA9E,KAAAmE,QAAA,CACA,CACAnD,MAAA,IACAK,MAAArB,KAAAa,GAAA,iBAEA,CACAG,MAAA,IACAK,MAAArB,KAAAa,GAAA,iBAEA,CACAG,MAAA,IACAK,MAAArB,KAAAa,GAAA,uBCzH8VsE,EAAA,0BCQ9VC,EAAgBC,OAAAC,EAAA,KAAAD,CACdF,EACArF,EACA2C,GACF,EACA,KACA,WACA,MAIA2C,EAAAjB,QAAAoB,OAAA,YACeC,EAAA,WAAAJ,sECpBf,IAAAK,EAAAC,EAAA,QAAAC,EAAAD,EAAAE,EAAAH,GAAmfE,EAAG", "file": "js/chunk-c75b8e6e.73103030.js", "sourcesContent": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',[_c('el-card',{staticClass:\"box-card\",staticStyle:{\"margin-top\":\"40px\"}},[_c('div',{staticClass:\"clearfix\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('span',{staticStyle:{\"margin-left\":\"10px\"}},[_vm._v(_vm._s(_vm.$t('i18nView.title')))])]),_c('div',[_c('el-radio-group',{attrs:{\"size\":\"small\"},model:{value:(_vm.lang),callback:function ($$v) {_vm.lang=$$v},expression:\"lang\"}},[_c('el-radio',{attrs:{\"label\":\"zh\",\"border\":\"\"}},[_vm._v(\"简体中文\")]),_c('el-radio',{attrs:{\"label\":\"en\",\"border\":\"\"}},[_vm._v(\"English\")]),_c('el-radio',{attrs:{\"label\":\"es\",\"border\":\"\"}},[_vm._v(\"Español\")])],1),_c('el-tag',{staticStyle:{\"margin-top\":\"15px\",\"display\":\"block\"},attrs:{\"type\":\"info\"}},[_vm._v(_vm._s(_vm.$t('i18nView.note')))])],1)]),_c('el-row',{staticStyle:{\"margin\":\"100px 15px 50px\"},attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":12,\"xs\":24}},[_c('div',{staticClass:\"block\"},[_c('el-date-picker',{attrs:{\"placeholder\":_vm.$t('i18nView.datePlaceholder'),\"type\":\"date\"},model:{value:(_vm.date),callback:function ($$v) {_vm.date=$$v},expression:\"date\"}})],1),_c('div',{staticClass:\"block\"},[_c('el-select',{attrs:{\"placeholder\":_vm.$t('i18nView.selectPlaceholder')},model:{value:(_vm.value),callback:function ($$v) {_vm.value=$$v},expression:\"value\"}},_vm._l((_vm.options),function(item){return _c('el-option',{key:item.value,attrs:{\"label\":item.label,\"value\":item.value}})}),1)],1),_c('div',{staticClass:\"block\"},[_c('el-button',{staticClass:\"item-btn\",attrs:{\"size\":\"small\"}},[_vm._v(_vm._s(_vm.$t('i18nView.default')))]),_c('el-button',{staticClass:\"item-btn\",attrs:{\"size\":\"small\",\"type\":\"primary\"}},[_vm._v(_vm._s(_vm.$t('i18nView.primary')))]),_c('el-button',{staticClass:\"item-btn\",attrs:{\"size\":\"small\",\"type\":\"success\"}},[_vm._v(_vm._s(_vm.$t('i18nView.success')))]),_c('el-button',{staticClass:\"item-btn\",attrs:{\"size\":\"small\",\"type\":\"info\"}},[_vm._v(_vm._s(_vm.$t('i18nView.info')))]),_c('el-button',{staticClass:\"item-btn\",attrs:{\"size\":\"small\",\"type\":\"warning\"}},[_vm._v(_vm._s(_vm.$t('i18nView.warning')))]),_c('el-button',{staticClass:\"item-btn\",attrs:{\"size\":\"small\",\"type\":\"danger\"}},[_vm._v(_vm._s(_vm.$t('i18nView.danger')))])],1)]),_c('el-col',{attrs:{\"span\":12,\"xs\":24}},[_c('el-table',{staticStyle:{\"width\":\"100%\"},attrs:{\"data\":_vm.tableData,\"fit\":\"\",\"highlight-current-row\":\"\",\"border\":\"\"}},[_c('el-table-column',{attrs:{\"label\":_vm.$t('i18nView.tableName'),\"prop\":\"name\",\"width\":\"100\",\"align\":\"center\"}}),_c('el-table-column',{attrs:{\"label\":_vm.$t('i18nView.tableDate'),\"prop\":\"date\",\"width\":\"120\",\"align\":\"center\"}}),_c('el-table-column',{attrs:{\"label\":_vm.$t('i18nView.tableAddress'),\"prop\":\"address\"}})],1)],1)],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "\r\nexport default {\r\n  zh: {\r\n    i18nView: {\r\n      title: '切换语言',\r\n      note: '本项目国际化基于 vue-i18n',\r\n      datePlaceholder: '请选择日期',\r\n      selectPlaceholder: '请选择',\r\n      tableDate: '日期',\r\n      tableName: '姓名',\r\n      tableAddress: '地址',\r\n      default: '默认按钮',\r\n      primary: '主要按钮',\r\n      success: '成功按钮',\r\n      info: '信息按钮',\r\n      warning: '警告按钮',\r\n      danger: '危险按钮',\r\n      one: '一',\r\n      two: '二',\r\n      three: '三'\r\n    }\r\n  },\r\n  en: {\r\n    i18nView: {\r\n      title: 'Switch Language',\r\n      note: 'The internationalization of this project is based on vue-i18n',\r\n      datePlaceholder: 'Pick a day',\r\n      selectPlaceholder: 'Select',\r\n      tableDate: 'tableDate',\r\n      tableName: 'tableName',\r\n      tableAddress: 'tableAddress',\r\n      default: 'default:',\r\n      primary: 'primary',\r\n      success: 'success',\r\n      info: 'info',\r\n      warning: 'warning',\r\n      danger: 'danger',\r\n      one: 'One',\r\n      two: 'Two',\r\n      three: 'Three'\r\n    }\r\n  },\r\n  es: {\r\n    i18nView: {\r\n      title: 'Switch Language',\r\n      note: 'The internationalization of this project is based on vue-i18n',\r\n      datePlaceholder: 'Pick a day',\r\n      selectPlaceholder: 'Select',\r\n      tableDate: 'tableDate',\r\n      tableName: 'tableName',\r\n      tableAddress: 'tableAddress',\r\n      default: 'default:',\r\n      primary: 'primary',\r\n      success: 'success',\r\n      info: 'info',\r\n      warning: 'warning',\r\n      danger: 'danger',\r\n      one: 'One',\r\n      two: 'Two',\r\n      three: 'Three'\r\n    }\r\n  }\r\n}\r\n", "<template>\r\n  <div>\r\n    <el-card class=\"box-card\" style=\"margin-top:40px;\">\r\n      <div slot=\"header\" class=\"clearfix\">\r\n        <span style=\"margin-left:10px;\">{{ $t('i18nView.title') }}</span>\r\n      </div>\r\n      <div>\r\n        <el-radio-group v-model=\"lang\" size=\"small\">\r\n          <el-radio label=\"zh\" border>简体中文</el-radio>\r\n          <el-radio label=\"en\" border>English</el-radio>\r\n          <el-radio label=\"es\" border>Español</el-radio>\r\n        </el-radio-group>\r\n        <el-tag style=\"margin-top:15px;display:block;\" type=\"info\">{{ $t('i18nView.note') }}</el-tag>\r\n      </div>\r\n    </el-card>\r\n\r\n    <el-row :gutter=\"20\" style=\"margin:100px 15px 50px;\">\r\n      <el-col :span=\"12\" :xs=\"24\">\r\n        <div class=\"block\">\r\n          <el-date-picker v-model=\"date\" :placeholder=\"$t('i18nView.datePlaceholder')\" type=\"date\" />\r\n        </div>\r\n        <div class=\"block\">\r\n          <el-select v-model=\"value\" :placeholder=\"$t('i18nView.selectPlaceholder')\">\r\n            <el-option\r\n              v-for=\"item in options\"\r\n              :key=\"item.value\"\r\n              :label=\"item.label\"\r\n              :value=\"item.value\"\r\n            />\r\n          </el-select>\r\n        </div>\r\n        <div class=\"block\">\r\n          <el-button class=\"item-btn\" size=\"small\">{{ $t('i18nView.default') }}</el-button>\r\n          <el-button class=\"item-btn\" size=\"small\" type=\"primary\">{{ $t('i18nView.primary') }}</el-button>\r\n          <el-button class=\"item-btn\" size=\"small\" type=\"success\">{{ $t('i18nView.success') }}</el-button>\r\n          <el-button class=\"item-btn\" size=\"small\" type=\"info\">{{ $t('i18nView.info') }}</el-button>\r\n          <el-button class=\"item-btn\" size=\"small\" type=\"warning\">{{ $t('i18nView.warning') }}</el-button>\r\n          <el-button class=\"item-btn\" size=\"small\" type=\"danger\">{{ $t('i18nView.danger') }}</el-button>\r\n        </div>\r\n      </el-col>\r\n      <el-col :span=\"12\" :xs=\"24\">\r\n        <el-table :data=\"tableData\" fit highlight-current-row border style=\"width: 100%\">\r\n          <el-table-column :label=\"$t('i18nView.tableName')\" prop=\"name\" width=\"100\" align=\"center\" />\r\n          <el-table-column :label=\"$t('i18nView.tableDate')\" prop=\"date\" width=\"120\" align=\"center\" />\r\n          <el-table-column :label=\"$t('i18nView.tableAddress')\" prop=\"address\" />\r\n        </el-table>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport local from './local'\r\nconst viewName = 'i18nView'\r\n\r\nexport default {\r\n  name: 'I18n',\r\n  data() {\r\n    return {\r\n      date: '',\r\n      tableData: [{\r\n        date: '2016-05-03',\r\n        name: 'Tom',\r\n        address: 'No. 189, Grove St, Los Angeles'\r\n      },\r\n      {\r\n        date: '2016-05-02',\r\n        name: 'Tom',\r\n        address: 'No. 189, Grove St, Los Angeles'\r\n      },\r\n      {\r\n        date: '2016-05-04',\r\n        name: 'Tom',\r\n        address: 'No. 189, Grove St, Los Angeles'\r\n      },\r\n      {\r\n        date: '2016-05-01',\r\n        name: 'Tom',\r\n        address: 'No. 189, Grove St, Los Angeles'\r\n      }],\r\n      options: [],\r\n      value: ''\r\n    }\r\n  },\r\n  computed: {\r\n    lang: {\r\n      get() {\r\n        return this.$store.state.language\r\n      },\r\n      set(lang) {\r\n        this.$i18n.locale = lang\r\n        this.$store.dispatch('setLanguage', lang)\r\n      }\r\n    }\r\n  },\r\n  watch: {\r\n    lang() {\r\n      this.setOptions()\r\n    }\r\n  },\r\n  created() {\r\n    if (!this.$i18n.getLocaleMessage('en')[viewName]) {\r\n      this.$i18n.mergeLocaleMessage('en', local.en)\r\n      this.$i18n.mergeLocaleMessage('zh', local.zh)\r\n      this.$i18n.mergeLocaleMessage('es', local.es)\r\n    }\r\n    this.setOptions() // set default select options\r\n  },\r\n  methods: {\r\n    setOptions() {\r\n      this.options = [\r\n        {\r\n          value: '1',\r\n          label: this.$t('i18nView.one')\r\n        },\r\n        {\r\n          value: '2',\r\n          label: this.$t('i18nView.two')\r\n        },\r\n        {\r\n          value: '3',\r\n          label: this.$t('i18nView.three')\r\n        }\r\n      ]\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.box-card {\r\n  width: 600px;\r\n  max-width: 100%;\r\n  margin: 20px auto;\r\n}\r\n.item-btn{\r\n  margin-bottom: 15px;\r\n  margin-left: 0px;\r\n}\r\n.block {\r\n  padding: 25px;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=5495408c&scoped=true&\"\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=5495408c&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"5495408c\",\n  null\n  \n)\n\ncomponent.options.__file = \"index.vue\"\nexport default component.exports", "import mod from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../node_modules/css-loader/index.js??ref--6-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=5495408c&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../node_modules/css-loader/index.js??ref--6-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=5495408c&scoped=true&lang=css&\""], "sourceRoot": ""}