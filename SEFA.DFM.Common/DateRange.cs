using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SEFA.DFM.Common
{
    public class DateRange
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }

        // 构造函数
        public DateRange(DateTime startDate, DateTime endDate)
        {
            StartDate = startDate;
            EndDate = endDate;
        }

        // 获取日期范围列表
        public List<DateTime> GetDatesInRange()
        {
            var datesInRange = new List<DateTime>();
            for (DateTime date = StartDate; date <= EndDate; date = date.AddDays(1))
            {
                datesInRange.Add(date);
            }
            return datesInRange;
        }
    }
}
