namespace SEFA.DFM.Common.EnterpriseWeChatCommon;


/*
 {
   "touser" : "UserID1|UserID2|UserID3",
   "toparty" : "PartyID1|PartyID2",
   "totag" : "TagID1 | TagID2",
   "msgtype" : "text",
   "agentid" : 1,
   "text" : {
       "content" : "你的快递已到，请携带工卡前往邮件中心领取。\n出发前可查看<a href=\"http://work.weixin.qq.com\">邮件中心视频实况</a>，聪明避开排队。"
   },
   "safe":0,
   "enable_id_trans": 0,
   "enable_duplicate_check": 0,
   "duplicate_check_interval": 1800
}
 */
public class Text
{
    /// <summary>
    /// 文本为荣
    /// </summary>
    public string content { get; set; }
}

public class EnterpriseWeChatMessage
{
    /// <summary>
    /// UserID1|UserID2|UserID3
    /// </summary>
    public string touser { get; set; }
    /// <summary>
    /// PartyID1|PartyID2
    /// </summary>
    public string toparty { get; set; }
    /// <summary>
    /// TagID1 | TagID2
    /// </summary>
    public string totag { get; set; }
    /// <summary>
    /// text
    /// </summary>
    public string msgtype { get; set; }
    /// <summary>
    /// Agentid
    /// </summary>
    public int agentid { get; set; }
    /// <summary>
    /// Text
    /// </summary>
    public Text text { get; set; }
    /// <summary>
    /// Safe
    /// </summary>
    public int safe { get; set; }
    /// <summary>
    /// Enable_id_trans
    /// </summary>
    public int enable_id_trans { get; set; }
    /// <summary>
    /// 是否拦截重复消息发送
    /// </summary>
    public int enable_duplicate_check { get; set; } = 0;
    /// <summary>
    /// 拦截重复发送时间 秒
    /// </summary>
    public int duplicate_check_interval { get; set; }
}

