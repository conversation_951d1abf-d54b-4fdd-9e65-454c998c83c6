using System.Net.Sockets;
using System.Text;
using SEFA.Base.Common;
using SEFA.DFM.Common.Exception;
using SEFA.DFM.Common.ViewModels;

namespace SEFA.DFM.Common;

public class ColosPrinter : IPrinter
{
    private readonly string colosServer = Appsettings.app("Print", "ColosHostname");
    private readonly string colosTimeoutStr = string.IsNullOrEmpty(Appsettings.app("Print", "ColosTimeout")) ? "15000" : Appsettings.app("Print", "ColosTimeout");
    private readonly string colosPortStr = string.IsNullOrEmpty(Appsettings.app("Print", "ColosPort")) ? "0" : Appsettings.app("Print", "ColosPort");
    private readonly string colosEncoding = string.IsNullOrEmpty(Appsettings.app("Print", "ColosEncoding")) ? "ASCII" : Appsettings.app("Print", "ColosEncoding");
    public PrinterInfo print(PrintBaseInfo printBaseInfo)
    {
        var printerInfo = new PrinterInfo();
        Dictionary<string, string> printProperties = printBaseInfo.printProperties;
        string printTemplate = printBaseInfo.printTemplate;
        Dictionary<string, object> printParameters = printBaseInfo.printParameters;
        int printNum = printBaseInfo.printNum;
        string colosPrinterName = PrinterHelper.GetPropertyValue(printProperties, "PrinterName", null);
        object colosPrinterShared = PrinterHelper.GetPropertyValue(printProperties, "Shared", null);
        if (colosPrinterShared == null)
        {
            colosPrinterShared = false;
        }

        string colosCommand = string.Empty;
        string colosParameters = string.Empty;
        string commandText = printTemplate;
        bool useTemplateOnly =
            PrinterHelper.GetPropertyValue(printProperties, "UseOnlyTemplateCommand", "false").ToLower() == "true";
        if (string.IsNullOrEmpty(commandText))
        {
            using (Dictionary<string, object>.KeyCollection.Enumerator
                   enumerator = printParameters.Keys.GetEnumerator())
            {
                while (enumerator.MoveNext())
                {
                    string key = enumerator.Current;
                    if (colosParameters.Length > 0)
                    {
                        colosParameters += "|";
                    }

                    colosParameters += string.Format("{0}={1}", key, printParameters[key]);
                }

                goto IL_130;
            }
        }

        if (useTemplateOnly)
        {
            if (!printParameters.ContainsKey("printer_name"))
            {
                printParameters.Add("printer_name", colosPrinterName);
            }

            // if (!printParameters.ContainsKey("template_id"))
            // {
            //     printParameters.Add("template_id", base.TemplateData.id);
            // }
        }

        commandText = PrinterHelper.ReplaceKeywords(printParameters, commandText);
        IL_130:
        colosCommand = string.Format("devices|lookup|{0}|select|{2}|@SelectMode=DownloadAndSelect",
            colosPrinterName, string.IsNullOrEmpty(colosParameters) ? commandText : colosParameters);
        string labelCountCommand = PrinterHelper.GetPropertyValue(printProperties, "LabelCountCommand", null);
        if (string.IsNullOrEmpty(labelCountCommand))
        {
            labelCountCommand = "@Allocation";
        }

        // if (printNum > 0 && base.PrinterData.type == 3)
        // {
        //     colosCommand += string.Format("|{0}={1}", labelCountCommand, printNum);
        // }

        if (useTemplateOnly)
        {
            colosCommand = (commandText ?? "");
        }

        bool debugOn = PrinterHelper.GetPropertyValue(printProperties, "DebugMode", null).ToUpper() == "TRUE";
        // if (base.PrinterData.status != 2)
        // {
        // base.UpdatePrintHistory(colosCommand);
        if (!debugOn)
        {
            if (colosServer == string.Empty)
            {
                throw new ApplicationException("Colos server is not configured.");
            }

            if (Convert.ToBoolean(colosPrinterShared))
            {
               SendCommand(colosServer, string.Format("devices|lookup|{0}|enable", colosPrinterName));
                Thread.Sleep(1000);
            }

            try
            {
                SendCommand(colosServer, colosCommand);
            }
            finally
            {
                if (Convert.ToBoolean(colosPrinterShared))
                {
                   SendCommand(colosServer,
                        string.Format("devices|lookup|{0}|disable", colosPrinterName));
                }
            }
        }
        return printerInfo;
        // }
    }

    public string SendCommand(string colosServer, string colosCommand)
    {
        if (colosServer == null || colosServer == "")
        {
            throw new ConfigurationException("Colos server is not configured.");
        }

        string colosResponse = "";
        TcpClient c = new TcpClient();
        NetworkStream ns = null;
        int colosPort = Int32.Parse(colosTimeoutStr);
        int timeout = Int32.Parse(colosTimeoutStr);
        if (colosPort < 1025 || colosPort > 65535)
        {
            throw new ConfigurationException(
                string.Format("Colos port must be between 1025 and 65535. {0} is not valid.", colosPort));
        }

        try
        {
            c.Connect(colosServer, colosPort);
            if (!c.Connected)
            {
                throw new ConfigurationException("Client failed to connect to Colos server.");
            }

            if (!colosCommand.Contains(Convert.ToChar(13)))
            {
                colosCommand += Environment.NewLine;
            }

            ns = c.GetStream();
            if (!ns.CanRead || !ns.CanWrite)
            {
                throw new ConfigurationException("Error accessing stream to Colos server.");
            }

            ns.ReadTimeout = timeout;
            Encoding enc = Encoding.GetEncoding(colosEncoding);
            byte[] cmd = enc.GetBytes(colosCommand);
            ns.Write(cmd, 0, cmd.Length);
            byte[] res = new byte[c.ReceiveBufferSize];
            ns.Read(res, 0, c.ReceiveBufferSize);
            colosResponse = enc.GetString(res);
            if (colosResponse.Contains(Convert.ToChar(0)))
            {
                colosResponse = colosResponse.Substring(0, colosResponse.IndexOf(Convert.ToChar(0)));
            }

            if (colosResponse.Contains(Convert.ToChar(13)))
            {
                colosResponse = colosResponse.Substring(0, colosResponse.IndexOf(Convert.ToChar(13)));
            }

            if (colosResponse.Contains(Convert.ToChar(10)))
            {
                colosResponse = colosResponse.Substring(0, colosResponse.IndexOf(Convert.ToChar(10)));
            }

            string[] responseArray = colosResponse.Split(new char[]
            {
                '|'
            });
            if (responseArray.Length > 0)
            {
                string responseCode = responseArray[0];
                if (responseCode.ToUpper() != "OK")
                {
                    throw new ConfigurationException(string.Format(
                        "Failed to print. CoLOS Response: {0}\r\n\r\nPrint command: {1}", colosResponse, colosCommand));
                }
            }
        }
        finally
        {
            try
            {
                if (ns != null)
                {
                    ns.Close();
                }

                if (c != null)
                {
                    c.Close();
                }
            }
            catch
            {
            }
        }

        return colosResponse;
    }

    // Token: 0x060009C4 RID: 2500 RVA: 0x0025B150 File Offset: 0x00259350
    public string trimSurroundingSlashes(string untrimmedString)
    {
        return untrimmedString.TrimStart(new char[]
        {
            '\\'
        }).TrimEnd(new char[]
        {
            '\\'
        });
    }

    // Token: 0x060009C5 RID: 2501 RVA: 0x0025B184 File Offset: 0x00259384
    public static string PhotoBase64ImgSrc(string fileNameandPath)
    {
        byte[] byteArray = File.ReadAllBytes(fileNameandPath);
        string base64 = Convert.ToBase64String(byteArray);
        return string.Format("data:image/gif;base64,{0}", base64);
    }

    // Token: 0x060009C6 RID: 2502 RVA: 0x0025B1AC File Offset: 0x002593AC
    // public override string Preview()
    // {
    //     string colosPrinterName = PrinterHelper.GetPropertyValue(printProperties, "PrinterName", null);
    //     object colosPrinterShared = PrinterHelper.GetPropertyValue(printProperties, "Shared", null);
    //     string templateId = base.TemplateData.id;
    //     string colosServer = this.trimSurroundingSlashes(Configuration.GetValue("Colos", "Hostname", ""));
    //     string colosPreviewLocation =
    //         this.trimSurroundingSlashes(Configuration.GetValue("Colos", "PreviewFolder", "C:\\Temp"));
    //     string colosPreviewRetrievalLocation =
    //         this.trimSurroundingSlashes(Configuration.GetValue("Colos", "PreviewRetrievalFolder", "\\Temp"));
    //     string imageFileExtension = Configuration.GetValue("Colos", "ImageType", "Gif");
    //     string previewFileName = string.Concat(new string[]
    //     {
    //         colosPrinterName,
    //         "_",
    //         templateId,
    //         "_",
    //         DateTime.Now.ToString("yyyyMMddHHmmssfff"),
    //         ".",
    //         imageFileExtension
    //     });
    //     if (colosPrinterShared == null)
    //     {
    //         colosPrinterShared = false;
    //     }
    //
    //     string colosParameters = string.Empty;
    //     string commandText = base.TemplateData.data;
    //     if (string.IsNullOrEmpty(commandText))
    //     {
    //         using (Dictionary<string, object>.KeyCollection.Enumerator
    //                enumerator = base.Parameters.Keys.GetEnumerator())
    //         {
    //             while (enumerator.MoveNext())
    //             {
    //                 string key = enumerator.Current;
    //                 if (colosParameters.Length > 0)
    //                 {
    //                     colosParameters += "|";
    //                 }
    //
    //                 colosParameters += string.Format("{0}={1}", key, base.Parameters[key]);
    //             }
    //
    //             goto IL_183;
    //         }
    //     }
    //
    //     commandText = base.ReplaceKeywords(commandText);
    //     IL_183:
    //     string colosCommand = string.Format("devices|lookup|{0}|preview|{1}|{2}|@location={3}{4}{5}|@format={6}",
    //         new object[]
    //         {
    //             colosPrinterName,
    //             templateId,
    //             string.IsNullOrEmpty(colosParameters) ? commandText : colosParameters,
    //             colosPreviewLocation,
    //             "\\",
    //             previewFileName,
    //             imageFileExtension
    //         });
    //     bool debugOn = PrinterHelper.GetPropertyValue(printProperties, "DebugMode", null).ToUpper() == "TRUE";
    //     // base.UpdatePrintHistory(colosCommand);
    //     if (!debugOn)
    //     {
    //         if (string.IsNullOrEmpty(colosServer))
    //         {
    //             throw new ConfigurationException("Colos server is not configured.");
    //         }
    //
    //         if (Convert.ToBoolean(colosPrinterShared))
    //         {
    //             ColosPrinter.SendCommand(colosServer, string.Format("devices|lookup|{0}|enable", colosPrinterName));
    //             Thread.Sleep(1000);
    //         }
    //
    //         try
    //         {
    //             ColosPrinter.SendCommand(colosServer, colosCommand);
    //         }
    //         finally
    //         {
    //             if (Convert.ToBoolean(colosPrinterShared))
    //             {
    //                 ColosPrinter.SendCommand(colosServer,
    //                     string.Format("devices|lookup|{0}|disable", colosPrinterName));
    //             }
    //         }
    //
    //         string UNC = string.Concat(new string[]
    //         {
    //             "\\\\",
    //             colosServer,
    //             "\\",
    //             colosPreviewRetrievalLocation,
    //             "\\",
    //             previewFileName
    //         });
    //         return ColosPrinter.PhotoBase64ImgSrc(UNC);
    //     }
    //
    //     return "";
    // }
}