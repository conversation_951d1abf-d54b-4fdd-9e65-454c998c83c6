using System.Text;
using SEFA.Base.Common;
using SEFA.DFM.Common.ViewModels;

namespace SEFA.DFM.Common;

public class FileWriter : IPrinter
{
    private readonly string FilePath = Appsettings.app("Print", "FilePath");

    public PrinterInfo print(PrintBaseInfo printBaseInfo)
    {
        var printerInfo = new PrinterInfo();
        Dictionary<string, string> printProperties = printBaseInfo.printProperties;
        string printTemplate = printBaseInfo.printTemplate;
        Dictionary<string, object> printParameters = printBaseInfo.printParameters;
        int printNum = printBaseInfo.printNum;
        bool debugOn = PrinterHelper.GetPropertyValue(printProperties, "DebugMode", "FALSE").ToUpper() == "TRUE";
        string fileExtension = PrinterHelper.GetPropertyValue(printProperties, "FileExtension", "PDF");
        string fileNamingType = PrinterHelper.GetPropertyValue(printProperties, "FileNamingType", "PDF");

        string outputText = printTemplate;
        outputText = PrinterHelper.ReplaceKeywords(printParameters, printTemplate);
        Dictionary<string, object> parameters = printParameters;
        string formattedFileExtension = "";
        if (fileExtension != null)
        {
            formattedFileExtension = (fileExtension.StartsWith(".") ? fileExtension : ("." + fileExtension));
        }

        string fileName = DateTime.Now.ToString("yyyy-MM-dd-HHmmssfff");
        object result = null;
        if (fileNamingType != null)
        {
            if (fileNamingType.Equals("1"))
            {
                parameters.TryGetValue("sublot_id", out result);
            }
            else if (fileNamingType.Equals("2"))
            {
                parameters.TryGetValue("gtin", out result);
            }

            if (result != null)
            {
                fileName = result.ToString();
            }
        }

        string filePath = ".\\";
        if (!string.IsNullOrEmpty(FilePath))
        {
            filePath = FilePath;
        }

        string formattedFilePath = Path.Combine(filePath, fileName + formattedFileExtension);
        // base.UpdatePrintHistory("filename - " + formattedFilePath + Environment.NewLine + outputText);
        if (!debugOn)
        {
            Encoding encoding =
                Encoding.GetEncoding(PrinterHelper.GetPropertyValue(printProperties, "Encoding", "UTF-8"));
            File.WriteAllText(formattedFilePath, outputText, encoding);
        }

        return printerInfo;
    }
}