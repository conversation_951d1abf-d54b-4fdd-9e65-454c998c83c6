using System.Net.Sockets;
using System.Text;
using Newtonsoft.Json;
using SEFA.Base.Common.LogHelper;
using SEFA.DFM.Common.Exception;
using SEFA.DFM.Common.ViewModels;

namespace SEFA.DFM.Common;

public class GenericPrinter : IPrinter
{
    public PrinterInfo print(PrintBaseInfo printBaseInfo)
    {
        var printerInfo = new PrinterInfo();
        Dictionary<string, string> printProperties = printBaseInfo.printProperties;
        string printTemplate = printBaseInfo.printTemplate;
        Dictionary<string, object> printParameters = printBaseInfo.printParameters;
        int printNum = printBaseInfo.printNum;
        string ipAddress = PrinterHelper.GetPropertyValue(printProperties, "IPAddress", null);
        int port = Convert.ToInt32(PrinterHelper.GetPropertyValue(printProperties, "Port", "20000"));
        bool debugOn = PrinterHelper.GetPropertyValue(printProperties, "DebugMode", "FALSE").ToUpper() == "TRUE";
        Encoding encoding = Encoding.GetEncoding(PrinterHelper.GetPropertyValue(printProperties, "Encoding", "UTF-8"));
        List<GenericPrinter.PrinterCommand> printerCommands =
            JsonConvert.DeserializeObject<List<GenericPrinter.PrinterCommand>>(printTemplate);
        TcpClient client = new TcpClient();
        client.SendTimeout = 30000;
        client.ReceiveTimeout = 30000;
        NetworkStream netStream = null;
        StringBuilder templateResult = new StringBuilder();
        if (!debugOn)
        {
            client.Connect(ipAddress, port);
            netStream = client.GetStream();
        }

        try
        {
            foreach (GenericPrinter.PrinterCommand cmd in printerCommands)
            {
                cmd.command = PrinterHelper.ReplaceKeywords(printParameters, cmd.command);
                // if (base.PrinterData.status != 2)
                // {
                // base.UpdatePrintHistory(cmd.command);
                if (!debugOn)
                {
                    string response = this.SendCommand(netStream, cmd.command, cmd.success, cmd.error, encoding);
                    templateResult.AppendLine(string.Format("{0} - {1}", cmd.command, response));
                    if (!response.Contains(cmd.success))
                    {
                        SerilogServer.LogDebug($"打印机返回错误信息，内容如下:[{response}]", "GenericPrinterDebug");
                        throw new DeveloperException(string.Format("Printer Response failure message '{0}'", response));
                    }
                }
            }
            // }
        }
        catch (System.Exception ex)
        {
            SerilogServer.LogError(ex, $"发送打印内容到打印机失败，报错内容如下:[{ex.ToString()}]", "GenericPrinterError");
            throw;
        }
        finally
        {
            if (netStream != null)
            {
                netStream.Close();
            }

            client.Close();
            // base.UpdatePrintHistory(templateResult.ToString());
        }

        return printerInfo;
    }

    private string SendCommand(Stream stream, string command, string success, string error, Encoding encoding)
    {
        string result = string.Empty;
        if (!command.Contains(char.ConvertFromUtf32(13)))
        {
            command += Environment.NewLine;
        }

        try
        {
            byte[] commandData = encoding.GetBytes(command);
            stream.Write(commandData, 0, commandData.Length);
            for (int i = 0; i < 10; i++)
            {
                byte[] responseData = new byte[256];
                int readCount = stream.Read(responseData, 0, responseData.Length);
                Thread.Sleep(10);
                string buffer = encoding.GetString(responseData, 0, readCount);
                result += buffer;
                if (string.IsNullOrEmpty(error))
                {
                    if (result.Contains(success))
                    {
                        break;
                    }

                    if (result.Contains(char.ConvertFromUtf32(21)))
                    {
                        break;
                    }
                }
                else if (result.Contains(success) || result.Contains(error) ||
                         result.Contains(char.ConvertFromUtf32(21)))
                {
                    break;
                }
            }
        }
        catch (System.Exception ex)
        {
            throw new System.Exception(string.Format("SendCommand failure message '{0}'", ex.Message));
        }

        return result;
    }

    public class PrinterCommand
    {
        public string command { get; set; }

        public string success { get; set; }

        public string error { get; set; }
    }
}