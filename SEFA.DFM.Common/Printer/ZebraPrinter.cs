using Newtonsoft.Json;
using SEFA.Base.Common.LogHelper;
using SEFA.DFM.Common.ViewModels;
using System.Net.Sockets;
using System.Text;

namespace SEFA.DFM.Common;

public class ZebraPrinter : IPrinter
{
    public PrinterInfo print(PrintBaseInfo printBaseInfo)
    {
        var printerInfo = new PrinterInfo();

        SerilogServer.LogDebug($"ZebraPrinter，print(PrintBaseInfo printBaseInfo)：{JsonConvert.SerializeObject(printBaseInfo)}", "PrintLog");
        Dictionary<string, string> printProperties = printBaseInfo.printProperties;
        string printTemplate = printBaseInfo.printTemplate;
        Dictionary<string, object> printParameters = printBaseInfo.printParameters;
        int printNum = printBaseInfo.printNum;
        string ipAddress = PrinterHelper.GetPropertyValue(printProperties, "IPAddress", null);
        int port = Convert.ToInt32(PrinterHelper.GetPropertyValue(printProperties, "Port", "9100"));
        string outputText = printTemplate;
        outputText = PrinterHelper.ReplaceKeywords(printParameters, outputText);
        SerilogServer.LogDebug( $"打印信息:{outputText}", "PrintLog");
        printerInfo.Data = outputText;
        bool debugOn = PrinterHelper.GetPropertyValue(printProperties, "DebugMode", "FALSE").ToUpper() == "TRUE";
        // base.UpdatePrintHistory(outputText);
        if (!debugOn)
        {
            Encoding encoding =
                Encoding.GetEncoding(PrinterHelper.GetPropertyValue(printProperties, "Encoding", "UTF-8"));
            byte[] bytesToWrite = encoding.GetBytes(outputText);
            TcpClient client = new TcpClient();
            NetworkStream ns = null;
            try
            {
                client.Connect(ipAddress, port);
                ns = client.GetStream();
                if (ns.CanWrite)
                {
                    for (int i = 1; i <= printNum; i++)
                    {
                        ns.Write(bytesToWrite, 0, bytesToWrite.Length);
                    }
                }
            }
            catch (System.Exception ex)
            {
                SerilogServer.LogError(ex,$"打印异常，参数信息为{JsonConvert.SerializeObject(printBaseInfo)}", "PrintLog");
                printerInfo.AddException(ex);
            }
            finally
            {
                if (ns != null)
                {
                    ns.Close();
                }

                client.Close();
            }
        }
        return printerInfo;
    }
}