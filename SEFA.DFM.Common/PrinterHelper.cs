using System.Text.RegularExpressions;
using SEFA.DFM.Common.Exception;

namespace SEFA.DFM.Common;

public class PrinterHelper
{
    public static string ReplaceKeywords(Dictionary<string, object> parameters, string commandText)
		{
			Regex rx = new Regex("<([^><]+?)>", RegexOptions.IgnoreCase | RegexOptions.Compiled);
			MatchCollection matches = rx.Matches(commandText);
			foreach (object match in matches)
			{
				string[] field = match.ToString().Replace(">", "").Replace("<", "").Split(new char[]
				{
					','
				});
				if (parameters.ContainsKey(field[0]))
				{
					object fieldValue;
					parameters.TryGetValue(field[0], out fieldValue);
					string fieldValueString = (fieldValue == null) ? "" : (fieldValue.ToString() ?? "");
					if (field.Length > 1)
					{
						DateTime fieldDate;
						if (DateTime.TryParse(fieldValueString, out fieldDate))
						{
							try
							{
								string formattedDate = fieldDate.ToString(field[1]);
								if (field.Length > 2 && field[2].ToUpper() == "UPPER")
								{
									formattedDate = formattedDate.ToUpper();
								}
								else if (field.Length > 2 && field[2].ToUpper() == "LOWER")
								{
									formattedDate = formattedDate.ToLower();
								}
								commandText = commandText.Replace(match.ToString(), formattedDate);
								continue;
							}
							catch
							{
								throw new ConfigurationException(string.Format("Invalid formatting string. Cannot perform DateTime formatting on the following field: '{0}' using string '{1}'", field[0], field[1]));
							}
						}
						int charStart;
						if (int.TryParse(field[1], out charStart))
						{
							string fieldSubstring = fieldValueString;
							if (field.Length == 3)
							{
								int charLength;
								if (int.TryParse(field[2], out charLength))
								{
									if (charStart + charLength > fieldValueString.Length)
									{
										throw new ConfigurationException(string.Format("Invalid formatting as string '{0}' has {1} characters, and can't take {2} characters starting from position {3}.", new object[]
										{
											field[0],
											fieldValueString.Length.ToString(),
											charLength.ToString(),
											charStart.ToString()
										}));
									}
									fieldSubstring = fieldValueString.Substring(charStart, charLength);
								}
							}
							else
							{
								if (charStart >= fieldValueString.Length)
								{
									throw new ConfigurationException(string.Format("Invalid formatting as string '{0}' has {1} characters, and can't start from position {2}.", field[0], fieldValueString.Length.ToString(), charStart.ToString()));
								}
								fieldSubstring = fieldValueString.Substring(charStart);
							}
							commandText = commandText.Replace(match.ToString(), fieldSubstring);
						}
						else
						{
							commandText = commandText.Replace(match.ToString(), fieldValueString);
						}
					}
					else
					{
						commandText = commandText.Replace(match.ToString(), fieldValueString);
					}
				}
			}
			return commandText;
		}
    
    public static string GetPropertyValue(Dictionary<string, string> printProperties, string id, string defaultValue = null)
    {
	    string value = (from x in printProperties
		    where x.Key == id
		    select x.Value).FirstOrDefault<string>();
	    if (value != null)
	    {
		    return value;
	    }
	    return defaultValue;
    }
}