<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Reference Include="SEFA.Base.Common">
      <HintPath>..\..\common\SEFA.Base.Common.dll</HintPath>
    </Reference>
    <Reference Include="SEFA.Base.ESB">
      <HintPath>..\..\common\SEFA.Base.ESB.dll</HintPath>
    </Reference>
    <Reference Include="SEFA.Base.Model">
      <HintPath>..\..\common\SEFA.Base.Model.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.2" />
    <PackageReference Include="System.DirectoryServices" Version="6.0.0" />
  </ItemGroup>

</Project>
