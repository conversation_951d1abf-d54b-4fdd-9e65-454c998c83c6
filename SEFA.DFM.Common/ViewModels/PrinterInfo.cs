namespace SEFA.DFM.Common.ViewModels;

public class PrinterInfo
{
    public PrinterInfo()
    {
        Status = "OK";
        IsSuccess = true;
    }

    public bool IsSuccess { get; set; }

    public string LotId { get; set; }

    public string SublotId { get; set; }

    public string Data { get; set; }

    public string PrinterId { get; set; }

    public string TemplateId { get; set; }

    public string SourceEquipmentId { get; set; }

    public string BatchPalletId { get; set; }

    public string LogType { get; set; }

    public string ProductionOrderId { get; set; }

    public int Board { get; set; }

    public string Status { get; set; }

    public string Message { get; set; } 

    public void AddException(System.Exception ex)
    {
        Status = "ERROR";
        Message = ex.Message;
        IsSuccess = false;
    }
}