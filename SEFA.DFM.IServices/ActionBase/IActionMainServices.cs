using Microsoft.AspNetCore.Http;
using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using SEFA.DFM.Model;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SEFA.DFM.IServices
{
    /// <summary>
    /// IActionMainServices
    /// </summary>	
    public interface IActionMainServices :IBaseServices<ActionEntity>
    {
		public Task<MessageModel<ActionResponse>> Process(ActionRequest request, HttpContext httpContext);
    }
}