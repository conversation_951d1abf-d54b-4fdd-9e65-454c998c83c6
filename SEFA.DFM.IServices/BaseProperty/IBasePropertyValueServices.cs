using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;

namespace SEFA.DFM.IServices
{	
	/// <summary>
	/// IBasePropertyValueServices
	/// </summary>	
    public interface IBasePropertyValueServices :IBaseServices<BasePropertyValueEntity>
	{

        Task<List<BasePropertyValueEntity>> GetList(BasePropertyValueRequestModel reqModel);

        Task<MessageModel<string>> SaveMaterialGroupPropertyList (SaveMaterialGroupPropertyValueModel model);


        Task<List<MaterialGroupPropertyValueModel>> GetMaterialGroupPropertyList (MaterialGroupRequestModel reqModel);
    }
}