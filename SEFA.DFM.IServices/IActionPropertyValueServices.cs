using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SEFA.DFM.IServices
{	
	/// <summary>
	/// IActionPropertyValueServices
	/// </summary>	
    public interface IActionPropertyValueServices :IBaseServices<ActionPropertyValueEntity>
	{
        Task<List<EquipmentActionPropertyModel>> GetEquipmentActionProperty (EquipmentActionPropertyModel model);
        Task<MessageModel<string>> SaveEquipmentActionProperty (List<EquipmentActionPropertyModel> actionList);
    }
}