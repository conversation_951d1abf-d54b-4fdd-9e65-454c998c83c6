using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;

namespace SEFA.DFM.IServices
{	
	/// <summary>
	/// IBaseUniqueNumberServices
	/// </summary>	
    public interface IBaseUniqueNumberServices :IBaseServices<BaseUniqueNumberEntity>
	{
		Task<PageModel<BaseUniqueNumberEntity>> GetPageList(BaseUniqueNumberRequestModel reqModel);

        Task<List<BaseUniqueNumberEntity>> GetList(BaseUniqueNumberRequestModel reqModel);

		Task<bool> SaveForm(BaseUniqueNumberEntity entity);
    }
}