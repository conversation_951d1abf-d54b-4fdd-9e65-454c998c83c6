using SEFA.Base.IServices.BASE;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SEFA.DFM.IServices
{	
	/// <summary>
	/// ICalendarServices
	/// </summary>	
    public interface ICalendarServices :IBaseServices<CalendarEntity>
	{
		Task<bool> isExist(CalendarEntity request, bool mod);
		List<ShiftView> QueryList(string OrderDate);
		List<ShiftView> QueryListByModelid(string modelid, string finishdate);

		List<ShiftView> QueryListByModelids(string[] modelids, string OrderDate);
		List<ShiftView> QueryListSomeData(string OrderDate, List<OrderRequireViewM> order);

        List<ShiftView> QueryByWorkDay(DateTime workDay);

    }
}