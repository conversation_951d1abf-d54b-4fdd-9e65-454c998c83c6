using SEFA.Base.IServices.BASE;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SEFA.DFM.IServices
{	
	/// <summary>
	/// ICimlevelEquipmentServices
	/// </summary>	
    public interface ICimlevelEquipmentServices :IBaseServices<CimlevelEquipmentEntity>
	{

		Task<List<TreeModel>> GetCimEquipmentTree(string cimid);
	}


}