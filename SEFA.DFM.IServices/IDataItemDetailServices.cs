using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace SEFA.DFM.IServices
{
	/// <summary>
	/// IDataItemDetailServices 
	/// </summary>	
	public interface IDataItemDetailServices : IBaseServices<DataItemDetailEntity>
	{
        Task<List<DataItemDetailEntity>> GetBy(string itemCode, string lang);

		Task<PageModel<DataItemDetailEntity>> QueryPageList(Expression<Func<DataItemDetailEntity, DataItemEntity, bool>> whereExpression, int page, int intPageSize);
		Task<bool> isExistName(DataItemDetailEntity request, bool ismod);
		Task<bool> isExistValue(DataItemDetailEntity request, bool ismod);
		//Task<List<DataItemDetailModel>> GetPageListBy(string itemCode, string lang, string key);
	}
}