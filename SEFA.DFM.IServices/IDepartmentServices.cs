using SEFA.Base.IServices.BASE;
using SEFA.DFM.Model.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SEFA.DFM.IServices
{
    /// <summary>
    /// IDepartmentServices
    /// </summary>	
    public interface IDepartmentServices : IBaseServices<DepartmentEntity>
    {
        Task<bool> isExist(DepartmentEntity request, bool mod);
        Task<List<DepartmentEntity>> GetCompanyIdByDepId(string depid);
    }
}