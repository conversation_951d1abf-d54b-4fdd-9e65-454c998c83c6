using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;

namespace SEFA.DFM.IServices
{	
	/// <summary>
	/// IEnergyMappingServices
	/// </summary>	
    public interface IEnergyMappingServices :IBaseServices<EnergyMappingEntity>
	{
		Task<PageModel<EnergyMappingEntity>> GetPageList(EnergyTagModel reqModel);

        Task<List<EnergyMappingDataModel>> GetList (EnergyTagModel reqModel);

		Task<bool> SaveForm(EnergyMappingEntity entity);

		Task<List<EnergyMappingDataModel>> GetEquipmentTagValue (List<EnergyTagModel> reqlist);
    }
}