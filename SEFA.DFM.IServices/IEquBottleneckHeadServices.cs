using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using System.Threading.Tasks;

namespace SEFA.DFM.IServices
{	
	/// <summary>
	/// IEquBottleneckHeadServices
	/// </summary>	
    public interface IEquBottleneckHeadServices :IBaseServices<EquBottleneckHeadEntity>
	{
		Task<MessageModel<string>> SaveEquipmentBottleneck (EquBottleneckModel equBottleneckModel);

        Task<MessageModel<string>> DeleteEquipmentBottleneck (EquBottleneckModel equBottleneckModel);

    }
}