using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;

namespace SEFA.DFM.IServices
{	
	/// <summary>
	/// IEquipMaterialCompatServices
	/// </summary>	
    public interface IEquipMaterialCompatServices :IBaseServices<EquipMaterialCompatEntity>
	{
		Task<PageModel<EquipMaterialCompatEntity>> GetPageList(EquipMaterialCompatRequestModel reqModel);

        Task<List<EquipMaterialCompatEntity>> GetList(EquipMaterialCompatRequestModel reqModel);

		Task<bool> SaveForm(EquipMaterialCompatEntity entity);
    }
}