using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;

namespace SEFA.DFM.IServices
{	
	/// <summary>
	/// IEquipmentGroupEquipServices
	/// </summary>	
    public interface IEquipmentGroupEquipServices :IBaseServices<EquipmentGroupEquipEntity>
	{
		Task<PageModel<EquipmentGroupEquModel>> GetPageList(EquipmentGroupEquipRequestModel reqModel);

        Task<List<EquipmentGroupEquipEntity>> GetList(EquipmentGroupEquipRequestModel reqModel);

		Task<bool> SaveForm(EquipmentGroupEquipEntity entity);
    }
}