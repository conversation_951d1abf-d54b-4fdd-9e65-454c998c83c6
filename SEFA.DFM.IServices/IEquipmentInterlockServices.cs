using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SEFA.DFM.IServices
{	
	/// <summary>
	/// IEquipmnetInterlockServices
	/// </summary>	
    public interface IEquipmentInterlockServices :IBaseServices<EquipmentInterlockEntity>
	{
        Task<List<EquipmentInterLockModel>> GetEquipmentInterLock (string EquipmentActionId, string ActionCode = "");
        Task<MessageModel<string>> SaveEquipmentInterLock (List<EquipmentInterLockModel> interlocklist);
        Task<MessageModel<string>> SaveEquipmentInterLock (EquipmentInterLockModel interlockEntity);
    }
}