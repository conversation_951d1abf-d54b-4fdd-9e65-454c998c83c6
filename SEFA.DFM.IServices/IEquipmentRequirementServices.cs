using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;

namespace SEFA.DFM.IServices
{	
	/// <summary>
	/// IEquipmentRequirementServices
	/// </summary>	
    public interface IEquipmentRequirementServices :IBaseServices<EquipmentRequirementEntity>
	{
		Task<PageModel<EquipmentRequirementEntity>> GetPageList(EquipmentRequirementRequestModel reqModel);

        Task<List<EquipmentRequirementEntity>> GetList(EquipmentRequirementRequestModel reqModel);

		Task<bool> SaveForm(EquipmentRequirementEntity entity);
    }
}