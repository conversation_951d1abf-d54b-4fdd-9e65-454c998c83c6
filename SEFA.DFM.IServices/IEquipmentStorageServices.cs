using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;

namespace SEFA.DFM.IServices
{	
	/// <summary>
	/// IEquipmentStorageServices
	/// </summary>	
    public interface IEquipmentStorageServices :IBaseServices<EquipmentStorageEntity>
	{
		Task<PageModel<EquipmentStorageEntity>> GetPageList(EquipmentStorageRequestModel reqModel);

        Task<List<EquipmentStorageEntity>> GetList(EquipmentStorageRequestModel reqModel);
        Task<EquipmentStorageRequestModel> GetEquipmentStorage (EquipmentStorageRequestModel reqModel);
        

        Task<bool> SaveForm(EquipmentStorageEntity entity);
    }
}