using System.Collections.Generic;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;

namespace SEFA.DFM.IServices
{	
	/// <summary>
	/// IViewServices
	/// </summary>	
    public interface IEquipmentViewServices :IBaseServices<EquipmentViewEntity>
	{

		public Task<List<EquipmentViewEntity>> GetList(EquipmentViewRequest equipmentViewRequest);

		public Task<PageModel<EquipmentViewEntity>> GetPageList(EquipmentViewRequest equipmentViewRequest);

	}
}