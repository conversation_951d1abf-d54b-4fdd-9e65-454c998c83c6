using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SEFA.DFM.IServices
{	
	/// <summary>
	/// IFuntionPropertyValueServices
	/// </summary>	
    public interface IFunctionPropertyValueServices :IBaseServices<FunctionPropertyValueEntity>
	{
		Task<List<EquipmentFunctionPropertyModel>> GetEquipmentActiveFunctionProperty (EquipmentFunctionModel model);

        Task<List<EquFunctionPropertyModel>> GetEquActiveFunctionPropertyValueList (EquipmentFunctionModel model);
        Task<MessageModel<string>> SaveEquActiveFunctionPropertyValueList (List<EquipmentFunctionPropertyModel> functionProrertyList);
    }
}