using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using SEFA.Base.Common.Common;

namespace SEFA.DFM.IServices
{	
	/// <summary>
	/// IHrUserServices
	/// </summary>	
    public interface IHrUserServices :IBaseServices<HrUserEntity>
	{
		Task<PageModel<HrUserEntity>> GetPageList(HrUserRequestModel reqModel);

        Task<List<HrUserEntity>> GetList(HrUserRequestModel reqModel);

		Task<bool> SaveForm(HrUserEntity entity);

        Task<ResultBool> ImportDataFromHr(HrUserRequestModel reqModel);
    }
}