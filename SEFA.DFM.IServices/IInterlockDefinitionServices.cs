using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;

namespace SEFA.DFM.IServices
{	
	/// <summary>
	/// IInterlockDefinitionServices
	/// </summary>	
    public interface IInterlockDefinitionServices :IBaseServices<InterlockDefinitionEntity>
	{
		Task<PageModel<InterlockDefinitionEntity>> GetPageList(InterlockDefinitionRequestModel reqModel);

        Task<List<InterlockDefinitionEntity>> GetList(InterlockDefinitionRequestModel reqModel);

		Task<bool> SaveForm(InterlockDefinitionEntity entity);
    }
}