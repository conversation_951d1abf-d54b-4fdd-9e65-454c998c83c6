using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;

namespace SEFA.DFM.IServices
{	
	/// <summary>
	/// IInterlockLogServices
	/// </summary>	
    public interface IInterlockLogServices :IBaseServices<InterlockLogEntity>
	{
		Task<PageModel<InterlockLogEntity>> GetPageList(InterlockLogRequestModel reqModel);

        Task<List<InterlockLogEntity>> GetList(InterlockLogRequestModel reqModel);

		Task<bool> SaveForm(InterlockLogEntity entity);
    }
}