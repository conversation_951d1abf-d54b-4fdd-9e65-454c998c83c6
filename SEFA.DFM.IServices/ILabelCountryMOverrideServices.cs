using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;

namespace SEFA.DFM.IServices
{	
	/// <summary>
	/// ILabelCountryMOverrideServices
	/// </summary>	
    public interface ILabelCountryMOverrideServices :IBaseServices<LabelCountryMOverrideEntity>
	{
		Task<PageModel<LabelCountryMOverrideRequestModel>> GetPageList(LabelCountryMOverrideRequestModel reqModel);

        Task<List<LabelCountryMOverrideRequestModel>> GetList(LabelCountryMOverrideRequestModel reqModel);

    }
}