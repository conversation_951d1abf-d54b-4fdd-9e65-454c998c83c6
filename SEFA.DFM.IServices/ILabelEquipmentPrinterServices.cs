using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;

namespace SEFA.DFM.IServices
{	
	/// <summary>
	/// ILabelEquipmentPrinterServices
	/// </summary>	
    public interface ILabelEquipmentPrinterServices :IBaseServices<LabelEquipmentPrinterEntity>
	{
		Task<PageModel<LabelEquipmentPrinterRequestModel>> GetPageList(LabelEquipmentPrinterRequestModel reqModel);

        Task<List<LabelEquipmentPrinterRequestModel>> GetList(LabelEquipmentPrinterRequestModel reqModel);

    }
}