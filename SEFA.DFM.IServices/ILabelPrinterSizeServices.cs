using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;

namespace SEFA.DFM.IServices
{	
	/// <summary>
	/// ILabelPrinterSizeServices
	/// </summary>	
    public interface ILabelPrinterSizeServices :IBaseServices<LabelPrinterSizeEntity>
	{
		Task<PageModel<LabelPrinterSizeEntity>> GetPageList(LabelPrinterSizeRequestModel reqModel);

        Task<List<LabelPrinterSizeEntity>> GetList(LabelPrinterSizeRequestModel reqModel);

    }
}