using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;

namespace SEFA.DFM.IServices
{	
	/// <summary>
	/// ILabelTempleteServices
	/// </summary>	
    public interface ILabelTempleteServices :IBaseServices<LabelTempleteEntity>
	{
		Task<PageModel<LabelTempleteRequestModel>> GetPageList(LabelTempleteRequestModel reqModel);

        Task<List<LabelTempleteRequestModel>> GetList(LabelTempleteRequestModel reqModel);

    }
}