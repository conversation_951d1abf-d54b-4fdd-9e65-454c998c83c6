using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;

namespace SEFA.DFM.IServices
{	
	/// <summary>
	/// ILabelbbdFormatServices
	/// </summary>	
    public interface ILabelbbdFormatServices :IBaseServices<LabelbbdFormatEntity>
	{
		Task<PageModel<LabelbbdFormatEntity>> GetPageList(LabelbbdFormatRequestModel reqModel);

        Task<List<LabelbbdFormatEntity>> GetList(LabelbbdFormatRequestModel reqModel);

    }
}