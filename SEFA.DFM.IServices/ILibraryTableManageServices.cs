using SEFA.Base.IServices.BASE;
using SEFA.DFM.Model.Models;
using SqlSugar;
using System.Collections.Generic;

namespace SEFA.DFM.IServices
{
    /// <summary>
    /// ILibraryTableManageServices
    /// </summary>	
    public interface ILibraryTableManageServices : IBaseServices<LibraryTableManageEntity>
    {
        List<DbTableInfo> GetTable();
        List<DbColumnInfo> GetTableDetails(string tablename);
        List<object> GetData(string tablename, string colname);
    }
}