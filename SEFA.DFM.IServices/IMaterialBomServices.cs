using SEFA.Base.IServices.BASE;
using SEFA.DFM.Model.Models;
using System.Threading.Tasks;

namespace SEFA.DFM.IServices
{
    /// <summary>
    /// IMaterialBomServices
    /// </summary>	
    public interface IMaterialBomServices : IBaseServices<MaterialBomEntity>
    {
        Task<bool> isExist(MaterialBomEntity request, bool ismod);
        Task<int> UpdateStuts(MaterialBomEntity request);
    }
}