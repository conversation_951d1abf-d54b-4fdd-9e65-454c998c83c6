using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;

namespace SEFA.DFM.IServices
{	
	/// <summary>
	/// IMaterialProcessDataServices
	/// </summary>	
    public interface IMaterialProcessDataServices :IBaseServices<MaterialProcessDataEntity>
	{
		Task<PageModel<MaterialProcessDataEntity>> GetPageList(MaterialProcessDataRequestModel reqModel);

        Task<List<MaterialProcessDataEntity>> GetList(MaterialProcessDataRequestModel reqModel);

		Task<bool> SaveForm(MaterialProcessDataEntity entity);
    }
}