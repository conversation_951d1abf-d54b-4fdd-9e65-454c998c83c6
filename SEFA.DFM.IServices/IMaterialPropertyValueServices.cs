using Microsoft.AspNetCore.Mvc;
using SEFA.Base.Common.Common;
using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SEFA.DFM.IServices
{	
	/// <summary>
	/// IMaterialPropertyValueServices
	/// </summary>	
    public interface IMaterialPropertyValueServices :IBaseServices<MaterialPropertyValueEntity>
	{
		Task<List<MaterialPropertyModel>> GetMaterialPropertyList (MaterialPropertyRequestModel request);

        Task<PageModel<MaterialPropertyModel>> GetMaterialPropertyPageList (MaterialPropertyRequestModel request);
		Task<ResultString> ImportData ([FromForm] FileImportDto input);
        Task<ResultString> ImportPlanData ([FromForm] FileImportDto input);
		Task<PageModel<MaterialPlanPropertyExcelDto>> GetMaterialPlanPropertyPageList (MaterialPropertyRequestModel request);
    }
}