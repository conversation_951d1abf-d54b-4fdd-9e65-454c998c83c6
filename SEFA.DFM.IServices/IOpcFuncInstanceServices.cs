using Microsoft.AspNetCore.Mvc;
using SEFA.Base.IServices.BASE;
using SEFA.DFM.Model.Models;
using System.Threading.Tasks;

namespace SEFA.DFM.IServices
{	
	/// <summary>
	/// IOpcFuncInstanceServices
	/// </summary>	
    public interface IOpcFuncInstanceServices :IBaseServices<OpcFuncInstanceEntity>
	{
		Task<int> AddInstance(OpcFuncInstanceEntity entity);

		Task<bool> DeleteInstance([FromBody] string[] ids);
    }
}