using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;

namespace SEFA.DFM.IServices
{	
	/// <summary>
	/// IPhaseSalescontainerServices
	/// </summary>	
    public interface IPhaseSalescontainerServices :IBaseServices<PhaseSalescontainerEntity>
	{
		Task<PageModel<PhaseSalescontainerEntity>> GetPageList(PhaseSalescontainerRequestModel reqModel);

        Task<List<PhaseSalescontainerEntity>> GetList(PhaseSalescontainerRequestModel reqModel);

		Task<bool> SaveForm(PhaseSalescontainerEntity entity);
    }
}