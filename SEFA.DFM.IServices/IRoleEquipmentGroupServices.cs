using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;

namespace SEFA.DFM.IServices
{	
	/// <summary>
	/// IRoleEquipmentGroupServices
	/// </summary>	
    public interface IRoleEquipmentGroupServices :IBaseServices<RoleEquipmentGroupEntity>
	{
		Task<PageModel<RoleEquipmentGroupEntity>> GetPageList(RoleEquipmentGroupRequestModel reqModel);

        Task<List<RoleEquipmentGroupModel>> GetList(RoleEquipmentGroupRequestModel reqModel);

		Task<MessageModel<string>> SaveForm (List<RoleEquipmentGroupEntity> datalist);

		Task<List<EquipmentGroupEntity>> GetUserEquipmetGroupList (string userName);
    }
}