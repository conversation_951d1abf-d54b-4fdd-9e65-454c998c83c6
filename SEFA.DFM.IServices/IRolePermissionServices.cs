using SEFA.Base.IServices.BASE;
using SEFA.DFM.Model.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SEFA.DFM.IServices
{	
	/// <summary>
	/// IRolePermissionServices
	/// </summary>	
    public interface IRolePermissionServices :IBaseServices<RolePermissionEntity>
	{
		Task<List<MenuEntity>> GetPermissionListByRoleId(UserRoleEntity userRole);
		List<MenuEntity> GetPermissionListByRoleIdNew(UserRoleEntity userRole);
        Task<List<RolePermissionEntity>> GetUserRoleByRoleId(string roleid);
	}
}