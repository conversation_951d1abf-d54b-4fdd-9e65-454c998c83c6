using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;

namespace SEFA.DFM.IServices
{	
	/// <summary>
	/// ISopDirServices
	/// </summary>	
    public interface ISopDirServices : IBaseServices<SopDirEntity>
	{
		Task<PageModel<SopDirEntity>> GetPageList(SopDirRequestModel reqModel);

        Task<List<SopDirEntity>> GetList(SopDirRequestModel reqModel);

		Task<bool> SaveForm(SopDirEntity entity);
    
        Task<List<TreeModelEx<SopDirEntity>>> GetTreeList(string parentId = "");

        Task<List<SopDirDto>> GetAllDirs();
    }
}