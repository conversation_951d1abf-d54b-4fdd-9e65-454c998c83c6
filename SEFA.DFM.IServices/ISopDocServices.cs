using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;

namespace SEFA.DFM.IServices
{	
	/// <summary>
	/// ISopDocServices
	/// </summary>	
    public interface ISopDocServices :IBaseServices<SopDocEntity>
	{
		Task<PageModel<SopDocEntity>> GetPageList(SopDocRequestModel reqModel);

        Task<List<SopDocEntity>> GetList(SopDocRequestModel reqModel);

		Task<bool> SaveForm(SopDocEntity entity);

        Task<bool> BatchAddAsync(List<SopDocEntity> docs);

        /// <summary>
        /// 根据文档编码获取最新版本号
        /// </summary>
        /// <param name="docCode">文档编码</param>
        /// <returns>最新版本号</returns>
        Task<string> GetLatestVersionByCode(string docCode);
    }
}