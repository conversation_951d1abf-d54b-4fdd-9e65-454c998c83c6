using SEFA.Base.IServices.BASE;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SEFA.DFM.IServices
{
    /// <summary>
    /// IStaffServices
    /// </summary>	
    public interface IStaffServices : IBaseServices<StaffEntity>
    {
        /// <summary>
        /// 获取组织结构 数据
        /// </summary>
        /// <returns></returns>
        Task<List<DepartmentDto>> GetDepartments();

        /// <summary>
        /// 员工 编号 是否存在
        /// </summary>
        /// <param name="Code"></param>
        /// <returns></returns>
        public Task<bool> IsExistCode(StaffEntity entity);

        public Task<List<StaffDto>> GetList(StaffRequestModel reqModel);
    }
}