using SEFA.Base.IServices.BASE;
using SEFA.DFM.Model.Models;
using System.Threading.Tasks;

namespace SEFA.DFM.IServices
{	
	/// <summary>
	/// IUnitmanageServices
	/// </summary>	
    public interface IUnitmanageServices :IBaseServices<UnitmanageEntity>
	{

		/// <summary>
		/// 判断编码是否重复
		/// </summary>
		/// <param name="request"></param>
		/// <returns></returns>
		public Task<bool> isExist(UnitmanageEntity request);
	}
}