using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SEFA.DFM.IServices
{
    /// <summary>
    /// IUserinfoServices
    /// </summary>	
    public interface IUserinfoServices : IBaseServices<UserinfoEntity>
    {
        Task<List<UserInfoViewModel>> GetUserinfos(UserinfoEntity request);
        
        Task<List<UserInfoViewModel>> GetFilteredUsersList(UserinfoEntity request);

        Task<List<UserinfoEntity>> GetUserList(UserinfoEntity request);

        Task<List<UserinfoEntity>> GetDepartUserList();
        Task<PageModel<UserInfoViewModel>> GetUserinfosPageList(UserinfoEntity request, int pageIndex, int pageSize);

        Task<List<UserinfoEntity>> GetUserListByRoleId(UserRoleEntity userRole);

        Task<int> UpdatePassWord(UserInfoViewModel request);
        Task<int> UpdateData(UserinfoEntity request);

        Task<bool> isExist(UserinfoEntity request);

        Task<MessageModel2<string>> ImportBatchUser(List<UserInfoImportModel> userInfoImportModel);

        Task<MessageModel2<string>> ImportUser(UserInfoImportModel userInfoImportModel);
        
        /// <summary>
        /// 设置用户班组信息批量
        /// </summary>
        /// <param name="userTeamViews"></param>
        /// <returns></returns>
        Task<MessageModel<string>> SetTeamInfo(List<UserTeamView> userTeamViews);

        Task<UserinfoEntity> GetUserByUserNo (string UserNo);
    }
}