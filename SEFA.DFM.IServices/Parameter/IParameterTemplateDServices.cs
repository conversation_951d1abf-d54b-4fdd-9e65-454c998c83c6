using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;

namespace SEFA.DFM.IServices
{	
	/// <summary>
	/// IParameterTemplateDServices
	/// </summary>	
    public interface IParameterTemplateDServices :IBaseServices<ParameterTemplateDEntity>
	{
		Task<PageModel<ParameterTemplateDEntity>> GetPageList(ParameterTemplateDRequestModel reqModel);

        Task<List<ParameterTemplateDEntity>> GetList(ParameterTemplateDRequestModel reqModel);

		Task<bool> SaveForm(ParameterTemplateDEntity entity);
    }
}