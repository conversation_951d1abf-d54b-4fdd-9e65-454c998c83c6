using Microsoft.AspNetCore.Mvc;
using SEFA.Base.Common.Common;
using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SEFA.DFM.IServices
{	
	/// <summary>
	/// IMaterialGroupMappingServices
	/// </summary>	
    public interface IMaterialGroupMappingServices :IBaseServices<MaterialGroupMappingEntity>
	{
		Task<PageModel<MaterialModel>> GetPageList (MaterialGroupRequestModel reqModel);
		Task<List<MaterialModel>> GetList (MaterialGroupRequestModel reqModel);
		Task<ResultString> ImportData ([FromForm] FileImportDto input);
    }
}