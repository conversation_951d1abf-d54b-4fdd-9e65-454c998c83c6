using SEFA.Base.IServices.BASE;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SEFA.DFM.IServices
{	
	/// <summary>
	/// IParameterGroupEquipmentServices
	/// </summary>	
    public interface IParameterGroupEquipmentServices :IBaseServices<ParameterGroupEquipmentEntity>
	{
		 Task<List<ParameterGroupEquipmentModel>> GetGroupEquipmentList (string ParameterGroupId);
    }
}