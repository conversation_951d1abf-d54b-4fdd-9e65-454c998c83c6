using Microsoft.AspNetCore.Mvc;
using SEFA.Base.Common.Common;
using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;

namespace SEFA.DFM.IServices
{	
	/// <summary>
	/// IRecipeServices
	/// </summary>	
    public interface IRecipeCommonServices
    {
       
        Task<List<TreeModelEx<RecipeModel>>> GetRecipeTreeList (string selectSectionVersionId, string selectContextVersionId, string searchKey = "");
        Task<List<RecipeParameterModel>> GetRecipeParameterConfigList (string recipeSectionVersionId, string recipeContextVersionId, string parameterGroupId);
        Task<List<RecipeParameterModel>> GetRecipeParameterDefinitionList (string recipeSectionVersionId, string parameterGroupId);

        Task<List<RecipeParameterModel>> GetPoRecipeParameterList (PoRecipeModel poRecipeModel,string type = null);

        
        Task<List<RecipeParameterGroupModel>> GetParameterGroupList (string recipeSectionVersionID);
        Task<List<TreeModelEx<RecipeLevelModel>>> GetRecipeInfoList (string recipeId);



        Task<MessageModel<string>> DeleteRecipe (RecipeCommonModel recipeCommonModel);
        Task<MessageModel<string>> NewRecipeSection (RecipeCommonModel recipeCommonModel);
        Task<MessageModel<string>> DeleteRecipeSection (string RecipeSectionID);
        Task<MessageModel<string>> EditRecipeSection (RecipeCommonModel recipeCommonModel);

        Task<MessageModel<string>> RecipeSectionCheckOut (RecipeCommonModel recipeCommonModel);
        Task<MessageModel<string>> RecipeContextCheckOut (RecipeMakeContextModel recipeMakeContextModel);
        Task<MessageModel<string>> RecipeSectionCheckIn (RecipeCommonModel recipeCommonModel);
        Task<MessageModel<string>> RecipeContextCheckIn (RecipeCommonModel recipeCommonModel);
        Task<MessageModel<string>> RecipeSectionApprove (RecipeCommonModel recipeCommonModel);
        Task<MessageModel<string>> RecipeContextApprove (RecipeCommonModel recipeCommonModel);

        Task<List<RecipeHisModel>> GetRecipeSectionHisList (string RecipeSectionId);
        Task<List<RecipeHisModel>> GetRecipeContextHisList (string RecipeContextId);

        Task<MessageModel<string>> BindPoRecipe (PoRecipeModel poRecipeModel);

        Task<DataTable> GetRecipeTableData (RecipeCommonModel recipeCommonModel);
        Task<PoRecipeDataModel> GetPoRecipeData (PoRecipeModel poRecipeModel);

        Task<PoRecipeDataModel> GetPoRecipeDataNew (PoRecipeModel poRecipeModel);

        Task<MessageModel<List<RecipeParameterModel>>> GetEquipmentLogSheetList (string EquipmentId);

        Task<MessageModel<string>> BindProcessMappingData (ProcessDataMappingModel request);
        Task<MessageModel<string>> BindProcessMappingTextData (ProcessDataMappingModel request);

        Task<ResultString> ImportData ([FromForm] FileImportDto input);

        Task<MessageModel<string>> BlParameterConfig (string recipeName);

    }
}