using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SEFA.DFM.IServices
{
	/// <summary>
	/// IWoServices
	/// </summary>	
	public interface IWoServices : IBaseServices<WoEntity>
	{
	}
}