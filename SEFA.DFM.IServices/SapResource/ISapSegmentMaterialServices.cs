using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;

namespace SEFA.DFM.IServices
{	
	/// <summary>
	/// ISapSegmentMaterialServices
	/// </summary>	
    public interface ISapSegmentMaterialServices :IBaseServices<SapSegmentMaterialEntity>
	{
        Task<PageModel<SapSegmentMaterialBomModel>> GetPageList (SapSegmentMaterialRequestModel model);

        Task<List<SapSegmentMaterialModel>> GetList (SapSegmentMaterialRequestModel model);

		Task<bool> SaveForm(SapSegmentMaterialEntity entity,string userId);

		Task<MessageModel<string>> Delete (string Id);

		Task<List<MaterialVersionModel>> GetSelectMaterialVersionList (string materialVersionId);

		Task<MessageModel<string>> CopeData (SapSegmentMaterialEntity entity, string userId);
    }
}