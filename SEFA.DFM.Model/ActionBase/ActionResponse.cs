using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SEFA.DFM.Model
{
    /// <summary>
    /// 返回对象
    /// </summary>
    public class ActionResponse
    {
        public Dictionary<string, object> Data { get; set; } = new();

        public bool Result = true;

        public string ErrorCode = string.Empty;

        public string Message = string.Empty;

        public void AddError(string _message,string _errorCode = "") {
            this.Result = false;
            this.Message = _message;
            this.ErrorCode = _errorCode;
        }

        public ActionResponse() { }
    }
}
