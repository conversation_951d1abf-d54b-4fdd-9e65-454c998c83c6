using AutoMapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using SEFA.DFM.Model.Models.DATAWAREHOUSE;
using SEFA.DFM.Model.Models.Models.ViewModel;
using SEFA.Base.Model;

namespace SEFA.DFM.Model.AutoMapperProfile
{
    public class DFMProfile : Profile
    {
        public DFMProfile()
        {
            CreateMap<MaterialEntity, TreeModel>()
                .ForMember(a => a.id, b => b.MapFrom(c => c.ID))
                .ForMember(a => a.name, b => b.MapFrom(c => c.Code + (c.Type == "S" ? "(系列)" : "(机种)")))
                .ForMember(a => a.value, b => b.MapFrom(c => c.ID)).ReverseMap();

            CreateMap<EquipmentEntity, EquipmentView>(MemberList.None).ReverseMap();
            CreateMap<MaterialEntity, MaterialDto>(MemberList.None).ReverseMap();
            CreateMap<UserinfoEntity, UserInfoViewModel>(MemberList.None).ReverseMap();
            CreateMap<FactorySiteEntity, FactorySiteTreeDto>(MemberList.None).ReverseMap();
            CreateMap<OrderRequireEntity, OrderRouting>()
                .ForMember(a => a.ParentProductionCode, b => b.MapFrom(c => c.MainMaterialCode))
                .ForMember(a => a.ProductionCode, b => b.MapFrom(c => c.MaterialCode))
                .ForMember(a => a.ProductionName, b => b.MapFrom(c => c.MaterialName))
                .ForMember(a => a.ExcellentRate, b => b.MapFrom(c => c.YieldRate))
                .ForMember(a => a.LineID, b => b.MapFrom(c => c.EquipmentId))
                .ForMember(a => a.LineName, b => b.MapFrom(c => c.EquipmentName))
                .ForMember(a => a.LineCode, b => b.MapFrom(c => c.EquipmentCode))
                .ForMember(a => a.ProductionLineCode, b => b.MapFrom(c => c.ProductionLineCode))
                .ForMember(a => a.RoutingShortName, b => b.MapFrom(c => c.RoutingType))
                .ForMember(a => a.ResId, b => b.MapFrom(c => c.ResId))
                .ForMember(a => a.RoutingId, b => b.MapFrom(c => c.RoutingId))
                .ForMember(a => a.RoutingCode, b => b.MapFrom(c => c.RoutingCode))
                .ForMember(a => a.Stage, b => b.MapFrom(c => c.ProduceType))
                .ForMember(a => a.RoutingName, b => b.MapFrom(c => c.RoutingId)).ReverseMap();

            CreateMap<FulllineGoalEntity, FulllineGoalView>(MemberList.None).ReverseMap();
            CreateMap<DatamodelEntity, DatamodelDto>(MemberList.None).ForMember(a => a.ID,
                b => b.MapFrom(c => String.IsNullOrEmpty(c.ID) ? Guid.NewGuid().ToString() : c.ID)).ReverseMap();
            CreateMap<DatamodelDto, DatamodelEntity>(MemberList.None).ForMember(a => a.ID,
                b => b.MapFrom(c => String.IsNullOrEmpty(c.ID) ? Guid.NewGuid().ToString() : c.ID)).ReverseMap();
            CreateMap<ProductlineouttagEntity, ProductlineouttagDto>(MemberList.None).ForMember(a => a.ID,
                b => b.MapFrom(c => String.IsNullOrEmpty(c.ID) ? Guid.NewGuid().ToString() : c.ID)).ReverseMap();
            CreateMap<ProductlineouttagDto, ProductlineouttagEntity>(MemberList.None).ForMember(a => a.ID,
                b => b.MapFrom(c => String.IsNullOrEmpty(c.ID) ? Guid.NewGuid().ToString() : c.ID)).ReverseMap();
            CreateMap<RelMaterialTagEntity, RelMaterialTagDto>(MemberList.None).ForMember(a => a.ID,
                b => b.MapFrom(c => String.IsNullOrEmpty(c.ID) ? Guid.NewGuid().ToString() : c.ID)).ReverseMap();
            CreateMap<RelMaterialTagDto, RelMaterialTagEntity>(MemberList.None).ForMember(a => a.ID,
                b => b.MapFrom(c => String.IsNullOrEmpty(c.ID) ? Guid.NewGuid().ToString() : c.ID)).ReverseMap();

            CreateMap<EquBottleneckHeadEntity, EquBottleneckModel>(MemberList.None).ReverseMap();

            CreateMap<OpcFunctionEntity, OpcFunctionsView>(MemberList.None).ReverseMap();
            CreateMap<OpcFuncPropEntity, OpcFunctionsPropInstanceView>(MemberList.None).ReverseMap();
            CreateMap<OpcFuncPropInstanceEntity, OpcFunctionsPropInstanceView>(MemberList.None).ReverseMap();

            CreateMap<EquBottleneckHeadEntity, EquBottleneckModel>(MemberList.None).ReverseMap();

            CreateMap<ParameterDefinitionEntity, RecipeParameterModel>(MemberList.None).ReverseMap();

            CreateMap<MaterialVersionEntity, MaterialVersionRequestModel>(MemberList.None).ReverseMap();

            CreateMap<ParameterLimitEntity, ParameterLimitRequestModel>(MemberList.None).ReverseMap();
            CreateMap<ParameterTemplateDEntity, ParameterLimitRequestModel>(MemberList.None).ReverseMap();

            CreateMap<PageModel<StaffEntity>, PageModel<StaffDto>>(MemberList.None).ReverseMap();
            CreateMap<StaffDutyLineExcelDto, StaffDutyLineEntity>(MemberList.None).ReverseMap();
            CreateMap<StaffDutyLineDto, StaffDutyLineEntity>(MemberList.None).ReverseMap();
            CreateMap<StaffEntity, AndonStaffDto>(MemberList.None).ReverseMap();
            CreateMap<StaffEntity, StaffDto>(MemberList.None).ReverseMap();
            CreateMap<EquipmentImportDto, EquipmentEntity>()
                .ForMember(a => a.EquipmentCode, b => b.MapFrom(c => c.deptcode))
                .ForMember(a => a.EquipmentName, b => b.MapFrom(c => c.deptname))
                .ReverseMap();
            CreateMap<StaffDto, StaffDutyLineEntity>()
                .ForMember(a => a.StaffName, b => b.MapFrom(c => c.Name))
                .ForMember(a => a.StaffCode, b => b.MapFrom(c => c.Code)).ReverseMap();
            CreateMap<EquipmentStorageEntity, EquipmentStorageRequestModel>(MemberList.None).ReverseMap();
            CreateMap<HrUserEntity, KayangAPIEmployeeRow>(MemberList.None).ReverseMap();

            CreateMap<EquipmentCapacityRequestModel, EquipmentCapacityExcelDto>(MemberList.None).ReverseMap();
            CreateMap<MaterialModel, MaterialGroupExcelDto>(MemberList.None).ReverseMap();

            CreateMap<MaterialPropertyModel, MaterialPropertyExcelDto>(MemberList.None).ReverseMap();
            CreateMap<EquipmentMaterialRequestModel, EquipmentMaterialExcelDto>(MemberList.None).ReverseMap();
            CreateMap<ProductBomExcelDto, SapSegmentMaterialStepModel>()
                .ForMember(a => a.SapSegmentName, b => b.MapFrom(c => c.SegmentName)).ReverseMap();
            CreateMap<SopDirEntity, SopDirDto>(MemberList.None).ReverseMap();
        }
    }
}