using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("DFM_M_ACTION")] 
    public class ActionEntity : EntityBase
    {
        public ActionEntity()
        {
        }
           /// <summary>
           /// Desc:分类类型
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="FUNCTION_ID")]
        public string FunctionId { get; set; }
           /// <summary>
           /// Desc:分类编码
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="ACTION_CODE")]
        public string ActionCode { get; set; }
           /// <summary>
           /// Desc:分类名称
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="ACTION_NAME")]
        public string ActionName { get; set; }
           /// <summary>
           /// Desc:是否删除（0未删除，1已删除）
           /// Default:0
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DELETED")]
        public int Deleted { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="PARENT_CLASS_ID")]
        public int ParentClassId { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="IDENTIFY")]
        public string Identify { get; set; }
    }
}