using SEFA.Base.Model.BASE;
using SqlSugar;
using System;

namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///审计日志
    ///</summary>

    [SugarTable("DFM_B_AUDIT_LOG")]
    public class AuditLogEntity : EntityBase
    {
        public AuditLogEntity()
        {
        }

        /// <summary>
        /// Desc:参数
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "PARAMETERS")]
        public string Parameters { get; set; }

        /// <summary>
        /// Desc:浏览器信息
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "BROWSER_INFO")]
        public string BrowserInfo { get; set; }

        /// <summary>
        /// Desc:客户端信息
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "CLIENT_NAME")]
        public string ClientName { get; set; }

        /// <summary>
        /// Desc:客户端IP地址
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "CLIENT_IP_ADDRESS")]
        public string ClientIpAddress { get; set; }

        /// <summary>
        /// Desc:执行时长
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "EXECUTION_DURATION")]
        public int? ExecutionDuration { get; set; }

        /// <summary>
        /// Desc:执行时间
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "EXECUTION_TIME")]
        public DateTime? ExecutionTime { get; set; }

        /// <summary>
        /// Desc:返回内容
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "RETURN_VALUE")]
        public string ReturnValue { get; set; }

        /// <summary>
        /// Desc:方法名
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "METHOD_NAME")]
        public string MethodName { get; set; }

        /// <summary>
        /// Desc:服务名
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SERVICE_NAME")]
        public string ServiceName { get; set; }

        /// <summary>
        /// Desc:调用者工号
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "USER_CODE")]
        public string UserCode { get; set; }

        /// <summary>
        /// Desc:调用者姓名
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "USER_NAME")]
        public string UserName { get; set; }

        /// <summary>
        /// Desc:方法用途
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "METHOD_REMARK")]
        public string MethodRemark { get; set; }
    }
}