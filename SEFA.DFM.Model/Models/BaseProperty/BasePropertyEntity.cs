using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("DFM_M_BASE_PROPERTY")] 
    public class BasePropertyEntity : EntityBase
    {
        public BasePropertyEntity()
        {
        }
           /// <summary>
           /// Desc:属性关联表ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="TABLE_ID")]
        public string TableId { get; set; }
           /// <summary>
           /// Desc:属性
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="CODE")]
        public string Code { get; set; }
           /// <summary>
           /// Desc:描述
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DESCRIPTION")]
        public string Description { get; set; }
           /// <summary>
           /// Desc:值类型
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="VALUE_TYPE")]
        public string ValueType { get; set; }
           /// <summary>
           /// Desc:默认值
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DEFAULT_VALUE")]
        public string DefaultValue { get; set; }
           /// <summary>
           /// Desc:所属模块
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="FEATURE")]
        public string Feature { get; set; }
           /// <summary>
           /// Desc:排序
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="SORT")]
        public string Sort { get; set; }

    }
}