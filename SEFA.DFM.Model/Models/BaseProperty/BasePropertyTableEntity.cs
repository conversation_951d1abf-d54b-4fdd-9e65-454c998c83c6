using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("DFM_M_BASE_PROPERTY_TABLE")] 
    public class BasePropertyTableEntity : EntityBase
    {
        public BasePropertyTableEntity()
        {
        }
           /// <summary>
           /// Desc:业务表
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="TABLE_NAME")]
        public string TableName { get; set; }
           /// <summary>
           /// Desc:描述
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DESCRIPTION")]
        public string Description { get; set; }
           /// <summary>
           /// Desc:业务类型
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="TYPE")]
        public string Type { get; set; }

    }
}