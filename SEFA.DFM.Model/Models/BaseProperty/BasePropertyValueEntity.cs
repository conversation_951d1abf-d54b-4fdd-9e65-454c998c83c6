using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("DFM_M_BASE_PROPERTY_VALUE")] 
    public class BasePropertyValueEntity : EntityBase
    {
        public BasePropertyValueEntity()
        {
        }
           /// <summary>
           /// Desc:属性ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="PROPERTY_ID")]
        public string PropertyId { get; set; }
           /// <summary>
           /// Desc:业务表关联行
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="TABLE_ROW_ID")]
        public string TableRowId { get; set; }
           /// <summary>
           /// Desc:实际值
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="VALUE")]
        public string Value { get; set; }

    }
}