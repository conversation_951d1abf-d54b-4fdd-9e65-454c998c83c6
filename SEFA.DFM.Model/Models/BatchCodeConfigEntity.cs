using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("DFM_M_BATCH_CODE_CONFIG")] 
    public class BatchCodeConfigEntity : EntityBase
    {
        public BatchCodeConfigEntity()
        {
        }
           /// <summary>
           /// Desc:设备
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="EQUIPMENT_ID")]
        public string EquipmentId { get; set; }
           /// <summary>
           /// Desc:批次生成模式
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="BATCH_MODE")]
        public string BatchMode { get; set; }
           /// <summary>
           /// Desc:批次应用模式
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="BATCH_LOCATION")]
        public string BatchLocation { get; set; }
           /// <summary>
           /// Desc:格式
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="FOMAT")]
        public string Fomat { get; set; }
    }
}