using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using System.Collections.Generic;

namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>

    [SugarTable("DFM_M_CALENDAR")]
    public class CalendarEntity : EntityBase
    {
        public CalendarEntity()
        {
        }
        /// <summary>
        /// Desc:公司ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "COMPANYID")]
        public string Companyid { get; set; }
        /// <summary>
        /// Desc:模型ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "MODELID")]
        public string MODELID { get; set; }
        /// <summary>
        /// Desc:班次ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SHIFTID")]
        public string Shiftid { get; set; }
        /// <summary>
        /// Desc:班次序号
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SHIFTSEQ")]
        public int? Shiftseq { get; set; }
        /// <summary>
        /// Desc:班组ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "TEAMID")]
        public string Teamid { get; set; }
        /// <summary>
        /// Desc:类型
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "TYPE")]
        public int? Type { get; set; }
        /// <summary>
        /// Desc:工作日
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "WORKDATE")]
        public DateTime Workdate { get; set; }
        /// <summary>
        /// Desc:开始时间
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "STARTTIME")]
        public DateTime? Starttime { get; set; }
        /// <summary>
        /// Desc:结束时间
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "ENDTIME")]
        public DateTime? Endtime { get; set; }
         /// <summary>
        /// Desc:休息开始时间
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "REST_STARTTIME")]
        public string? RestStarttime { get; set; }
        /// <summary>
        /// Desc:休息结束时间
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "REST_ENDTIME")]
        public string? RestEndtime { get; set; }
        
        /// <summary>
        /// Desc:加班时长
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "DELAYTIME")]
        public int? Delaytime { get; set; }
        /// <summary>
        /// Desc:描述
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "DESCRIPTION")]
        public string Description { get; set; }
        /// <summary>
        /// Desc:
        /// Default:0
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "DELETED")]
        public int Deleted { get; set; }
        

      



    }
}