using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using SqlSugar;

namespace SEFA.DFM.Model.Models
{
    public class CalendarTeamEntity 
    {

        public string ID { get; set; }
        /// <summary>
        /// 公司ID
        /// </summary>
        public string CompanyId { get; set; }
        /// <summary>
        /// 班组主键
        /// </summary>
        public string TeamId { get; set; }
        /// <summary>
        /// 公司名称
        /// </summary>
        public string CompanyName { get; set; }
        /// <summary>
        /// 名称
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// 简称
        /// </summary>
        public string ShortName { get; set; }
        /// <summary>
        /// 工作班组
        /// </summary>
        public int? IsWorkTeam { get; set; }
        /// <summary>
        /// 序号
        /// </summary>
        public int? Sequence { get; set; }
        /// <summary>
        /// 删除
        /// </summary>
        public int? DeleteMark { get; set; }
        /// <summary>
        /// 启用
        /// </summary>
        public int? EnabledMark { get; set; }
        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; }

    }
}
