using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("DFM_M_CATEGORY")] 
    public class CategoryEntity : EntityBase
    {
        public CategoryEntity()
        {
        }
           /// <summary>
           /// Desc:编号
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="CODE")]
        public string Code { get; set; }
           /// <summary>
           /// Desc:名称
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="NAME")]
        public string Name { get; set; }
           /// <summary>
           /// Desc:分类特征
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="IDENTITIES")]
        public string Identities { get; set; }
           /// <summary>
           /// Desc:描述
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DESCRIPTION")]
        public string Description { get; set; }
           /// <summary>
           /// Desc:启用标识
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="ENABLED")]
        public int Enabled { get; set; }
           /// <summary>
           /// Desc:删除标志
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DELETED")]
        public int Deleted { get; set; }

    }
}