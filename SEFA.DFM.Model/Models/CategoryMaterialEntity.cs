using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("DFM_M_CATEGORY_MATERIAL")] 
    public class CategoryMaterialEntity : EntityBase
    {
           public CategoryMaterialEntity()
           {
           }
           /// <summary>
           /// Desc:机种ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="CATEGORYID")]
           public string Categoryid { get; set; }
           /// <summary>
           /// Desc:物料ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="MATERIALID")]
           public string Materialid { get; set; }
           /// <summary>
           /// Desc:
           /// Default:0
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DELETED")]
           public int Deleted { get; set; }
    }
}