using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("DFM_M_CLASS_MAPPING")] 
    public class ClassMappingEntity : EntityBase
    {
        public ClassMappingEntity()
        {
        }
           /// <summary>
           /// Desc:分类ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="CLASS_ID")]
        public string ClassId { get; set; }
           /// <summary>
           /// Desc:关联ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="MAPPING_ID")]
        public string MappingId { get; set; }
           /// <summary>
           /// Desc:是否删除（0未删除，1已删除）
           /// Default:0
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DELETED")]
        public int Deleted { get; set; }

    }
}