using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("DFM_M_COMP_DEPT_MAPPING")] 
    public class CompDeptMappingEntity : EntityBase
    {
           public CompDeptMappingEntity()
           {
           }
           /// <summary>
           /// Desc:公司编号
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="COMPANYID")]
           public string Companyid { get; set; }
           /// <summary>
           /// Desc:部门编号
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="DEPARTMENTID")]
           public string Departmentid { get; set; }
           /// <summary>
           /// Desc:
           /// Default:0
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DELETED")]
           public int Deleted { get; set; }
    }
}