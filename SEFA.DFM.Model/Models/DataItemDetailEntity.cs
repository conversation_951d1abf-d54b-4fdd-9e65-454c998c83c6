using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;

namespace SEFA.DFM.Model.Models
{
	[SugarTable("DFM_M_DATAITEMDETAIL")]
	public class DataItemDetailEntity: EntityBase
	{
		
		[SugarColumn(ColumnName = "ITEMID")]
		public string ItemId { get; set; }

		[SugarColumn(ColumnName = "PARENTID")]
		public string ParentId { get; set; }

		[SugarColumn(ColumnName = "ITEMCODE")]
		public string ItemCode { get; set; }

		[SugarColumn(ColumnName = "ITEMNAME")]
		public string ItemName { get; set; }

		[SugarColumn(ColumnName = "ITEMVALUE")]
		public string ItemValue { get; set; }

		[SugarColumn(ColumnName = "QUICKQUERY")]
		public string QuickQuery { get; set; }

		[SugarColumn(ColumnName = "SIMPLESPELLING")]
		public string SimpleSpelling { get; set; }

		[SugarColumn(ColumnName = "ISDEFAULT")]
		public int? IsDefault { get; set; }

		[SugarColumn(ColumnName = "SORTCODE")]
		public int? SortCode { get; set; }


		[SugarColumn(ColumnName = "DESCRIPTION")]
		public string Description { get; set; }

		/// <summary>
		/// Desc:删除标识
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "DELETED")]
		public int Deleted { get; set; }

		/// <summary>
		/// Desc:是否能使用
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "ENABLE")]
		public int Enable { get; set; }

		/// <summary>
		/// 语种
		/// </summary>
		[SugarColumn(ColumnName = "LANG")]
		public string Lang { get; set; }

        /// <summary>
        /// 简称
        /// </summary>
        [SugarColumn(ColumnName = "SHORT_NAME")]
        public string ShortName { get; set; }

        /// <summary>
        /// 背景色
        /// </summary>
        [SugarColumn(ColumnName = "COLOR")]
        public string Color { get; set; }
    }

}
