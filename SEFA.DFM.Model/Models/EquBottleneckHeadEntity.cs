using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>

    [SugarTable("DFM_M_EQU_BOTTLENECK_HEAD")]
    public class EquBottleneckHeadEntity : EntityBase
    {
        public EquBottleneckHeadEntity () {
        }
        /// <summary>
        /// Desc:工厂
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "FACTORY")]
        public string Factory { get; set; }
        /// <summary>
        /// Desc:产线ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "LINE_ID")]
        public string LineId { get; set; }
        /// <summary>
        /// Desc:物料编码
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "MATERIAL_CODE")]
        public string MaterialCode { get; set; }
        /// <summary>
        /// Desc:物料组ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "MATERILA_GROUP_ID")]
        public string MaterialGroupId { get; set; }
        /// <summary>
        /// Desc:物料版本
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "MATERIAL_VERSION")]
        public string MaterialVersion { get; set; }
        /// <summary>
        /// Desc:生效时间
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "EFFECTIVE_START_DATE")]
        public DateTime? EffectiveStartDate { get; set; }
        /// <summary>
        /// Desc:失效时间
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "EFFECTIVE_END_DATE")]
        public DateTime? EffectiveEndDate { get; set; }
        /// <summary>
        /// Desc:删除标识
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "DELETED")]
        public int Deleted { get; set; }

    }
}