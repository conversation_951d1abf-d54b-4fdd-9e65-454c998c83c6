using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("DFM_M_EQUIP_ASSOC_INTERLOCK")] 
    public class EquipAssocInterlockEntity : EntityBase
    {
        public EquipAssocInterlockEntity()
        {
        }
           /// <summary>
           /// Desc:检查项ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="INTERLOCK_DEFINITION_ID")]
        public string InterlockDefinitionId { get; set; }
           /// <summary>
           /// Desc:设备执行动作ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="EQUIPMENT_ASSOC_ID")]
        public string EquipmentAssocId { get; set; }
           /// <summary>
           /// Desc:状态
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="STATUS")]
        public int Status { get; set; }

    }
}