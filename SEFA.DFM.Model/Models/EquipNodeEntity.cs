using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("DFM_M_EQUIP_NODE")] 
    public class EquipNodeEntity : EntityBase
    {
        public EquipNodeEntity()
        {
        }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="EQUIP_ID")]
        public string EquipId { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="GROUP_ID")]
        public string GroupId { get; set; }
        /// <summary>
        /// Desc:图形坐标
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "COORDINATE")]
        public string Coordinate { get; set; }
        /// <summary>
        /// Desc:宽
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "WIDTH")]
        public string Width { get; set; }
        /// <summary>
        /// Desc:高
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "HEIGHT")]
        public string Height { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName="DELETED")]
        public int Deleted { get; set; }

    }
}