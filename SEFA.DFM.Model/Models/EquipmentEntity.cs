using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>

    [SugarTable("DFM_M_EQUIPMENT")]
    public class EquipmentEntity : EntityBase
    {
        public EquipmentEntity()
        {
        }
        /// <summary>
        /// Desc:父级ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "PARENT_ID")]
        public string ParentId { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "EQUIPMENT_CODE")]
        public string EquipmentCode { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "EQUIPMENT_NAME")]
        public string EquipmentName { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "LEVEL")]
        public string Level { get; set; }
        /// <summary>
        /// Desc:备注
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "REMARK")]
        public string Remark { get; set; }
        /// <summary>
        /// Desc:删除标记
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "DELETED")]
        public int Deleted { get; set; }

        /// <summary>
        /// Desc:启用标记
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "ENABLED")]
        public int Enabled { get; set; }
        /// <summary>
        /// Desc:产线ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "LINE_ID")]

        public string LineId { get; set; }
        /// <summary>
        /// Desc:排序号
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "SORT_NUMBER")]
        public int SortNumber { get; set; } = 0;
        
    }
}