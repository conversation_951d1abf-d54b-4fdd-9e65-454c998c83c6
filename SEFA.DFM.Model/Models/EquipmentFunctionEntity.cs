using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>

    [SugarTable("DFM_R_EQUIPMENT_FUNCTION")]
    public class EquipmentFunctionEntity : EntityBase
    {
        public EquipmentFunctionEntity () {
        }
        /// <summary>
        /// Desc:分类ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "FUNCTION_ID")]
        public string FunctionId { get; set; }
        /// <summary>
        /// Desc:关联ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "EQUIPMENT_ID")]
        public string EquipmentId { get; set; }
        /// <summary>
        /// Desc:是否删除（0未删除，1已删除）
        /// Default:0
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "DELETED")]
        public int Deleted { get; set; } = 0;

    }
}