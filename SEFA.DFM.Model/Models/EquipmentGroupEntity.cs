using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("DFM_M_EQUIPMENT_GROUP")] 
    public class EquipmentGroupEntity : EntityBase
    {
        public EquipmentGroupEntity()
        {
        }
           /// <summary>
           /// Desc:类型
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="TYPE")]
        public string Type { get; set; }
           /// <summary>
           /// Desc:描述
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DESCRIPTION")]
        public string Description { get; set; }
           /// <summary>
           /// Desc:分组父节点
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="PARENT_EQUIPMENT_GROUP_ROW_ID")]
        public string ParentEquipmentGroupRowId { get; set; }
           /// <summary>
           /// Desc:X坐标
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="TOP_LEFT_X")]
        public string TopLeftX { get; set; }
           /// <summary>
           /// Desc:Y坐标
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="TOP_LEFT_Y")]
        public string TopLeftY { get; set; }
           /// <summary>
           /// Desc:排序
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="SORT_ORDER")]
        public string SortOrder { get; set; }
           /// <summary>
           /// Desc:状态
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="STATUS")]
        public string Status { get; set; }
           /// <summary>
           /// Desc:分组
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="CONSOLE_GROUP")]
        public string ConsoleGroup { get; set; }
           /// <summary>
           /// Desc:颜色
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="COLOR")]
        public string Color { get; set; }

    }
}