using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("DFM_M_EQUIPMENT_REQUIREMENT")] 
    public class EquipmentRequirementEntity : EntityBase
    {
        public EquipmentRequirementEntity()
        {
        }
           /// <summary>
           /// Desc:父节点
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="PARENT_ID")]
        public string ParentId { get; set; }
           /// <summary>
           /// Desc:仓储编码
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="CODE")]
        public string Code { get; set; }
           /// <summary>
           /// Desc:等级
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="LEVEL")]
        public string Level { get; set; }
           /// <summary>
           /// Desc:是否管理库存
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="MANAGE_INVENTORY")]
        public string ManageInventory { get; set; }
        /// <summary>
        /// Desc:是否喉头
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "IS_THROAT")]
        public string IsThroat { get; set; }

    }
}