using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("DFM_M_EQUIPMENTATTR")] 
    public class EquipmentattrEntity : EntityBase
    {
           public EquipmentattrEntity()
           {
           }
           /// <summary>
           /// Desc:设备ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="EQUIPMENTID")]
           public string Equipmentid { get; set; }
           /// <summary>
           /// Desc:属性
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="NAME")]
           public string Name { get; set; }
           /// <summary>
           /// Desc:属性代码
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="ATTRCODE")]
           public string Attrcode { get; set; }
           /// <summary>
           /// Desc:类型
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="TYPE")]
           public string Type { get; set; }
           /// <summary>
           /// Desc:值
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="VALUE")]
           public string Value { get; set; }
           /// <summary>
           /// Desc:
           /// Default:0
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DELETED")]
           public int Deleted { get; set; }
    }
}