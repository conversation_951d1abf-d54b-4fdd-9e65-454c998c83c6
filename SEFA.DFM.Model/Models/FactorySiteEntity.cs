using SEFA.Base.Model.BASE;
using SqlSugar;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///厂房（工作点、楼层）
    ///</summary>

    [SugarTable("DFM_M_FACTORY_SITE")]
    public class FactorySiteEntity : EntityBase
    {
        public FactorySiteEntity()
        {
        }
        /// <summary>
        /// Desc:显示名称
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "NAME")]
        public string Name { get; set; }
        /// <summary>
        /// Desc:标识Code
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "CODE")]
        public string Code { get; set; }
        /// <summary>
        /// Desc:排序
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SORT")]
        public int? Sort { get; set; }
        /// <summary>
        /// Desc:父级ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "PARENTID")]
        public string Parentid { get; set; }
        /// <summary>
        /// Desc:备注
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "REMARK")]
        public string Remark { get; set; }
        /// <summary>
        /// Desc:是否删除
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "DELETED")]
        public int Deleted { get; set; }

    }
}