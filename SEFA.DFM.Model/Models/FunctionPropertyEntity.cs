using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("DFM_M_FUNCTION_PROPERTY")] 
    public class FunctionPropertyEntity : EntityBase
    {
        public FunctionPropertyEntity()
        {
        }
           /// <summary>
           /// Desc:分类ID(CLASS_ID,ACTION_ID)
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="FUNCTION_ID")]
        public string FunctionId { get; set; }
           /// <summary>
           /// Desc:属性编码
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="PROPERTY_CODE")]
        public string PropertyCode { get; set; }
           /// <summary>
           /// Desc:属性名称
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="PROPERTY_NAME")]
        public string PropertyName { get; set; }
           /// <summary>
           /// Desc:属性类型(Function,Action)
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="PROPERTY_TYPE")]
        public string PropertyType { get; set; }
           /// <summary>
           /// Desc:值类型
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="VALUE_TYPE")]
        public string ValueType { get; set; }
           /// <summary>
           /// Desc:默认值
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="DEFAULT_VALUE")]
        public string DefaultValue { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="ENABLE")]
        public int? Enable { get; set; }
           /// <summary>
           /// Desc:是否删除
           /// Default:0
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DELETED")]
        public int Deleted { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="REMARK")]
        public string Remark { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="IDENTIFY")]
        public string Identify { get; set; }
    }
}