using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("DFM_SYS_HELPINDEX")] 
    public class HelpindexEntity : EntityBase
    {
        public HelpindexEntity()
        {
        }
           /// <summary>
           /// Desc:名称
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="NAME")]
        public string Name { get; set; }
           /// <summary>
           /// Desc:等级
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="LAYLEVEL")]
        public string Laylevel { get; set; }
           /// <summary>
           /// Desc:排序
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="SORT")]
        public int Sort { get; set; }
           /// <summary>
           /// Desc:上一级ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="PARENTID")]
        public string Parentid { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="DELETED")]
        public int Deleted { get; set; }

    }
}