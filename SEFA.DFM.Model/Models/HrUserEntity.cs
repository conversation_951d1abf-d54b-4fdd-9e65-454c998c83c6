using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("DFM_M_HR_USER")] 
    public class HrUserEntity : EntityBase
    {
        public HrUserEntity()
        {
        }
           /// <summary>
           /// Desc:工号
           /// Default:
           /// Nullable:False
           /// </summary>
        public string Badge { get; set; }
           /// <summary>
           /// Desc:姓名（中文）
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Name { get; set; }
           /// <summary>
           /// Desc:姓名（英文）
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Ename { get; set; }
           /// <summary>
           /// Desc:全称（中英文）
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="fullName")]
        public string Fullname { get; set; }
           /// <summary>
           /// Desc:性别
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="gender")]
        public string Gender { get; set; }
           /// <summary>
           /// Desc:域ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="adAccount")]
        public string Adaccount { get; set; }
           /// <summary>
           /// Desc:公司名
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="cptitle")]
        public string Cptitle { get; set; }
           /// <summary>
           /// Desc:BU
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="bu")]
        public string Bu { get; set; }
           /// <summary>
           /// Desc:功能组（部门）
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="dptitle3")]
        public string Dptitle3 { get; set; }
           /// <summary>
           /// Desc:功能组编号
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="hrisdpcode")]
        public string Hrisdpcode { get; set; }
           /// <summary>
           /// Desc:组类型
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="groupType")]
        public string Grouptype { get; set; }
           /// <summary>
           /// Desc:小组（产线）
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="groups")]
        public string Groups { get; set; }
           /// <summary>
           /// Desc:职位代码
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="empGrade2")]
        public string Empgrade2 { get; set; }
           /// <summary>
           /// Desc:职位
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="jobtitle")]
        public string Jobtitle { get; set; }
           /// <summary>
           /// Desc:职位代码
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="JBCode")]
        public string Jbcode { get; set; }
           /// <summary>
           /// Desc:入职日期
           /// Default:
           /// Nullable:True
           /// </summary>
        public DateTime? Joindate { get; set; }
           /// <summary>
           /// Desc:员工性质
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="empType")]
        public string Emptype { get; set; }
           /// <summary>
           /// Desc:员工性质代码
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="empTypeCode")]
        public string Emptypecode { get; set; }
           /// <summary>
           /// Desc:工作地点
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="workCity")]
        public string Workcity { get; set; }
           /// <summary>
           /// Desc:工作地点代码
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="workCityCode")]
        public string Workcitycode { get; set; }
           /// <summary>
           /// Desc:员工状态
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="empStatus")]
        public string Empstatus { get; set; }
           /// <summary>
           /// Desc:在职状态
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="jobStatus")]
        public string Jobstatus { get; set; }
           /// <summary>
           /// Desc:员工属性
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="empProperty")]
        public string Empproperty { get; set; }
           /// <summary>
           /// Desc:直接上级员工编号
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="reportto")]
        public string Reportto { get; set; }
           /// <summary>
           /// Desc:直接上级员工姓名
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="reporttoName")]
        public string Reporttoname { get; set; }
           /// <summary>
           /// Desc:办公室电话
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="OfficeTel")]
        public string Officetel { get; set; }
           /// <summary>
           /// Desc:手机号码
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Mobile { get; set; }
           /// <summary>
           /// Desc:邮箱
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Email { get; set; }
           /// <summary>
           /// Desc:成本中心
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Costcenter { get; set; }

    }
}