using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("DFM_M_INTERLOCK_LOG")] 
    public class InterlockLogEntity : EntityBase
    {
        public InterlockLogEntity()
        {
        }
           /// <summary>
           /// Desc:工厂节点ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="EQUIPMENT_ID")]
        public string EquipmentId { get; set; }
           /// <summary>
           /// Desc:仓储模型ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="EQUIPMENT_REQUIREMENT_ID")]
        public string EquipmentRequirementId { get; set; }
           /// <summary>
           /// Desc:绑定工厂节点ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="ASSOC_EQUIPMENT_ID")]
        public int? AssocEquipmentId { get; set; }
           /// <summary>
           /// Desc:状态
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="STATUS")]
        public string Status { get; set; }
           /// <summary>
           /// Desc:提示信息
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="STATUS_MESSAGE")]
        public string StatusMessage { get; set; }
           /// <summary>
           /// Desc:工单ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="PRODUCTION_EXECUTION_ROW_ID")]
        public string ProductionExecutionRowId { get; set; }

    }
}