using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///打印标签-国家月份缩写配置信息
    ///</summary>
    
    [SugarTable("DFM_M_LABEL_COUNTRY_M_OVERRIDE")] 
    public class LabelCountryMOverrideEntity : EntityBase
    {
        public LabelCountryMOverrideEntity()
        {
        }
           /// <summary>
           /// Desc:国家配置ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="COUNTRY_ID")]
        public string CountryId { get; set; }
           /// <summary>
           /// Desc:月份
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="MONTH")]
        public string Month { get; set; }
           /// <summary>
           /// Desc:覆写值
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="OVERRIDE_TEXT")]
        public string OverrideText { get; set; }

    }
}