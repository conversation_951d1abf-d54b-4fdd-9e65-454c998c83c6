using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///打印-打印日志表
    ///</summary>
    
    [SugarTable("DFM_M_LABEL_PRINT_HISTORY")] 
    public class LabelPrintHistoryEntity : EntityBase
    {
        public LabelPrintHistoryEntity()
        {
        }
           /// <summary>
           /// Desc:批次号
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="LOT_ID")]
        public string LotId { get; set; }
           /// <summary>
           /// Desc:子批次号
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="SUBLOT_ID")]
        public string SublotId { get; set; }
           /// <summary>
           /// Desc:数据
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DATA")]
        public string Data { get; set; }
           /// <summary>
           /// Desc:打印机
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="PRINTER_ID")]
        public string PrinterId { get; set; }
           /// <summary>
           /// Desc:模板
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="TEMPLATE_ID")]
        public string TemplateId { get; set; }
           /// <summary>
           /// Desc:设备来源
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="SOURCE_EQUIPMENT_ID")]
        public string SourceEquipmentId { get; set; }
           /// <summary>
           /// Desc:托盘
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="BATCH_PALLET_ID")]
        public string BatchPalletId { get; set; }
           /// <summary>
           /// Desc:日志类型
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="LOG_TYPE")]
        public string LogType { get; set; }
           /// <summary>
           /// Desc:下载状态
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="DOWNLOAD_STATUS")]
        public string DownloadStatus { get; set; }
           /// <summary>
           /// Desc:工单ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="PRODUCTION_ORDER_ID")]
       public string ProductionOrderId { get; set; }
           /// <summary>
           /// Desc:板号
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="BOARD")]
       public int? Board { get; set; }
           /// <summary>
           /// Desc:状态
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="STATUS")]
        public string Status { get; set; }
    }
}