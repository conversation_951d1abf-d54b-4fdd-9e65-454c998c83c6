using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///打印-打印机属性配置表
    ///</summary>
    
    [SugarTable("DFM_M_LABEL_PRINTER_CLASS_PROP")] 
    public class LabelPrinterClassPropEntity : EntityBase
    {
        public LabelPrinterClassPropEntity()
        {
        }
           /// <summary>
           /// Desc:打印机类型
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="PRINTER_CLASS_ID")]
        public string PrinterClassId { get; set; }
           /// <summary>
           /// Desc:属性
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="PROPERTY_DEFINITION_ID")]
        public string PropertyDefinitionId { get; set; }
           /// <summary>
           /// Desc:是否需要
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="IS_REQUIRED")]
        public string IsRequired { get; set; }

    }
}