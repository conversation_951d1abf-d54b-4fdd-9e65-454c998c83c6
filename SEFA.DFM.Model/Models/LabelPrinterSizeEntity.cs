using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///打印标签-规格信息
    ///</summary>
    
    [SugarTable("DFM_M_LABEL_PRINTER_SIZE")] 
    public class LabelPrinterSizeEntity : EntityBase
    {
        public LabelPrinterSizeEntity()
        {
        }
           /// <summary>
           /// Desc:大小
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="SIZE")]
        public string Size { get; set; }

    }
}