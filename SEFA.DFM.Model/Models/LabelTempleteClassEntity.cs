using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///打印标签-模板内容配置
    ///</summary>
    
    [SugarTable("DFM_M_LABEL_TEMPLETE_CLASS")] 
    public class LabelTempleteClassEntity : EntityBase
    {
        public LabelTempleteClassEntity()
        {
        }
           /// <summary>
           /// Desc:名称
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="CODE")]
        public string Code { get; set; }
           /// <summary>
           /// Desc:描述
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DESCRIPTION")]
        public string Description { get; set; }
           /// <summary>
           /// Desc:打印类型
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="PRINTER_TYPE")]
        public string PrinterType { get; set; }
           /// <summary>
           /// Desc:数据定义
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="FIELD_DEFINITIONS")]
        public string FieldDefinitions { get; set; }

    }
}