using System;
using System.Linq;
using System.Text;
using SqlSugar;
using SEFA.Base.Model.BASE;

namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///打印-SAP标签模板配置表
    ///</summary>
    [SugarTable("DFM_M_LABELBBD_FORMAT")]
    public class LabelbbdFormatEntity : EntityBase
    {
        public LabelbbdFormatEntity()
        {
        }

        /// <summary>
        /// Desc:有效期类型
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "EXPIRATION_TYPE")]
        public string ExpirationType { get; set; }

        /// <summary>
        /// Desc:格式ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "FORMAT_ID")]
        public string FormatId { get; set; }

        /// <summary>
        /// Desc:周期
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "PERIOD_IND_FOR_SLED")]
        public string PeriodIndForSled { get; set; }

        /// <summary>
        /// Desc:BBD显示格式
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "BBD_DISPLAY_FORMAT")]
        public string BbdDisplayFormat { get; set; }

        /// <summary>
        /// Desc:BBD显示内容
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "BBD_DISPLAY_TEXT")]
        public string BbdDisplayText { get; set; }

        /// <summary>
        /// Desc:大写日期
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "UPPER_CASE_DATE")]
        public string UpperCaseDate { get; set; }

        /// <summary>
        /// Desc:条形码
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "BARCODE_AI")]
        public string BarcodeAi { get; set; }

        /// <summary>
        /// Desc:条形码格式
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "BARCODE_DATE_FORMAT")]
        public string BarcodeDateFormat { get; set; }
    }
}