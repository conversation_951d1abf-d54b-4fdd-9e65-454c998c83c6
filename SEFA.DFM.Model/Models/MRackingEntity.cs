using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using System.Collections.Generic;

namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>

    [SugarTable("DFM_M_MRacking")]
    public class MRackingEntity : EntityBase
    {

        public MRackingEntity()
        {
        }

        #region 实体成员
        /// <summary>
        /// 工厂
        /// </summary>
        [SugarColumn(ColumnName = "FACTORY")] 
        public string FACTORY { get; set; }
        /// <summary>
        /// 货架代码
        /// </summary>
        [SugarColumn(ColumnName = "RACKING_CODE")] 
        public string RACKING_CODE { get; set; }
        /// <summary>
        /// 货架名称
        /// </summary>
        [SugarColumn(ColumnName = "RACKING_NAME")] 
        public string RACKING_NAME { get; set; }
        /// <summary>
        /// 货架位置描述
        /// </summary>
        [SugarColumn(ColumnName = "DESCRIPTION")] 
        public string DESCRIPTION { get; set; }
        /// <summary>
        /// 有效标志
        /// </summary>
        [SugarColumn(ColumnName = "STATUS")] 
        public string STATUS { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(ColumnName = "REMARK")] 
        public string REMARK { get; set; }
        #endregion

        /// <summary>
        /// 货架ID 
        /// </summary>
        [SugarColumn(ColumnName = "RACKING_ID")] 
        public string RACKING_ID { get; set; }
        /// <summary>
        /// 设备号
        /// </summary>
        [SugarColumn(ColumnName = "DEVICE_CODE")] 
        public string DEVICE_CODE { get; set; }
        [SugarColumn(ColumnName = "Pagination")] 
        public Pagination Pagination { get; set; }
        /// <summary>
        /// 货架基本信息列表
        /// </summary>
        public List<MRackingEntity> ListData { get; set; }
        /// <summary>
        /// 货架对应设备列表
        /// </summary>
       // public List<MRackingDeviceModel> RackingDeviceListData { get; set; }
        /// <summary>
        /// 货架位列表
        /// </summary>
        //public List<MRackingBinModel> RackingBinListData { get; set; }
    }
}
