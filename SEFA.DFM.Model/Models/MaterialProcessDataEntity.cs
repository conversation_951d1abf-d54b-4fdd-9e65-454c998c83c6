using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("DFM_M_MATERIAL_PROCESS_DATA")] 
    public class MaterialProcessDataEntity : EntityBase
    {
        public MaterialProcessDataEntity()
        {
        }
           /// <summary>
           /// Desc:产品版本
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="VERSION_ID")]
        public string VersionId { get; set; }
           /// <summary>
           /// Desc:工艺长文本
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="PROCESS_DATA")]
        public string ProcessData { get; set; }
           /// <summary>
           /// Desc:长文本编码
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="HASH_DATA")]
        public string HashData { get; set; }
           /// <summary>
           /// Desc:长文本版本
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="TEXT_VERSION")]
        public int TextVersion { get; set; }
           /// <summary>
           /// Desc:已提醒
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="IS_REMINDED")]
        public string IsReminded { get; set; }
           /// <summary>
           /// Desc:状态
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="STATUS")]
        public string Status { get; set; }
           /// <summary>
           /// Desc:工单号
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="Order_ID")]
        public string OrderId { get; set; }
           /// <summary>
           /// Desc:审核时间
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="REVIEWTIME")]
        public DateTime? Reviewtime { get; set; }
           /// <summary>
           /// Desc:审核人员
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="REVIEWUSERID")]
        public string Reviewuserid { get; set; }
        /// <summary>
        /// Desc:类型
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "TYPE")]
        public int Type { get; set; }
        /// <summary>
        /// Desc:Token
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "TOKEN")]
        public string Token { get; set; }        
    }
}