using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models.DATAWAREHOUSE
{
    ///<summary>
    ///
    ///</summary>

    [SugarTable("RPT_M_DataModel_Seting", "DATAWAREHOUSE")]
    public class DatamodelEntity
    {
        public DatamodelEntity()
        {
        }
        [SugarColumn(IsPrimaryKey = true,ColumnName ="ID")]
        public string ID { get; set; }

        /// <summary>
        /// Desc:产线名称
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "ProjectName")]
        public string Projectname { get; set; }
        /// <summary>
        /// Desc:产线编码
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "ProductLine")]
        public string Productline { get; set; }
        /// <summary>
        /// Desc:工段编码
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SegmentCode")]
        public string Segmentcode { get; set; }
        
        /// <summary>
        /// Desc:工段名称
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SegmentName")]
        public string Segmentname { get; set; }
        /// <summary>
        /// Desc:工序名称
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "ProcessCode")]
        public string Processcode { get; set; }
        /// <summary>
        /// Desc:工序名称
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "ProcessName")]
        public string Processname { get; set; }
        /// <summary>
        /// Desc:设备编码
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "DeviceCode")]
        public string Devicecode { get; set; }
        /// <summary>
        /// Desc:投入物料编码
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "InMaterialCode")]
        public string Inmaterialcode { get; set; }
        /// <summary>
        /// Desc:投入物料名称
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "InMaterialName")]
        public string Inmaterialname { get; set; }
        /// <summary>
        /// Desc:产出物料编码
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "OutMaterialCode")]
        public string Outmaterialcode { get; set; }
        /// <summary>
        /// Desc:产出物料名称
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "OutMaterialName")]
        public string Outmaterialname { get; set; }
        /// <summary>
        /// Desc:投入采集点
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "InTag")]
        public string Intag { get; set; }
        /// <summary>
        /// Desc:故障采集点
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "FaultTag")]
        public string Faulttag { get; set; }
        /// <summary>
        /// Desc:合格产出采集点
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "OKTag")]
        public string Oktag { get; set; }
        /// <summary>
        /// Desc:NG品采集点
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "NGTag")]
        public string Ngtag { get; set; }
        /// <summary>
        /// Desc:是否上料工站（1:是，0:否）
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "IsFeed")]
        public int? Isfeed { get; set; }
        /// <summary>
        /// Desc:CT
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "CT")]
        public decimal? Ct { get; set; }
        /// <summary>
        /// Desc:是否瓶颈工站（1:是，0:否）
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "IsChokePoint")]
        public int? Ischokepoint { get; set; }
        /// <summary>
        /// Desc:是否工段收盘站（1:是，0:否）
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "IsSegmentTag")]
        public int? Issegmenttag { get; set; }
        /// <summary>
        /// Desc:是否整线收盘站（1:是，0:否）
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "IsProductLineTag")]
        public int? Isproductlinetag { get; set; }
        /// <summary>
        /// Desc:工站排序
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "Order_Num")]
        public int? OrderNum { get; set; }
        /// <summary>
        /// Desc:工段排序
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "Segment_Seq")]
        public int? SegmentSeq { get; set; }
        /// <summary>
        /// Desc:CT采集点
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "CT_Tag")]
        public string CtTag { get; set; }
        /// <summary>
        /// Desc:设备状态采集点
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "StopStatusTag")]
        public string Stopstatustag { get; set; }
        /// <summary>
        /// Desc:SFC测试机台ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SFCMachineId")]
        public int? Sfcmachineid { get; set; }
        /// <summary>
        /// Desc:SFC数据库名称
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SFCDBName")]
        public string Sfcdbname { get; set; }
        /// <summary>
        /// Desc:是否为测试工段
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "IsTestSegment")]
        public int? Istestsegment { get; set; }
        /// <summary>
        /// Desc:是否为工段良率计算物料上料点（1:是，0:否）
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "IsSegmentMaterial")]
        public int? Issegmentmaterial { get; set; }
        /// <summary>
        /// Desc:原始CT
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "OrgCT")]
        public decimal? Orgct { get; set; }
        /// <summary>
        /// Desc:工段是否显示(1:是，0:否)
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "IsEnabled")]
        public int? Isenabled { get; set; }
        /// <summary>
        /// Desc:是否启用
        /// Default:
        /// Nullable:True
        /// </summary>
        public int? Enable { get; set; }

        /// <summary>
        /// Desc:产品型号
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "ProductCode")]
        public string ProductCode { get; set; }
        /// <summary>
        /// Desc:产品型号名称
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "ProductName")]
        public string ProductName { get; set; }
        /// <summary>
        /// Desc:产品类型：马达/单体
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "ProductType")]
        public string ProductType { get; set; }

    }
}