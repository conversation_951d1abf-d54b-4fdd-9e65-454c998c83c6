using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models.DATAWAREHOUSE
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("RPT_M_ProductLineOutTag_Seting", "DATAWAREHOUSE")] 
    public class ProductlineouttagEntity 
    {
        public ProductlineouttagEntity()
        {
        }
        [SugarColumn(IsPrimaryKey = true, ColumnName = "ID")]
        public string ID { get; set; }
        /// <summary>
        /// Desc:产线编码
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName="ProductLine_id")]
        public string ProductlineId { get; set; }
           /// <summary>
           /// Desc:产线名称
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="ProductLine_Name")]
        public string ProductlineName { get; set; }
           /// <summary>
           /// Desc:工段ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="Segment_id")]
        public string SegmentId { get; set; }
           /// <summary>
           /// Desc:工段编码
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="Segment_Code")]
        public string SegmentCode { get; set; }
           /// <summary>
           /// Desc:工段名称
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="Segment_Name")]
        public string SegmentName { get; set; }
           /// <summary>
           /// Desc:产出采集点
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="InTag")]
        public string Intag { get; set; }
           /// <summary>
           /// Desc:合格产出点
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="OKTag")]
        public string Oktag { get; set; }
           /// <summary>
           /// Desc:是否为组装工段
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="IsLastSegment")]
        public int? Islastsegment { get; set; }
           /// <summary>
           /// Desc:产出成品物料编码
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="MaterialCode")]
        public string Materialcode { get; set; }
           /// <summary>
           /// Desc:产出成品物料名称
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="MaterialName")]
        public string Materialname { get; set; }
           /// <summary>
           /// Desc:PLC产出TAG点
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="PLC_OKTag")]
        public string PlcOktag { get; set; }
           /// <summary>
           /// Desc:是否启用
           /// Default:
           /// Nullable:True
           /// </summary>
        public int? Enable { get; set; }
    }
}