using Magicodes.ExporterAndImporter.Core;
using Magicodes.ExporterAndImporter.Excel;
using OfficeOpenXml.Table;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace SEFA.DFM.Model.Models.Models.ViewModel
{
    [ExcelExporter(Name = "报表基础模型配置", TableStyle = OfficeOpenXml.Table.TableStyles.Medium7,
  AutoFitAllColumn = true)]
    public class DatamodelDto
    {
        /// <summary>
        /// Desc:ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [ExporterHeader(DisplayName = "ID(勿修改)")]
        [ImporterHeader(Name = "ID(勿修改)")]
        public string ID { get; set; }
        /// <summary>
        /// Desc:产线名称
        /// Default:
        /// Nullable:True
        /// </summary>
        [ExporterHeader(DisplayName = "产线名称")]
        [ImporterHeader(Name = "产线名称")]
        public string Projectname { get; set; }
        /// <summary>
        /// Desc:产线编码
        /// Default:
        /// Nullable:True
        /// </summary>
        [ExporterHeader(DisplayName = "产线编码")]
        [ImporterHeader(Name = "产线编码")]
        public string Productline { get; set; }
        /// <summary>
        /// Desc:工段编码
        /// Default:
        /// Nullable:True
        /// </summary>
        [ExporterHeader(DisplayName = "工段编码")]
        [ImporterHeader(Name = "工段编码")]
        public string Segmentcode { get; set; }
        /// <summary>
        /// Desc:工段名称
        /// Default:
        /// Nullable:True
        /// </summary>
        [ExporterHeader(DisplayName = "工段名称")]
        [ImporterHeader(Name = "工段名称")]
        public string Segmentname { get; set; }
        /// <summary>
        /// Desc:工序名称
        /// Default:
        /// Nullable:True
        /// </summary>
        [ExporterHeader(DisplayName = "工序名称")]
        [ImporterHeader(Name = "工序名称")]
        public string Processname { get; set; }
        /// <summary>
        /// Desc:设备编码
        /// Default:
        /// Nullable:True
        /// </summary>
        [ExporterHeader(DisplayName = "设备编码")]
        [ImporterHeader(Name = "设备编码")]
        public string Devicecode { get; set; }
        /// <summary>
        /// Desc:投入物料编码
        /// Default:
        /// Nullable:True
        /// </summary>
        [ExporterHeader(DisplayName = "投入物料编码")]
        [ImporterHeader(Name = "投入物料编码")]
        public string Inmaterialcode { get; set; }
        /// <summary>
        /// Desc:投入物料名称
        /// Default:
        /// Nullable:True
        /// </summary>
        [ExporterHeader(DisplayName = "投入物料名称")]
        [ImporterHeader(Name = "投入物料名称")]
        public string Inmaterialname { get; set; }
        /// <summary>
        /// Desc:产出物料编码
        /// Default:
        /// Nullable:True
        /// </summary>
        [ExporterHeader(DisplayName = "产出物料编码")]
        [ImporterHeader(Name = "产出物料编码")]
        public string Outmaterialcode { get; set; }
        /// <summary>
        /// Desc:产出物料名称
        /// Default:
        /// Nullable:True
        /// </summary>
        [ExporterHeader(DisplayName = "产出物料名称")]
        [ImporterHeader(Name = "产出物料名称")]
        public string Outmaterialname { get; set; }
        /// <summary>
        /// Desc:投入采集点
        /// Default:
        /// Nullable:True
        /// </summary>
        [ExporterHeader(DisplayName = "投入采集点")]
        [ImporterHeader(Name = "投入采集点")]
        public string Intag { get; set; }
        /// <summary>
        /// Desc:故障采集点
        /// Default:
        /// Nullable:True
        /// </summary>
        [ExporterHeader(DisplayName = "故障采集点")]
        [ImporterHeader(Name = "故障采集点")]
        public string Faulttag { get; set; }
        /// <summary>
        /// Desc:合格产出采集点
        /// Default:
        /// Nullable:True
        /// </summary>
        [ExporterHeader(DisplayName = "合格产出采集点")]
        [ImporterHeader(Name = "合格产出采集点")]
        public string Oktag { get; set; }
        /// <summary>
        /// Desc:NG品采集点
        /// Default:
        /// Nullable:True
        /// </summary>
        [ExporterHeader(DisplayName = "NG品采集点")]
        [ImporterHeader(Name = "NG品采集点")]
        public string Ngtag { get; set; }
        /// <summary>
        /// Desc:是否上料工站（1:是，0:否）
        /// Default:
        /// Nullable:True
        /// </summary>
        [ExporterHeader(DisplayName = "是否上料工站（1:是，0:否）")]
        [ImporterHeader(Name = "是否上料工站（1:是，0:否）")]
        public int? Isfeed { get; set; }
        /// <summary>
        /// Desc:CT
        /// Default:
        /// Nullable:True
        /// </summary>
        [ExporterHeader(DisplayName = "CT")]
        [ImporterHeader(Name = "CT")]
        public decimal? Ct { get; set; }
        /// <summary>
        /// Desc:是否瓶颈工站（1:是，0:否）
        /// Default:
        /// Nullable:True
        /// </summary>
        [ExporterHeader(DisplayName = "是否瓶颈工站（1:是，0:否）")]
        [ImporterHeader(Name = "是否瓶颈工站（1:是，0:否）")]
        public int? Ischokepoint { get; set; }
        /// <summary>
        /// Desc:是否工段收盘站（1:是，0:否）
        /// Default:
        /// Nullable:True
        /// </summary>
        [ExporterHeader(DisplayName = "是否工段收盘站（1:是，0:否）")]
        [ImporterHeader(Name = "是否工段收盘站（1:是，0:否）")]
        public int? Issegmenttag { get; set; }
        /// <summary>
        /// Desc:是否整线收盘站（1:是，0:否）
        /// Default:
        /// Nullable:True
        /// </summary>
        [ExporterHeader(DisplayName = "是否整线收盘站（1:是，0:否）")]
        [ImporterHeader(Name = "是否整线收盘站（1:是，0:否）")]
        public int? Isproductlinetag { get; set; }
        /// <summary>
        /// Desc:工站排序
        /// Default:
        /// Nullable:True
        /// </summary>
        [ExporterHeader(DisplayName = "工站排序")]
        [ImporterHeader(Name = "工站排序")]
        public int? OrderNum { get; set; }
        /// <summary>
        /// Desc:工段排序
        /// Default:
        /// Nullable:True
        /// </summary>
        [ExporterHeader(DisplayName = "工段排序")]
        [ImporterHeader(Name = "工段排序")]
        public int? SegmentSeq { get; set; }
        /// <summary>
        /// Desc:CT采集点
        /// Default:
        /// Nullable:True
        /// </summary>
        [ExporterHeader(DisplayName = "CT采集点")]
        [ImporterHeader(Name = "CT采集点")]
        public string CtTag { get; set; }
        /// <summary>
        /// Desc:设备状态采集点
        /// Default:
        /// Nullable:True
        /// </summary>
        [ExporterHeader(DisplayName = "设备状态采集点")]
        [ImporterHeader(Name = "设备状态采集点")]
        public string Stopstatustag { get; set; }
        /// <summary>
        /// Desc:SFC测试机台ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [ExporterHeader(DisplayName = "SFC测试机台ID")]
        [ImporterHeader(Name = "SFC测试机台ID")]
        public int? Sfcmachineid { get; set; }
        /// <summary>
        /// Desc:SFC数据库名称
        /// Default:
        /// Nullable:True
        /// </summary>
        [ExporterHeader(DisplayName = "SFC数据库名称")]
        [ImporterHeader(Name = "SFC数据库名称")]
        public string Sfcdbname { get; set; }
        /// <summary>
        /// Desc:是否为测试工段
        /// Default:
        /// Nullable:True
        /// </summary>
        [ExporterHeader(DisplayName = "是否为测试工段")]
        [ImporterHeader(Name = "是否为测试工段")]
        public int? Istestsegment { get; set; }
        /// <summary>
        /// Desc:是否为工段良率计算物料上料点（1:是，0:否）
        /// Default:
        /// Nullable:True
        /// </summary>
        [ExporterHeader(DisplayName = "是否为工段良率计算物料上料点（1:是，0:否）")]
        [ImporterHeader(Name = "是否为工段良率计算物料上料点（1:是，0:否）")]
        public int? Issegmentmaterial { get; set; }
        /// <summary>
        /// Desc:原始CT
        /// Default:
        /// Nullable:True
        /// </summary>
        [ExporterHeader(DisplayName = "原始CT")]
        [ImporterHeader(Name = "原始CT")]
        public decimal? Orgct { get; set; }
        /// <summary>
        /// Desc:工段是否显示(1:是，0:否)
        /// Default:
        /// Nullable:True
        /// </summary>
        [ExporterHeader(DisplayName = "工段是否显示(1:是，0:否)")]
        [ImporterHeader(Name = "工段是否显示(1:是，0:否)")]
        public int? Isenabled { get; set; }
        /// <summary>
        /// Desc:是否启用
        /// Default:
        /// Nullable:True
        /// </summary>
        [ExporterHeader(DisplayName = "是否启用(1:是，0:否)")]
        [ImporterHeader(Name = "是否启用(1:是，0:否)")]
        public int? Enable { get; set; }
        /// <summary>
        /// Desc:产品型号
        /// Default:
        /// Nullable:True
        /// </summary>
        [ExporterHeader(DisplayName = "产品型号")]
        [ImporterHeader(Name = "产品型号")]
        public string ProductCode { get; set; }
        /// <summary>
        /// Desc:产品型号名称
        /// Default:
        /// Nullable:True
        /// </summary>
        [ExporterHeader(DisplayName = "产品型号名称")]
        [ImporterHeader(Name = "产品型号名称")]
        public string ProductName { get; set; }
        /// <summary>
        /// Desc:产品类型：马达/单体
        /// Default:
        /// Nullable:True
        /// </summary>
        [ExporterHeader(DisplayName = "产品类型：马达/单体")]
        [ImporterHeader(Name = "产品类型：马达/单体")]
        public string ProductType { get; set; }
    }
}
