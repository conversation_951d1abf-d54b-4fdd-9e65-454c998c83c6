using Magicodes.ExporterAndImporter.Core;
using Magicodes.ExporterAndImporter.Excel;
using OfficeOpenXml.Table;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace SEFA.DFM.Model.Models.Models.ViewModel
{
    [ExcelExporter(Name = "工段收盘站采集点配置", TableStyle = OfficeOpenXml.Table.TableStyles.Medium7,
AutoFitAllColumn = true)]
    public class ProductlineouttagDto
    {
        /// <summary>
        /// Desc:ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [ExporterHeader(DisplayName = "ID(勿修改)")]
        [ImporterHeader(Name = "ID(勿修改)")]
        public string ID { get; set; }
        /// <summary>
        /// Desc:产线编码
        /// Default:
        /// Nullable:True
        /// </summary>
        [ExporterHeader(DisplayName = "产线编码")]
        [ImporterHeader(Name = "产线编码")]
        public string ProductlineId { get; set; }
        /// <summary>
        /// Desc:产线名称
        /// Default:
        /// Nullable:True
        /// </summary>
        [ExporterHeader(DisplayName = "产线名称")]
        [ImporterHeader(Name = "产线名称")]
        public string ProductlineName { get; set; }
        /// <summary>
        /// Desc:工段ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [ExporterHeader(DisplayName = "工段ID")]
        [ImporterHeader(Name = "工段ID")]
        public string SegmentId { get; set; }
        /// <summary>
        /// Desc:工段编码
        /// Default:
        /// Nullable:True
        /// </summary>
        [ExporterHeader(DisplayName = "工段编码")]
        [ImporterHeader(Name = "工段编码")]
        public string SegmentCode { get; set; }
        /// <summary>
        /// Desc:工段名称
        /// Default:
        /// Nullable:True
        /// </summary>
        [ExporterHeader(DisplayName = "工段名称")]
        [ImporterHeader(Name = "工段名称")]
        public string SegmentName { get; set; }
        /// <summary>
        /// Desc:产出采集点
        /// Default:
        /// Nullable:True
        /// </summary>
        [ExporterHeader(DisplayName = "产出采集点")]
        [ImporterHeader(Name = "产出采集点")]
        public string Intag { get; set; }
        /// <summary>
        /// Desc:合格产出点
        /// Default:
        /// Nullable:True
        /// </summary>
        [ExporterHeader(DisplayName = "合格产出点")]
        [ImporterHeader(Name = "合格产出点")]
        public string Oktag { get; set; }
        /// <summary>
        /// Desc:是否为组装工段
        /// Default:
        /// Nullable:True
        /// </summary>
        [ExporterHeader(DisplayName = "是否为组装工段")]
        [ImporterHeader(Name = "是否为组装工段")]
        public int? Islastsegment { get; set; }
        /// <summary>
        /// Desc:产出成品物料编码
        /// Default:
        /// Nullable:True
        /// </summary>
        [ExporterHeader(DisplayName = "产出成品物料编码")]
        [ImporterHeader(Name = "产出成品物料编码")]
        public string Materialcode { get; set; }
        /// <summary>
        /// Desc:产出成品物料名称
        /// Default:
        /// Nullable:True
        /// </summary>
        [ExporterHeader(DisplayName = "产出成品物料名称")]
        [ImporterHeader(Name = "产出成品物料名称")]
        public string Materialname { get; set; }
        /// <summary>
        /// Desc:PLC产出TAG点
        /// Default:
        /// Nullable:True
        /// </summary>
        [ExporterHeader(DisplayName = "PLC产出TAG点")]
        [ImporterHeader(Name = "PLC产出TAG点")]
        public string PlcOktag { get; set; }
        /// <summary>
        /// Desc:是否启用
        /// Default:
        /// Nullable:True
        /// </summary>
        [ExporterHeader(DisplayName = "是否启用")]
        [ImporterHeader(Name = "是否启用")]
        public int? Enable { get; set; }
    }
}
