using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("DFM_M_OPC_FUNC_PROP")] 
    public class OpcFuncPropEntity : EntityBase
    {
        public OpcFuncPropEntity()
        {
        }
           /// <summary>
           /// Desc:OPC功能ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="OPC_FUNCTION_ID")]
        public string OpcFunctionId { get; set; }
           /// <summary>
           /// Desc:名称
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="NAME")]
        public string Name { get; set; }
           /// <summary>
           /// Desc:描述
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DESCRIPTION")]
        public string Description { get; set; }
           /// <summary>
           /// Desc:数据类型
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DATA_TYPE")]
        public string DataType { get; set; }
           /// <summary>
           /// Desc:初始值
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="INITIAL_VALUE")]
        public string InitialValue { get; set; }
           /// <summary>
           /// Desc:空值
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="NULL_VALUE")]
        public string NullValue { get; set; }
           /// <summary>
           /// Desc:是否绑定
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="IS_BOUND")]
        public string IsBound { get; set; }
           /// <summary>
           /// Desc:是否订阅
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="IS_SUBSCRIBED")]
        public string IsSubscribed { get; set; }
           /// <summary>
           /// Desc:类型
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="TYPE")]
        public int Type { get; set; }
           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DELETED")]
        public int Deleted { get; set; }

    }
}