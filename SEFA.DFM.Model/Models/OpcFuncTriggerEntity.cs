using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("DFM_M_OPC_FUNC_TRIGGER")] 
    public class OpcFuncTriggerEntity : EntityBase
    {
        public OpcFuncTriggerEntity()
        {
        }
           /// <summary>
           /// Desc:OPC功能ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="OPC_FUNCTION_ID")]
        public string OpcFunctionId { get; set; }
           /// <summary>
           /// Desc:触发类型
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="TRIGGER_TYPE")]
        public string TriggerType { get; set; }
           /// <summary>
           /// Desc:OPC功能属性ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="OPC_FUNCTION_PROPERTY_ID")]
        public string OpcFunctionPropertyId { get; set; }
           /// <summary>
           /// Desc:触发条件
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="TRIGGER_CONDITION")]
        public string TriggerCondition { get; set; }
           /// <summary>
           /// Desc:关闭条件
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="OFF_CONDITION")]
        public string OffCondition { get; set; }
           /// <summary>
           /// Desc:频率
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="FREQUENCY")]
        public int? Frequency { get; set; }
           /// <summary>
           /// Desc:OPC动作类ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="OPC_ACTION_CLASS_ID")]
        public string OpcActionClassId { get; set; }
           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DELETED")]
        public int Deleted { get; set; }

    }
}