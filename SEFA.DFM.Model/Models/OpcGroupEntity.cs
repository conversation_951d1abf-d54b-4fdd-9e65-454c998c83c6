using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("DFM_M_OPC_GROUP")] 
    public class OpcGroupEntity : EntityBase
    {
        public OpcGroupEntity()
        {
        }
           /// <summary>
           /// Desc:OPCServerID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="OPC_SERVER_ID")]
        public string OpcServerId { get; set; }
           /// <summary>
           /// Desc:名称
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="NAME")]
        public string Name { get; set; }
           /// <summary>
           /// Desc:描述
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DESCRIPTION")]
        public string Description { get; set; }
           /// <summary>
           /// Desc:扫描频率
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="SCAN_RATE")]
        public int ScanRate { get; set; }
           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DELETED")]
        public int Deleted { get; set; }

    }
}