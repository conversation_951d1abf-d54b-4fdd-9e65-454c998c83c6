using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("DFM_M_OPC_REF_ASSEMBLY")] 
    public class OpcRefAssemblyEntity : EntityBase
    {
        public OpcRefAssemblyEntity()
        {
        }
           /// <summary>
           /// Desc:程序集路径
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="ASSEMBLY_PATH")]
        public string AssemblyPath { get; set; }
           /// <summary>
           /// Desc:类型
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="TYPE")]
        public string Type { get; set; }
           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DELETED")]
        public int Deleted { get; set; }

    }
}