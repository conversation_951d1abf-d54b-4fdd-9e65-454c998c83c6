using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("DFM_M_OPC_SERVER")] 
    public class OpcServerEntity : EntityBase
    {
        public OpcServerEntity()
        {
        }
           /// <summary>
           /// Desc:URL
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="URI")]
        public string Uri { get; set; }
           /// <summary>
           /// Desc:描述
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DESCRIPTION")]
        public string Description { get; set; }
           /// <summary>
           /// Desc:服务器类型
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="SERVER_TYPE")]
        public string ServerType { get; set; }
           /// <summary>
           /// Desc:查找URI
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="LOOKUP_URI")]
        public string LookupUri { get; set; }
           /// <summary>
           /// Desc:URI前缀
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="URI_PREFIX")]
        public string UriPrefix { get; set; }
           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DELETED")]
        public int Deleted { get; set; }

    }
}