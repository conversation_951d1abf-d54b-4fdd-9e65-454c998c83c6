using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("DFM_R_PHASE_SALESCONTAINER")] 
    public class PhaseSalescontainerEntity : EntityBase
    {
        public PhaseSalescontainerEntity()
        {
        }
           /// <summary>
           /// Desc:工序ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="SEGMENT_ID")]
        public string SegmentId { get; set; }
           /// <summary>
           /// Desc:SalesContainer ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="SALESCONTAINER_ID")]
        public string SalescontainerId { get; set; }
           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DELETED")]
        public int Deleted { get; set; }

    }
}