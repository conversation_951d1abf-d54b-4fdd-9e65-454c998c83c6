using SEFA.Base.Model.BASE;
using SqlSugar;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///打印模板属性表
    ///</summary>

    [SugarTable("DFM_M_PRINT_TPL_PROPERTY")]
    public class PrintTplPropertyEntity : EntityBase
    {
        public PrintTplPropertyEntity()
        {
        }

        /// <summary>
        /// Desc:模板ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "TPL_ID")]
        public string TPL_ID { get; set; }

        /// <summary>
        /// Desc:名称
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "NAME")]
        public string Name { get; set; }
        /// <summary>
        /// Desc:值
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "VALUE")]
        public string Value { get; set; }
        /// <summary>
        /// Desc:类型
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "TYPE")]
        public string Type { get; set; }
        /// <summary>
        /// Desc:状态
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "STATUS")]
        public int? Status { get; set; }
        /// <summary>
        /// Desc:是否删除
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "DELETED")]
        public int Deleted { get; set; }

    }
}