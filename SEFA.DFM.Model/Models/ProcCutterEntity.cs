using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("DFM_M_PROC_CUTTER")] 
    public class ProcCutterEntity : EntityBase
    {
           public ProcCutterEntity()
           {
           }
           /// <summary>
           /// Desc:工艺路线明细ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="ROUTING_DETAIL_ID")]
           public string RoutingDetailId { get; set; }
           /// <summary>
           /// Desc:设备类型
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="DEVICE_TYPE")]
           public string DeviceType { get; set; }
           /// <summary>
           /// Desc:刀具所属工厂
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="CUTTER_FACTORY")]
           public string CutterFactory { get; set; }
           /// <summary>
           /// Desc:刀具料号
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="MATERIAL_CODE")]
           public string MaterialCode { get; set; }
           /// <summary>
           /// Desc:平均百件产品需要数量
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="AVG_QUANTITY")]
           public decimal? AvgQuantity { get; set; }
           /// <summary>
           /// Desc:需要数量
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="QUANTITY")]
           public int Quantity { get; set; }
           /// <summary>
           /// Desc:替代刀具组
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="ALT_GROUP")]
           public string AltGroup { get; set; }
           /// <summary>
           /// Desc:主替代关系
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="ALT_ITEM")]
           public string AltItem { get; set; }
           /// <summary>
           /// Desc:备注
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="REMARK")]
           public string Remark { get; set; }
           /// <summary>
           /// Desc:是否删除
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DELETED")]
           public int Deleted { get; set; }

    }
}