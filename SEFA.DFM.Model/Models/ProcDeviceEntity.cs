using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>

    [SugarTable("DFM_M_PROC_DEVICE")]
    public class ProcDeviceEntity : EntityBase
    {
        public ProcDeviceEntity()
        {
        }
        /// <summary>
        /// Desc:工艺资源ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "RES_ID")]
        public string ResId { get; set; }


        /// <summary>
        /// Desc:工艺路线明细ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "ROUTING_DETAIL_ID")]
        public string RoutingDetailId { get; set; }
        /// <summary>
        /// Desc:设备ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "EQUIP_ID")]
        public string EquipId { get; set; }
        /// <summary>
        /// Desc:设备编码
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "EQUIP_CODE")]
        public string EquipCode { get; set; }
        /// <summary>
        /// Desc:设备名称
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "EQUIP_NAME")]
        public string EquipName { get; set; }
        /// <summary>
        /// Desc:工时基准
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "TIMB")]
        public string Timb { get; set; }
        /// <summary>
        /// Desc:运行机器
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "RUNM")]
        public int? Runm { get; set; }
        /// <summary>
        /// Desc:运行人工
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "RUNL")]
        public int? Runl { get; set; }
        /// <summary>
        /// Desc:设置人工
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SETL")]
        public int? Setl { get; set; }
        /// <summary>
        /// Desc:搬运小时数
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "MOVD")]
        public int? Movd { get; set; }
        /// <summary>
        /// Desc:排队小时数
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "QUED")]
        public int? Qued { get; set; }
        /// <summary>
        /// Desc:是否删除
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "DELETED")]
        public int Deleted { get; set; }

    }

}