using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("DFM_M_PROC_EQUIPMENT_MAPPING")] 
    public class ProcEquipmentMappingEntity : EntityBase
    {
        public ProcEquipmentMappingEntity()
        {
        }
           /// <summary>
           /// Desc:工艺路线ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="PROC_ID")]
        public string ProcId { get; set; }
           /// <summary>
           /// Desc:物理模型ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="EQUIPMENT_ID")]
        public string EquipmentId { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DELETED")]
        public int Deleted { get; set; }

    }
}