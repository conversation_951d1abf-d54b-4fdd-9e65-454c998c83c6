using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>

    [SugarTable("DFM_M_PROC_MATERIAL")]
    public class ProcMaterialEntity : EntityBase
    {
        public ProcMaterialEntity()
        {
        }
        /// <summary>
        /// Desc:产品物料编号
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "PRODUCT_MATERIAL_CODE")]
        public string ProductMaterialCode { get; set; }
        /// <summary>
        /// Desc:工艺路径明细ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "RES_ID")]
        public string ResId { get; set; }
        /// <summary>
        /// Desc:工艺路径明细ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "ROUTING_DETAIL_ID")]
        public string RoutingDetailId { get; set; }

        /// <summary>
        /// Desc:工序基础信息ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "PROC_ID")]
        public string ProcId { get; set; }
        /// <summary>
        /// Desc:工序编号
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "PROC_CODE")]
        public string ProcCode { get; set; }
        /// <summary>
        /// Desc:投入产出
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "INOUT")]
        public string Inout { get; set; }
        /// <summary>
        /// Desc:物料编号
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "MATERIAL_CODE")]
        public string MaterialCode { get; set; }


        /// <summary>
        /// Desc:物料编号
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "MATERIAL_NAME")]
        public string MaterialName { get; set; }

        /// <summary>
        /// Desc:父级编码
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "PARENT_CODE")]
        public string ParentCode { get; set; }

        /// <summary>
        /// Desc:父级名称
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "PARENT_NAME")]
        public string ParentName { get; set; }
        /// <summary>
        /// Desc:父级数量
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "PARENT_QUANTITY")]
        public decimal? ParentQuantity { get; set; }

        /// <summary>
        /// Desc:父级单位
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "PARENT_UNIT")]
        public string ParentUnit { get; set; }
        /// <summary>
        /// Desc:数量
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "QUANTITY")]
        public decimal? Quantity { get; set; }
        /// <summary>
        /// Desc:单位
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "UNIT")]
        public string Unit { get; set; }
        /// <summary>
        /// Desc:转换率
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "CONVERSIONRATE")]
        public string Conversionrate { get; set; }
        /// <summary>
        /// Desc:备注
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "REMARK")]
        public string Remark { get; set; }
        /// <summary>
        /// Desc:删除标志
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "DELETED")]
        public int Deleted { get; set; }

    }
}