using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>

    [SugarTable("DFM_M_PROC_PARAM")]
    public class ProcParamEntity : EntityBase
    {
        public ProcParamEntity()
        {
        }

        /// <summary>
        /// Desc:工艺资源ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "RES_ID")]
        public string Resid { get; set; }
        /// <summary>
        /// Desc:工艺详情ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "DETAILD_ID")]
        public string Detaildid { get; set; }
        /// <summary>
        /// Desc:工艺参数编码
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "CODE")]
        public string Code { get; set; }
        /// <summary>
        /// Desc:工艺参数名称
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "NAME")]
        public string Name { get; set; }
        /// <summary>
        /// Desc:标准值
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "STANDARD")]
        public string Standard { get; set; }
        /// <summary>
        /// Desc:上限
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "UPPER_LIMIT")]
        public string UpperLimit { get; set; }
        /// <summary>
        /// Desc:下限
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "LOWER_LIMIT")]
        public string LowerLimit { get; set; }
        /// <summary>
        /// Desc:上上限
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "UPPER_UPPER_LIMIT")]
        public string UpperUpperLimit { get; set; }
        /// <summary>
        /// Desc:下下限
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "LOWER_LOWER_LIMIT")]
        public string LowerLowerLimit { get; set; }
        /// <summary>
        /// Desc:单位
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "UOM")]
        public string Uom { get; set; }
        /// <summary>
        /// Desc:排序
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SORT")]
        public int SORT { get; set; }
        /// <summary>
        /// Desc:是否可用
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "ENABLED")]
        public int Enabled { get; set; }
        /// <summary>
        /// Desc:创建用户
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "CREATEUSERNAME")]
        public string Createusername { get; set; }
        /// <summary>
        /// Desc:更新用户
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "MODIFYUSERNAME")]
        public string Modifyusername { get; set; }
        /// <summary>
        /// Desc:
        /// Default:0
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "DELETED")]
        public int Deleted { get; set; }
    }
}