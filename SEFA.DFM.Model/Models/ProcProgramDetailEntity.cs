using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("DFM_M_PROC_PROGRAM_DETAIL")] 
    public class ProcProgramDetailEntity : EntityBase
    {
           public ProcProgramDetailEntity()
           {
           }
           /// <summary>
           /// Desc:工艺路线明细ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="ROUTING_DETAIL_ID")]
           public string RoutingDetailId { get; set; }
           /// <summary>
           /// Desc:父表ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="PROGRAM_ID")]
           public string ProgramId { get; set; }
           /// <summary>
           /// Desc:备注
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="REMARK")]
           public string Remark { get; set; }
           /// <summary>
           /// Desc:
           /// Default:0
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DELETED")]
           public int Deleted { get; set; }
    }
}