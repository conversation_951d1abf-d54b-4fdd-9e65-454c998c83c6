using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("DFM_M_PROC_PROGRAM")] 
    public class ProcProgramEntity : EntityBase
    {
           public ProcProgramEntity()
           {
           }
           /// <summary>
           /// Desc:工厂
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="FACTORY")]
           public string Factory { get; set; }
           /// <summary>
           /// Desc:程序编号
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="CODE")]
           public string Code { get; set; }
           /// <summary>
           /// Desc:程序名称
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="NAME")]
           public string Name { get; set; }
           /// <summary>
           /// Desc:版本
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="VERSION")]
           public string Version { get; set; }
           /// <summary>
           /// Desc:程序文件名称
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="FILE_NAME")]
           public string FileName { get; set; }
           /// <summary>
           /// Desc:程序格式
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="FILE_FORMAT")]
           public string FileFormat { get; set; }
           /// <summary>
           /// Desc:程序路径
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="FILE_ADDRESS")]
           public string FileAddress { get; set; }
           /// <summary>
           /// Desc:预定生效日期
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="STAR")]
           public DateTime? Star { get; set; }
           /// <summary>
           /// Desc:设备类型
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="DEVICE_TYPE")]
           public string DeviceType { get; set; }
           /// <summary>
           /// Desc:特定设备代码
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="DEVICE_CODE")]
           public string DeviceCode { get; set; }
           /// <summary>
           /// Desc:备注
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="REMARK")]
           public string Remark { get; set; }
           /// <summary>
           /// Desc:扩展字段1
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="RESERVE01")]
           public string Reserve01 { get; set; }
           /// <summary>
           /// Desc:扩展字段2
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="RESERVE02")]
           public string Reserve02 { get; set; }
           /// <summary>
           /// Desc:扩展字段3
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="RESERVE03")]
           public string Reserve03 { get; set; }
           /// <summary>
           /// Desc:扩展字段4
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="RESERVE04")]
           public string Reserve04 { get; set; }
           /// <summary>
           /// Desc:扩展字段5
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="RESERVE05")]
           public string Reserve05 { get; set; }
           /// <summary>
           /// Desc:扩展字段6
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="RESERVE06")]
           public string Reserve06 { get; set; }
           /// <summary>
           /// Desc:扩展字段7
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="RESERVE07")]
           public string Reserve07 { get; set; }
           /// <summary>
           /// Desc:扩展字段8
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="RESERVE08")]
           public string Reserve08 { get; set; }
           /// <summary>
           /// Desc:扩展字段9
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="RESERVE09")]
           public string Reserve09 { get; set; }
           /// <summary>
           /// Desc:扩展字段10
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="RESERVE10")]
           public string Reserve10 { get; set; }
           /// <summary>
           /// Desc:
           /// Default:0
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DELETED")]
           public int Deleted { get; set; }
    }
}