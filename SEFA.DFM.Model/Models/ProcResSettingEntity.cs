using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("DFM_M_PROC_RES_SETTING")] 
    public class ProcResSettingEntity : EntityBase
    {
        public ProcResSettingEntity()
        {
        }
           /// <summary>
           /// Desc:工序类型编码
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="PROC_TYPE_CODE")]
        public string ProcTypeCode { get; set; }
           /// <summary>
           /// Desc:工序类型名称
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="PROC_TYPE_NAME")]
        public string ProcTypeName { get; set; }
           /// <summary>
           /// Desc:工序资源编码
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="PROC_RES_CODE")]
        public string ProcResCode { get; set; }
           /// <summary>
           /// Desc:工序资源名称
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="PROC_RES_NAME")]
        public string ProcResName { get; set; }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="IS_MOUNT")]
        public string IsMount { get; set; }
           /// <summary>
           /// Desc:描述
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="DESCRIPTION")]
        public string Description { get; set; }
           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DELETED")]
        public int Deleted { get; set; }

    }
}