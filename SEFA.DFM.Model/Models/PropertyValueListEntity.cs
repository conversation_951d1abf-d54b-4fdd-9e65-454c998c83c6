using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("DFM_M_PROPERTY_VALUE_LIST")] 
    public class PropertyValueListEntity : EntityBase
    {
        public PropertyValueListEntity()
        {
        }
           /// <summary>
           /// Desc:属性ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="PROPERTY_ID")]
        public string PropertyId { get; set; }
           /// <summary>
           /// Desc:分类类型
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="ITEM_VALUE")]
        public string ItemValue { get; set; }
           /// <summary>
           /// Desc:分类编码
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="DESCRIPTION")]
        public string Description { get; set; }
           /// <summary>
           /// Desc:分类名称
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="SORT")]
        public int Sort { get; set; }
           /// <summary>
           /// Desc:是否删除（0未删除，1已删除）
           /// Default:0
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DELETED")]
        public int Deleted { get; set; }

    }
}