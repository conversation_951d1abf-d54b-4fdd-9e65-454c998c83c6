using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("DFM_M_RACKING_BIN")] 
    public class RackingBinEntity : EntityBase
    {
           public RackingBinEntity()
           {
           }
           /// <summary>
           /// Desc:线边货架ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="RACKING_ID")]
           public string RackingId { get; set; }
           /// <summary>
           /// Desc:货架位代码
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="BIN_CODE")]
           public string BinCode { get; set; }
           /// <summary>
           /// Desc:货架位名称
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="BIN_NAME")]
           public string BinName { get; set; }
           /// <summary>
           /// Desc:货架位位置描述
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="BIN_DESCRIPTION")]
           public string BinDescription { get; set; }
           /// <summary>
           /// Desc:货架位类型(工装/夹具/刀具/辅料)
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="BIN_TYPE")]
           public string BinType { get; set; }
           /// <summary>
           /// Desc:有效标志
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="STATUS")]
           public string Status { get; set; }
           /// <summary>
           /// Desc:备注
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="REMARK")]
           public string Remark { get; set; }
           /// <summary>
           /// Desc:
           /// Default:0
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DELETED")]
           public int Deleted { get; set; }
    }
}