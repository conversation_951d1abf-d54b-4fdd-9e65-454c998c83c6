using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("DFM_M_RACKING_BIN_MATERIAL")] 
    public class RackingBinMaterialEntity : EntityBase
    {
           public RackingBinMaterialEntity()
           {
           }
           /// <summary>
           /// Desc:线边货架位ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="BIN_ID")]
           public string BinId { get; set; }
           /// <summary>
           /// Desc:物料代码
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="MATERIAL_CODE")]
           public string MaterialCode { get; set; }
           /// <summary>
           /// Desc:有效标志
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="STATUS")]
           public string Status { get; set; }
           /// <summary>
           /// Desc:备注
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="REMARK")]
           public string Remark { get; set; }
           /// <summary>
           /// Desc:
           /// Default:0
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DELETED")]
           public int Deleted { get; set; }
    }
}