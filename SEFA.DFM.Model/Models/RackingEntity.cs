using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("DFM_M_RACKING")] 
    public class RackingEntity : EntityBase
    {
           public RackingEntity()
           {
           }
           /// <summary>
           /// Desc:工厂
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="FACTORY")]
           public string Factory { get; set; }
           /// <summary>
           /// Desc:货架代码
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="RACKING_CODE")]
           public string RackingCode { get; set; }
           /// <summary>
           /// Desc:货架名称
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="RACKING_NAME")]
           public string RackingName { get; set; }
           /// <summary>
           /// Desc:货架位置描述
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="DESCRIPTION")]
           public string Description { get; set; }
           /// <summary>
           /// Desc:有效标志
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="STATUS")]
           public string Status { get; set; }
           /// <summary>
           /// Desc:备注
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="REMARK")]
           public string Remark { get; set; }
           /// <summary>
           /// Desc:
           /// Default:0
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DELETED")]
           public int Deleted { get; set; }
    }
}