using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("DFM_M_MATERIAL_GROUP_MAPPING")] 
    public class MaterialGroupMappingEntity : EntityBase
    {
        public MaterialGroupMappingEntity()
        {
        }
           /// <summary>
           /// Desc:物料组ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="MATERIAL_GROUP_ID")]
        public string MaterialGroupId { get; set; }
           /// <summary>
           /// Desc:物料ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="MATERIAL_ID")]
        public string MaterialId { get; set; }
           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DELETED")]
        public int Deleted { get; set; }

    }
}