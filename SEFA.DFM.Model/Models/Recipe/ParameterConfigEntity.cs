using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>

    [SugarTable("DFM_M_PARAMETER_CONFIG")]
    public class ParameterConfigEntity : EntityBase
    {
        public ParameterConfigEntity () {
        }
        /// <summary>
        /// Desc:参数ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "PARAMETER_DEFINITION_ID")]
        public string ParameterDefinitionId { get; set; }
        /// <summary>
        /// Desc:配方ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "RECIPE_ID")]
        public string RecipeId { get; set; }
        /// <summary>
        /// Desc:配方版块ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "RECIPE_SECTION_ID")]
        public string RecipeSectionId { get; set; }
        /// <summary>
        /// Desc:配方版块版本ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "RECIPE_SECTION_VERSION_ID")]
        public string RecipeSectionVersionId { get; set; }
        /// <summary>
        /// Desc:配方内容ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "RECIPE_CONTEXT_ID")]
        public string RecipeContextId { get; set; }
        /// <summary>
        /// Desc:配方内容版本ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "RECIPE_CONTEXT_VERSION_ID")]
        public string RecipeContextVersionId { get; set; }
        /// <summary>
        /// Desc:默认值
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "DEFAULT_VALUE")]
        public string DefaultValue { get; set; }
        /// <summary>
        /// Desc:目标值
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "TARGET")]
        public decimal? Target { get; set; }
        /// <summary>
        /// Desc:文本目标值
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "TARGET_TEXT")]
        public string TargetText { get; set; }
        /// <summary>
        /// Desc:最大值
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "MAX_VALUE")]
        public decimal MaxValue { get; set; }
        /// <summary>
        /// Desc:最小值
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "MIN_VALUE")]
        public decimal? MinValue { get; set; }
        /// <summary>
        /// Desc:设备ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "EQUIPMENT_ID")]
        public string EquipmentId { get; set; }
        /// <summary>
        /// Desc:状态（启用，禁用）
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "STATUS")]
        public string Status { get; set; }
        /// <summary>
        /// Desc:删除标识
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "DELETED")]
        public int Deleted { get; set; }

    }
}