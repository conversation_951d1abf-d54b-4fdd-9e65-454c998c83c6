using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using System.ComponentModel.DataAnnotations.Schema;

namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>

    [SugarTable("DFM_M_PARAMETER_DEFINITION")]
    public class ParameterDefinitionEntity : EntityBase
    {
        public ParameterDefinitionEntity () {
        }
        /// <summary>
        /// Desc:配方ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "RECIPE_ID")]
        public string RecipeId { get; set; }
        /// <summary>
        /// Desc:配方版块ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "RECIPE_SECTION_ID")]
        public string RecipeSectionId { get; set; }
        /// <summary>
        /// Desc:配方版块版本ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "RECIPE_SECTION_VERSION_ID")]
        public string RecipeSectionVersionId { get; set; }
        /// <summary>
        /// Desc:参数组ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "PARAMETER_GROUP_ID")]
        public string ParameterGroupId { get; set; }
        /// <summary>
        /// Desc:参数名称
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "PARAMETER_NAME")]
        public string ParameterName { get; set; }
        /// <summary>
        /// Desc:参数简称
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SHORT_NAME")]
        public string ShortName { get; set; }
        /// <summary>
        /// Desc:参数类型
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "PARAMETER_TYPE")]
        public string ParameterType { get; set; }
        /// <summary>
        /// Desc:数据类型
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "DATA_TYPE")]

        public string DataType { get; set; }

        /// <summary>
        /// Desc:小数位
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "PRECISION")]

        public int? Precision { get; set; }
        /// <summary>
        /// Desc:计量单位ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "UOM_ID")]
        public string UomId { get; set; }
        /// <summary>
        /// Desc:显示格式
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "DISPLAY_FORMAT")]
        public string DisplayFormat { get; set; }
        /// <summary>
        /// Desc:排序号
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "SORT_ORDER")]
        public int SortOrder { get; set; }
        /// <summary>
        /// Desc:状态（启用，禁用）
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "STATUS")]
        public string Status { get; set; }
        /// <summary>
        /// Desc:数采或对接的influxdb点位
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "TAG")]
        public string Tag { get; set; }

        /// <summary>
        /// Desc:是否自动采集
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "IS_AUTO_COLLECTION")]
        public string IsAutoCollection { get; set; } = "0";
        /// <summary>
        /// Desc:点位类型
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "TAG_TYPE")]
        public string TagType { get; set; }

        /// <summary>
        /// Desc:是否为数采点位 1：是 0：否
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "IS_SCADA_TAG")]
        public string IsScadaTag { get; set; } = "0";
        /// <summary>
        /// Desc:删除标识
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "DELETED")]
        public int Deleted { get; set; }
    }
}