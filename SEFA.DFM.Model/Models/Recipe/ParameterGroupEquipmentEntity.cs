using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using System.ComponentModel.DataAnnotations.Schema;

namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>

    [SugarTable("DFM_M_PARAMETER_GROUP_EQUIPMENT")]
    public class ParameterGroupEquipmentEntity : EntityBase
    {
        public ParameterGroupEquipmentEntity () {
        }
        /// <summary>
        /// Desc:配方ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "RECIPE_ID")]
        public string RecipeId { get; set; }
        /// <summary>
        /// Desc:配方版块ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "RECIPE_SECTION_ID")]
        public string RecipeSectionId { get; set; }
        /// <summary>
        /// Desc:配方版块版本ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "RECIPE_SECTION_VERSION_ID")]
        public string RecipeSectionVersionId { get; set; }
        /// <summary>
        /// Desc:参数组ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "PARAMETER_GROUP_ID")]
        public string ParameterGroupId { get; set; }
        /// <summary>
        /// Desc:设备ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "EQUIPMENT_ID")]
        public string EquipmentId { get; set; }
        /// <summary>
        /// Desc:采集点位ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "ENABLE_TAG_ID")]
        public string EnableTagId { get; set; }
        /// <summary>
        /// Desc:采集频率ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "FREQUENCY_TAG_ID")]
        public string FrequencyTagId { get; set; }
        /// <summary>
        /// Desc:SCANNABLED_ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "SCANNABLED_ID")]
        public string ScannabledId { get; set; }
        /// <summary>
        /// Desc:自动触发
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "AUTO_TRIGGER")]
        public string AutoTrigger { get; set; }
        /// <summary>
        /// Desc:OPC_FUNCTION_INSTANCE_ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "OPC_FUNCTION_INSTANCE_ID")]
        public string OpcFunctionInstanceId { get; set; }
        /// <summary>
        /// Desc:删除标识
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "DELETED")]
        public int Deleted { get; set; }


    }
}