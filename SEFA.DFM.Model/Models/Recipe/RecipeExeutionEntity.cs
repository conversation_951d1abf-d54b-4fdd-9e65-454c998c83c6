using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>

    [SugarTable("DFM_M_RECIPE_EXEUTION")]
    public class RecipeExeutionEntity : EntityBase
    {
        public RecipeExeutionEntity () {
        }
        /// <summary>
        /// Desc:设备ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "EQUIPMENT_ID")]
        public string EquipmentId { get; set; }
        /// <summary>
        /// Desc:工单ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "PRODUCTION_ORDER_ID")]
        public string ProductionOrderId { get; set; }
        /// <summary>
        /// Desc:配方ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "RECIPE_ID")]
        public string RecipeId { get; set; }
        /// <summary>
        /// Desc:配方版块ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "RECIPE_SECTION_ID")]
        public string RecipeSectionId { get; set; }
        /// <summary>
        /// Desc:配方版块版本ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "RECIPE_SECTION_VERSION_ID")]
        public string RecipeSectionVersionId { get; set; }
        /// <summary>
        /// Desc:配方内容ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "RECIPE_CONTEXT_ID")]
        public string RecipeContextId { get; set; }
        /// <summary>
        /// Desc:配方内容版本ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "RECIPE_CONTEXT_VERSION_ID")]
        public string RecipeContextVersionId { get; set; }
        /// <summary>
        /// Desc:参数类型
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "CONTEXT_TYPE")]
        public int ContextType { get; set; }
        /// <summary>
        /// Desc:删除标识
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "DELETED")]
        public int Deleted { get; set; } = 0;
       
    }
}