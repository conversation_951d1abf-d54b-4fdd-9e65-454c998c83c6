using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("DFM_M_RECIPE_MAPPING")] 
    public class RecipeMappingEntity : EntityBase
    {
        public RecipeMappingEntity()
        {
        }
           /// <summary>
           /// Desc:配方ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="RECIPE_ID")]
        public string RecipeId { get; set; }
           /// <summary>
           /// Desc:产品组ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="MATERIAL_GROUP_ID")]
        public string MaterialGroupId { get; set; }
           /// <summary>
           /// Desc:产品ID或编码
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="MATERIAL_ID")]
        public string MaterialId { get; set; }
           /// <summary>
           /// Desc:产品版本ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="MATERIAL_VERSION_ID")]
        public string MaterialVersionId { get; set; }
      
        /// <summary>
        /// Desc:删除标识
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName="DELETED")]
        public int Deleted { get; set; }

    }
}