using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("DFM_M_ROLE_EQUIPMENT_GROUP")] 
    public class RoleEquipmentGroupEntity : EntityBase
    {
        public RoleEquipmentGroupEntity()
        {
        }
           /// <summary>
           /// Desc:角色ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="ROLE_ID")]
        public string RoleId { get; set; }
           /// <summary>
           /// Desc:设备组ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="EQUIPMENT_GROUP_ID")]
        public string EquipmentGroupId { get; set; }

    }
}