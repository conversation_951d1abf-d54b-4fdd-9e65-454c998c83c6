using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("BASE_M_ROLE_PERMISSION")] 
    public class RolePermissionEntity : EntityBase
    {
        public RolePermissionEntity()
        {
        }
           /// <summary>
           /// Desc:角色ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="ROLE_ID")]
        public string RoleId { get; set; }
           /// <summary>
           /// Desc:功能ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="MOUDULE_ID")]
        public string MouduleId { get; set; }
           /// <summary>
           /// Desc:删除标识
           /// Default:0
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DELETED")]
        public int Deleted { get; set; }

    }
}