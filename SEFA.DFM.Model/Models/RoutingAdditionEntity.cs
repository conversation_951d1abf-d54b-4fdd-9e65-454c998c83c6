using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("DFM_M_ROUTING_ADDITION")] 
    public class RoutingAdditionEntity : EntityBase
    {
           public RoutingAdditionEntity()
           {
           }
           /// <summary>
           /// Desc:工厂号（分部）
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="FACTORY")]
           public string Factory { get; set; }
           /// <summary>
           /// Desc:成品料号
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="MATERIAL_CODE")]
           public string MaterialCode { get; set; }
           /// <summary>
           /// Desc:成品料号版本
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="MATERIAL_VERSION")]
           public string MaterialVersion { get; set; }
           /// <summary>
           /// Desc:工艺类型
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="ROUTING_TYPE")]
           public string RoutingType { get; set; }
           /// <summary>
           /// Desc:工艺文件
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="NEED_DATA")]
           public string NeedData { get; set; }
           /// <summary>
           /// Desc:工序类型
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="PROC_TYPE")]
           public string ProcType { get; set; }
           /// <summary>
           /// Desc:扩展字段1
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="RESERVE01")]
           public string Reserve01 { get; set; }
           /// <summary>
           /// Desc:扩展字段2
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="RESERVE02")]
           public string Reserve02 { get; set; }
           /// <summary>
           /// Desc:扩展字段3
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="RESERVE03")]
           public string Reserve03 { get; set; }
           /// <summary>
           /// Desc:扩展字段4
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="RESERVE04")]
           public string Reserve04 { get; set; }
           /// <summary>
           /// Desc:扩展字段5
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="RESERVE05")]
           public string Reserve05 { get; set; }
           /// <summary>
           /// Desc:扩展字段6
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="RESERVE06")]
           public string Reserve06 { get; set; }
           /// <summary>
           /// Desc:扩展字段7
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="RESERVE07")]
           public string Reserve07 { get; set; }
           /// <summary>
           /// Desc:扩展字段8
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="RESERVE08")]
           public string Reserve08 { get; set; }
           /// <summary>
           /// Desc:扩展字段9
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="RESERVE09")]
           public string Reserve09 { get; set; }
           /// <summary>
           /// Desc:扩展字段10
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="RESERVE10")]
           public string Reserve10 { get; set; }
           /// <summary>
           /// Desc:
           /// Default:0
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DELETED")]
           public int Deleted { get; set; }
    }
}