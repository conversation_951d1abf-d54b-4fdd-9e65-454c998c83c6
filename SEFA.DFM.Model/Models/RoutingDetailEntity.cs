using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>

    [SugarTable("DFM_M_ROUTING_DETAIL")]
    public class RoutingDetailEntity : EntityBase
    {
        public RoutingDetailEntity()
        {
        }
        /// <summary>
        /// Desc:工艺路线
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "ROUTING_ID")]
        public string RoutingId { get; set; }
        /// <summary>
        /// Desc:工厂号（分部）
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "FACTORY")]
        public string Factory { get; set; }
        /// <summary>
        /// Desc:工作中心
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "WORK_CENTER")]
        public string WorkCenter { get; set; }
        /// <summary>
        /// Desc:工序基础ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "PROC_ID")]
        public string ProcId { get; set; }

        /// <summary>
        /// Desc:工序编码
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "PROC_CODE")]
        public string ProcCode { get; set; }
        /// <summary>
        /// Desc:工序名称
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "PROC_NAME")]
        public string ProcName { get; set; }
        /// <summary>
        /// Desc:版本
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "VERSION")]
        public string Version { get; set; }
        /// <summary>
        /// Desc:类型
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "PROC_TYPE")]
        public string ProcType { get; set; }
        /// <summary>
        /// Desc:经营单位
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "UNIT")]
        public string Unit { get; set; }
        /// <summary>
        /// Desc:生效自
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "EFFECT_START")]
        public DateTime? EffectStart { get; set; }
        /// <summary>
        /// Desc:生效至
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "EFFECT_END")]
        public DateTime? EffectEnd { get; set; }
        /// <summary>
        /// Desc:工时基准
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "TIMB")]
        public string Timb { get; set; }
        /// <summary>
        /// Desc:运行机器
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "RUNM")]
        public string Runm { get; set; }
        /// <summary>
        /// Desc:运行人工
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "RUNL")]
        public int? Runl { get; set; }
        /// <summary>
        /// Desc:设置人工
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SETL")]
        public int? Setl { get; set; }
        /// <summary>
        /// Desc:搬运小时数
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "MOVD")]
        public decimal? Movd { get; set; }
        /// <summary>
        /// Desc:排队小时数
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "QUED")]
        public decimal? Qued { get; set; }
        /// <summary>
        /// Desc:转序时间
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "MOVE_TIME")]
        public decimal? MoveTime { get; set; }
        /// <summary>
        /// Desc:成本类型
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "COSTCATEGORY")]
        public string Costcategory { get; set; }
        /// <summary>
        /// Desc:工作类型
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "JBCD")]
        public string Jbcd { get; set; }
        /// <summary>
        /// Desc:描述
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "DESCRIPTION")]
        public string Description { get; set; }
        /// <summary>
        /// Desc:备注
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "NOTES")]
        public string Notes { get; set; }
        /// <summary>
        /// Desc:状态
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "STATUS")]
        public string Status { get; set; }
        /// <summary>
        /// Desc:扩展字段1
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "RESERVE01")]
        public string Reserve01 { get; set; }
        /// <summary>
        /// Desc:扩展字段2
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "RESERVE02")]
        public string Reserve02 { get; set; }
        /// <summary>
        /// Desc:扩展字段3
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "RESERVE03")]
        public string Reserve03 { get; set; }
        /// <summary>
        /// Desc:扩展字段4
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "GROUP_ID")]
        public string GroupId { get; set; }
        /// <summary>
        /// Desc:是否挂载资源
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "IS_MOUNT_SOURCE")]
        public string IsMountSource { get; set; }
        /// <summary>
        /// Desc:能否跳站
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "IS_SKIP")]
        public string IsSkip { get; set; }
        /// <summary>
        /// Desc:图形坐标
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "COORDINATE")]
        public string Coordinate { get; set; }
        /// <summary>
        /// Desc:宽
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "WIDTH")]
        public string Width { get; set; }
        /// <summary>
        /// Desc:高
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "HEIGHT")]
        public string Height { get; set; }
        /// <summary>
        /// Desc:删除标识
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "DELETED")]
        public int Deleted { get; set; }

        /// <summary>
        /// Desc:是否创建批次
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "IS_CREATE_BATCH")]
        public int IsCreateBatch { get; set; }

        /// <summary>
        /// Desc:是否创建工单
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "IS_CREATE_ORDER")]
        public int IsCreateOrder { get; set; }

    }
}