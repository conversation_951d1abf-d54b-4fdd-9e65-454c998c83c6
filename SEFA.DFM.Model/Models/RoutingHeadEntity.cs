using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>

    [SugarTable("DFM_M_ROUTING_HEAD")]
    public class RoutingHeadEntity : EntityBase
    {
        public RoutingHeadEntity()
        {
        }
        /// <summary>
        /// Desc:工厂号（分部）
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "FACTORY")]
        public string Factory { get; set; }
        /// <summary>
        /// Desc:工艺路线编码
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "ROUTING_CODE")]
        public string RoutingCode { get; set; }
        /// <summary>
        /// Desc:工艺路线名称
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "ROUTING_NAME")]
        public string RoutingName { get; set; }
        /// <summary>
        /// Desc:版本
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "VERSION")]
        public string Version { get; set; }
        /// <summary>
        /// Desc:工艺类型
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "ROUTING_TYPE")]
        public string RoutingType { get; set; }
        /// <summary>
        /// Desc:生效自
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "EFFECT_START")]
        public DateTime? EffectStart { get; set; }
        /// <summary>
        /// Desc:生效至
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "EFFECT_END")]
        public DateTime? EffectEnd { get; set; }
        /// <summary>
        /// Desc:描述
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "DESCRIPTION")]
        public string Description { get; set; }
        /// <summary>
        /// Desc:备注
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "NOTES")]
        public string Notes { get; set; }
        /// <summary>
        /// Desc:状态
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "STATUS")]
        public string Status { get; set; }
        /// <summary>
        /// Desc:扩展字段1
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "RESERVE01")]
        public string Reserve01 { get; set; }
        /// <summary>
        /// Desc:扩展字段2
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "RESERVE02")]
        public string Reserve02 { get; set; }
        /// <summary>
        /// Desc:扩展字段3
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "RESERVE03")]
        public string Reserve03 { get; set; }
        /// <summary>
        /// Desc:扩展字段4
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "RESERVE04")]
        public string Reserve04 { get; set; }
        /// <summary>
        /// Desc:扩展字段5
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "RESERVE05")]
        public string Reserve05 { get; set; }
        /// <summary>
        /// Desc:扩展字段6
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "RESERVE06")]
        public string Reserve06 { get; set; }
        /// <summary>
        /// Desc:扩展字段7
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "RESERVE07")]
        public string Reserve07 { get; set; }
        /// <summary>
        /// Desc:扩展字段8
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "RESERVE08")]
        public string Reserve08 { get; set; }
        /// <summary>
        /// Desc:扩展字段9
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "RESERVE09")]
        public string Reserve09 { get; set; }
        /// <summary>
        /// Desc:扩展字段10
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "RESERVE10")]
        public string Reserve10 { get; set; }
        /// <summary>
        /// Desc:删除标识
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "DELETED")]
        public int Deleted { get; set; }
        /// <summary>
        /// Desc:删除标识
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "LEVEL")]
        public string Level { get; set; }
        /// <summary>
        /// Desc:类型
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "TYPE_CODE")]
        public string TypeCode { get; set; }
        /// <summary>
        /// Desc:类型
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "TYPE_NAME")]
        public string TypeName { get; set; }
    }
}