using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("DFM_M_ROUTING_PATH")] 
    public class RoutingPathEntity : EntityBase
    {
           public RoutingPathEntity()
           {
           }
           /// <summary>
           /// Desc:工艺路线明细ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="ROUTING_DETAIL_ID")]
           public string RoutingDetailId { get; set; }
           /// <summary>
           /// Desc:工艺路线明细前序ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="MOVE_PROC_ID")]
           public string MoveProcId { get; set; }
           /// <summary>
           /// Desc:进入条件
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="MOVE_IN_CONDITION")]
           public string MoveInCondition { get; set; }
           /// <summary>
           /// Desc:进入本工序类型
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="MOVE_IN_PROC_TYPE")]
           public string MoveInProcType { get; set; }
           /// <summary>
           /// Desc:
           /// Default:0
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DELETED")]
           public int Deleted { get; set; }
    }
}