using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>

    [SugarTable("PTM_B_BATCH")]
    public class BatchEntity : EntityBase
    {
        public BatchEntity()
        {
        }
        /// <summary>
        /// Desc:班组ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "TEAM_ID")]
        public string TeamId { get; set; }
        /// <summary>
        /// Desc:班组
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "TEAM_NAME")]
        public string TeamName { get; set; }
        /// <summary>
        /// Desc:班次ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SHIFT_ID")]
        public string ShiftId { get; set; }
        /// <summary>
        /// Desc:班次
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SHIFT_NAME")]
        public string ShiftName { get; set; }
        /// <summary>
        /// Desc:工厂ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "FACTORY_ID")]
        public string FactoryId { get; set; }
        /// <summary>
        /// Desc:工厂名称
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "FACTORY_NAME")]
        public string FactoryName { get; set; }
        /// <summary>
        /// Desc:产线ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "COMPANY_ID")]
        public string CompanyId { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "COMPANY_NAME")]
        public string CompanyName { get; set; }


        /// <summary>
        /// Desc:工艺路线中工站对应的ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "CURRENT_ROUTING_PROC_ID")]
        public string CurrentRoutingProcId { get; set; }
        /// <summary>
        /// Desc:工站ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "CURRENT_PROC_ID")]
        public string CurrentProcId { get; set; }

        /// <summary>
        /// Desc:工站Code
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "CURRENT_PROC_CODE")]
        public string CurrentProcCode { get; set; }
        /// <summary>
        /// Desc:工站名称
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "CURRENT_PROC_NAME")]
        public string CurrentProcName { get; set; }
        /// <summary>
        /// Desc:设备ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "EQUIPMENT_CODE")]
        public string EquipmentCode { get; set; }
        /// <summary>
        /// Desc:设备名称
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "EQUIPMENT_NAME")]
        public string EquipmentName { get; set; }
        /// <summary>
        /// Desc:物料编码
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "MATERIAL_CODE")]
        public string MaterialCode { get; set; }
        /// <summary>
        /// Desc:物料描述
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "MATERIAL_DESCRIPTION")]
        public string MaterialDescription { get; set; }
        /// <summary>
        /// Desc:批次
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "BATCH_NO")]
        public string BatchNo { get; set; }
        /// <summary>
        /// Desc:批次数量
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "BATCH_QUANTITY")]
        public decimal? BatchQuantity { get; set; }
        /// <summary>
        /// Desc:剩余数量
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "REMAIN_QUANTITY")]
        public decimal? RemainQuantity { get; set; }
        /// <summary>
        /// Desc:批次类型
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "BATCH_TYPE")]
        public int? BatchType { get; set; }
        /// <summary>
        /// Desc:使用状态
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "USE_STATUS")]
        public int? UseStatus { get; set; }
        /// <summary>
        /// Desc:工单号
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "WO_CODE")]
        public string WoCode { get; set; }
        /// <summary>
        /// Desc:批次当前所在工序生产状态
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "CURRENT_PROC_STATUS")]
        public int? CurrentProcStatus { get; set; }

        /// <summary>
        /// Desc:已删除
        /// Default:0
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "DELETED")]
        public int Deleted { get; set; }

        /// <summary>
        /// Desc:工单ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "WO_ID")]
        public string WoId { get; set; }

        /// <summary>
        /// Desc:测试等级
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "TEST_GRADE")]
        public string TestGrade { get; set; }

        /// <summary>
        /// Desc:项目名称
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "PROJECT_NAME")]
        public string ProjectName { get; set; }
        /// <summary>
        /// Desc:整线名称
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "FULL_LINE_NAME")]
        public string FullLineName { get; set; }

        /// <summary>
        /// Desc:工段ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SECTION_ID")]
        public string SectionId
        {
            get; set;
        }
        /// <summary>
        /// Desc:工段编码
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SECTION_CODE")]
        public string SectionCode
        {
            get; set;
        }
        /// <summary>
        /// Desc:工段名称
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SECTION_NAME")]
        public string SectionName
        {
            get; set;
        }
        /// <summary>
        /// Desc:阶段
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "STAGE")]
        public string Stage { get; set; }

        /// <summary>
        /// Desc:工作日
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "WORK_DATE")]
        public DateTime? WorkDate { get; set; }

        /// <summary>
        /// Desc:虚拟入库箱号
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "VIRTUAL_BOX_NUMBER")]
        public string VirtualBoxNumber { get; set; }

        /// <summary>
        /// Desc:创建批次类型 Scada/Person
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "CREATE_BATCH_TYPE")]
        public string CreateBatchType { get; set; }

    }
}