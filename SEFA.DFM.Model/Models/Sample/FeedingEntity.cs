using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("PTM_B_FEEDING")] 
    public class FeedingEntity : EntityBase
    {
        public FeedingEntity()
        {
        }
           /// <summary>
           /// Desc:班组ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="TEAM_ID")]
        public string TeamId { get; set; }
           /// <summary>
           /// Desc:班组
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="TEAM_NAME")]
        public string TeamName { get; set; }
           /// <summary>
           /// Desc:班次ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="SHIFT_ID")]
        public string ShiftId { get; set; }
           /// <summary>
           /// Desc:班次
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="SHIFT_NAME")]
        public string ShiftName { get; set; }
           /// <summary>
           /// Desc:工厂ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="FACTORY_ID")]
        public string FactoryId { get; set; }
           /// <summary>
           /// Desc:工厂名称
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="FACTORY_NAME")]
        public string FactoryName { get; set; }
           /// <summary>
           /// Desc:产线ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="COMPANY_ID")]
        public string CompanyId { get; set; }
        /// <summary>
        /// Desc:产线名称
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "COMPANY_NAME")]
        public string CompanyName { get; set; }
        /// <summary>
        /// Desc:工段ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName="SECTION_ID")]
        public string SectionId { get; set; }
        /// <summary>
        /// Desc:工段ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SECTION_CODE")]
        public string SectionCode { get; set; }
        /// <summary>
        /// Desc:工段名称
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName="SECTION_NAME")]
        public string SectionName { get; set; }
           /// <summary>
           /// Desc:工站ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="PROC_ID")]
        public string ProcId { get; set; }
           /// <summary>
           /// Desc:工站名称
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="PROC_NAME")]
        public string ProcName { get; set; }
           /// <summary>
           /// Desc:设备ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="EQUIPMENT_CODE")]
        public string EquipmentCode { get; set; }
           /// <summary>
           /// Desc:设备名称
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="EQUIPMENT_NAME")]
        public string EquipmentName { get; set; }
           /// <summary>
           /// Desc:物料编码
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="MATERIAL_CODE")]
        public string MaterialCode { get; set; }
           /// <summary>
           /// Desc:物料描述
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="MATERIAL_DESCRIPTION")]
        public string MaterialDescription { get; set; }
           /// <summary>
           /// Desc:物料批次
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="MATERIAL_BATCH_NO")]
        public string MaterialBatchNo { get; set; }
        /// <summary>
        /// Desc:物料批次
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "MATERIAL_PRODUCT_BATCH_NO")]
        public string MaterialProductBatchNo { get; set; }
        /// <summary>
        /// Desc:批次数量
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName="BATCH_QUANTITY")]
        public decimal? BatchQuantity { get; set; }
           /// <summary>
           /// Desc:剩余数量
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="REMAIN_QUANTITY")]
        public decimal? RemainQuantity { get; set; }

        /// <summary>
        /// Desc:单位
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "FEED_UNIT")]
        public string FeedUnit { get; set; }

        /// <summary>
        /// Desc:基数
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "BASE_QUANTITY")]
        public decimal? BaseQuantity { get; set; }
        /// <summary>
        /// Desc:单位用量
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "UNIT_CONSUMPTION")]
        public decimal? UnitConsumption { get; set; }

        /// <summary>
        /// Desc:使用状态
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName="STATUS")]
        public int? Status { get; set; }
           /// <summary>
           /// Desc:工单号
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="WO_CODE")]
        public string WoCode { get; set; }
           /// <summary>
           /// Desc:已删除
           /// Default:0
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DELETED")]
        public int Deleted { get; set; }

        /// <summary>
        /// Desc:条码
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "BARCODE")]
        public string BarCode { get; set; }

        /// <summary>
        /// Desc:整线名称
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "FULL_LINE_NAME")]
        public string FullLineName { get; set; }

        /// <summary>
        /// Desc:整线名称
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "MAIN_MATERIAL_CODE")]
        public string MainMaterialCode { get; set; }
        
    }
}