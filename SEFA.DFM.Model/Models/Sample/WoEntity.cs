using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>

    [SugarTable("PTM_B_WO")]
    public class WoEntity : EntityBase
    {
        public WoEntity()
        {
        }
        /// <summary>
        /// Desc:RESID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "RES_ID")]
        public string ResId
        {
            get; set;
        }
        /// <summary>
        /// Desc:SAP订单号
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SAP_WO_CODE")]
        public string SapWoCode
        {
            get; set;
        }
        /// <summary>
        /// Desc:工单号
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "WO_CODE")]
        public string WoCode
        {
            get; set;
        }
        /// <summary>
        /// Desc:班组ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "TEAM_ID")]
        public string TeamId
        {
            get; set;
        }
        /// <summary>
        /// Desc:班组
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "TEAM_NAME")]
        public string TeamName
        {
            get; set;
        }
        /// <summary>
        /// Desc:班次ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SHIFT_ID")]
        public string ShiftId
        {
            get; set;
        }
        /// <summary>
        /// Desc:班次
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SHIFT_NAME")]
        public string ShiftName
        {
            get; set;
        }
        /// <summary>
        /// Desc:工厂ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "FACTORY_ID")]
        public string FactoryId
        {
            get; set;
        }
        /// <summary>
        /// Desc:工厂编码
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "FACTORY_CODE")]
        public string FactoryCode
        {
            get; set;
        }
        /// <summary>
        /// Desc:工厂名称
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "FACTORY_NAME")]
        public string FactoryName
        {
            get; set;
        }
        /// <summary>
        /// Desc:产线ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "COMPANY_ID")]
        public string CompanyId
        {
            get; set;
        }
        /// <summary>
        /// Desc:产线编码
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "COMPANY_CODE")]
        public string CompanyCode
        {
            get; set;
        }
        /// <summary>
        /// Desc:产线名称
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "COMPANY_NAME")]
        public string CompanyName
        {
            get; set;
        }
        /// <summary>
        /// Desc:工段ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SECTION_ID")]
        public string SectionId
        {
            get; set;
        }
        /// <summary>
        /// Desc:工段编码
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SECTION_CODE")]
        public string SectionCode
        {
            get; set;
        }
        /// <summary>
        /// Desc:工段名称
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SECTION_NAME")]
        public string SectionName
        {
            get; set;
        }
        /// <summary>
        /// Desc:工单计划数量
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "WO_QUANTITY")]
        public decimal WoQuantity
        {
            get; set;
        }
        /// <summary>
        /// Desc:总成批数量
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "WO_SUM_BATCH_QUANTITY")]
        public decimal? WoSumBatchQuantity
        {
            get; set;
        }
        /// <summary>
        /// Desc:确认数量
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "WO_CONFIRM_BATCH_QUANTITY")]
        public decimal? WoConfirmBatchQuantity
        {
            get; set;
        }
        /// <summary>
        /// Desc:最终完成数量
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "WO_COMPLETE_QUANTITY")]
        public decimal? WoCompleteQuantity
        {
            get; set;
        }
        /// <summary>
        /// Desc:工单不良数量
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "WO_BAD_QUANTITY")]
        public decimal? WoBadQuantity
        {
            get; set;
        }
        /// <summary>
        /// Desc:默认成批数量
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "DEFAULT_BATCH_QTY")]
        public decimal? DefaultBatchQty
        {
            get; set;
        }
        /// <summary>
        /// Desc:工单状态
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "WO_STATUS")]
        public int? WoStatus
        {
            get; set;
        }
        /// <summary>
        /// Desc:计划开始时间
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "PLAN_START_TIME")]
        public DateTime? PlanStartTime
        {
            get; set;
        }
        /// <summary>
        /// Desc:计划结束时间
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "PLAN_END_TIME")]
        public DateTime? PlanEndTime
        {
            get; set;
        }
        /// <summary>
        /// Desc:实际开始时间
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "ACTUAL_START_TIME")]
        public DateTime? ActualStartTime
        {
            get; set;
        }
        /// <summary>
        /// Desc:实际结束时间
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "ACTUAL_END_TIME")]
        public DateTime? ActualEndTime
        {
            get; set;
        }
        /// <summary>
        /// Desc:工艺路线ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "ROUTING_ID")]
        public string RoutingId
        {
            get; set;
        }
        /// <summary>
        /// Desc:工艺路线编码
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "ROUTING_CODE")]
        public string RoutingCode
        {
            get; set;
        }
        /// <summary>
        /// Desc:工艺路线名称
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "ROUTING_NAME")]
        public string RoutingName
        {
            get; set;
        }

        /// <summary>
        /// Desc:物料编码
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "MATERIAL_CODE")]
        public string MaterialCode
        {
            get; set;
        }
        /// <summary>
        /// Desc:物料描述
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "MATERIAL_DESCRIPTION")]
        public string MaterialDescription
        {
            get; set;
        }
        /// <summary>
        /// Desc:项目名称
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "PROJECT_NAME")]
        public string ProjectName
        {
            get; set;
        }
        /// <summary>
        /// Desc:整线名称
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "FULL_LINE_NAME")]
        public string FullLineName
        {
            get; set;
        }
        /// <summary>
        /// Desc:原因
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "REASON")]
        public string Reason
        {
            get; set;
        }
        /// <summary>
        /// Desc:计划日期
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "PLAN_DATE")]
        public DateTime PlanDate
        {
            get; set;
        }
        /// <summary>
        /// Desc:工段简称
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "ROUTING_SHORT_NAME")]
        public string RoutingShortName
        {
            get; set;
        }


        /// <summary>
        /// Desc:
        /// Default:0
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "DELETED")]
        public int Deleted
        {
            get; set;
        }

        /// <summary>
        /// Desc:
        /// Default:0
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "LOCK_STATUS")]
        public int? LockStatus
        {
            get; set;
        } = 0;

        /// <summary>
        /// Desc:设备工时
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "MACHINE_HOURS")]
        public decimal? MachineHours { get; set; }

        /// <summary>
        /// Desc:人员工时
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "PERSON_HOURS")]
        public decimal? PersonHours { get; set; }

        /// <summary>
        /// Desc:人员工时
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "STAGE")]
        public string Stage { get; set; }

        /// <summary>
        /// Desc:SAP订单料号
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SAP_WO_PRODUCTION_CODE")]
        public string SapWoProductionCode { get; set; }


        /// <summary>
        /// Desc:工单类型
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "WO_TYPE")]
        public string WoType { get; set; }

        /// <summary>
        /// Desc:SAP报工标识
        /// Default:0 未发送
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SEND_SAP_FLAG")]
        public int SendSapFlag { get; set; } = 0;

        /// <summary>
        /// Desc:数采产量
        /// Default:0
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "SCADA_QTY")]
        public int ScadaQty
        {
            get; set;
        }

        /// <summary>
        /// Desc:过账日期
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "BOOK_DATE")]
        public DateTime? BookDate { get; set; }

        /// <summary>
        /// Desc:工单启动类型
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "WO_START_TYPE")]
        public string WoStartType { get; set; }

        /// <summary>
        /// Desc:工单结束类型
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "WO_STOP_TYPE")]
        public string WoStopType { get; set; }
    }
}