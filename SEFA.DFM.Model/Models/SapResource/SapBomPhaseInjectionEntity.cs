using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("DFM_M_SAP_BOM_PHASE_INJECTION")] 
    public class SapBomPhaseInjectionEntity : EntityBase
    {
        public SapBomPhaseInjectionEntity()
        {
        }
           /// <summary>
           /// Desc:物料组ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="MATERIAL_GROUP_ID")]
        public string MaterialGroupId { get; set; }
           /// <summary>
           /// Desc:物料ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="MATERIAL_ID")]
        public string MaterialId { get; set; }
           /// <summary>
           /// Desc:物料版本ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="MATERIAL_VERSION_ID")]
        public string MaterialVersionId { get; set; }
           /// <summary>
           /// Desc:产出工序
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="PRODUCT_SAP_SEGMENT_ID")]
        public string ProductSapSegmentId { get; set; }
           /// <summary>
           /// Desc:消耗工序
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="CONSUME_SAP_SEGMENT_ID")]
        public string ConsumeSapSegmentId { get; set; }
           /// <summary>
           /// Desc:产出物料ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="PRODUCT_MATERIAL_ID")]
        public string ProductMaterialId { get; set; }
           /// <summary>
           /// Desc:类型
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="TYPE")]
        public int Type { get; set; }
           /// <summary>
           /// Desc:利用率
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="PERCENT_QUANTITY")]
        public decimal? PercentQuantity { get; set; }
           /// <summary>
           /// Desc:消耗数量
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="CONSUME_QUANTITY")]
        public decimal? ConsumeQuantity { get; set; }
           /// <summary>
           /// Desc:消耗单位
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="CONSUME_UNIT_ID")]
        public string ConsumeUnitId { get; set; }
           /// <summary>
           /// Desc:优先级
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="PRIORITY")]
        public int? Priority { get; set; }
           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DELETED")]
        public int Deleted { get; set; }

    }
}