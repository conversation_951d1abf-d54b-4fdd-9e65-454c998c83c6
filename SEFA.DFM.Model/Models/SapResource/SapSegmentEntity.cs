using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>

    [SugarTable("DFM_M_SAP_SEGMENT")]
    public class SapSegmentEntity : EntityBase
    {
        public SapSegmentEntity () {
        }
        /// <summary>
        /// Desc:SAP资源ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SAP_EQUIPMENT_ID")]
        public string SapEquipmentId { get; set; }
        /// <summary>
        /// Desc:编码
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "SEGMENT_CODE")]
        public string SegmentCode { get; set; }
        /// <summary>
        /// Desc:名称
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "SEGMENT_NAME")]
        public string SegmentName { get; set; }
        /// <summary>
        /// Desc:父级ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "PARENT_ID")]
        public string ParentId { get; set; }
        /// <summary>
        /// Desc:层级
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "LEVEL")]
        public int Level { get; set; }
        /// <summary>
        /// Desc:备注
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "REMARK")]
        public string Remark { get; set; }
        /// <summary>
        /// Desc:删除标识
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "DELETED")]
        public int Deleted { get; set; }

    }
}