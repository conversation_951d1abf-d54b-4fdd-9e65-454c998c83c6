using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("DFM_M_SEGMENT_PROPERTY")] 
    public class SegmentPropertyEntity : EntityBase
    {
        public SegmentPropertyEntity()
        {
        }
           /// <summary>
           /// Desc:工序
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="SAP_SEGMENT_ID")]
        public string SapSegmentId { get; set; }
           /// <summary>
           /// Desc:属性名
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="CODE")]
        public string Code { get; set; }
           /// <summary>
           /// Desc:描述
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="DESCRIPTION")]
        public string Description { get; set; }
           /// <summary>
           /// Desc:默认值
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="DEFAULT_VALUE")]
        public string DefaultValue { get; set; }

    }
}