using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("DFM_M_SEGMENT_PROPERTY_V")] 
    public class SegmentPropertyVEntity : EntityBase
    {
        public SegmentPropertyVEntity()
        {
        }
           /// <summary>
           /// Desc:属性ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="PROPERTITY_ID")]
        public string PropertityId { get; set; }
           /// <summary>
           /// Desc:工序
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="SAP_SEGMENT_ID")]
        public string SapSegmentId { get; set; }
           /// <summary>
           /// Desc:值
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="VALUE")]
        public string Value { get; set; }

    }
}