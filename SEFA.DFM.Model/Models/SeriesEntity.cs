using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("DFM_M_SERIES")] 
    public class SeriesEntity : EntityBase
    {
           public SeriesEntity()
           {
           }
           /// <summary>
           /// Desc:编号
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="CODE")]
           public string Code { get; set; }
           /// <summary>
           /// Desc:名称
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="NAME")]
           public string Name { get; set; }
           /// <summary>
           /// Desc:厂别
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="PLANT")]
           public string Plant { get; set; }
           /// <summary>
           /// Desc:制程
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="PROCESSCODE")]
           public string Processcode { get; set; }
           /// <summary>
           /// Desc:描述
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DESCRIPTION")]
           public string Description { get; set; }
           /// <summary>
           /// Desc:有效标识
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="ENABLED")]
           public int Enabled { get; set; }
           /// <summary>
           /// Desc:删除标志
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DELETED")]
           public int Deleted { get; set; }

    }
}