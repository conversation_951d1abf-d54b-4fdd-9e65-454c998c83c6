using System;
using System.Linq;
using System.Text;
using SqlSugar;
using SEFA.Base.Model.BASE;

namespace SEFA.DFM.Model.Models
{
    public class Series_CategoryEntity : EntityBase
    {
        /// <summary>
        /// 系列ID
        /// </summary>
        [SugarColumn(ColumnName = "SERIESID")]
        public string SERIESID { get; set; }
        /// <summary>
        /// 机种ID
        /// </summary>
        [SugarColumn(ColumnName = "CATEGORYID")]
        public string CATEGORYID { get; set; }
        public SeriesEntity Series { get; set; }
        public CategoryEntity Category { get; set; }
    }
}
