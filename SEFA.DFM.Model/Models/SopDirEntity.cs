using System;
using System.Linq;
using System.Text;
using SqlSugar;
using SEFA.Base.Model.BASE;

namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///SOP文档目录树结构表
    ///</summary>
    [SugarTable("DFM_B_SOP_DIR")]
    public class SopDirEntity : EntityBase
    {
        public SopDirEntity()
        {
        }

        /// <summary>
        /// Desc:父目录ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "PARENT_ID")]
        public string ParentId { get; set; }

        /// <summary>
        /// Desc:目录名称
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "DIR_NAME")]
        public string DirName { get; set; }

        /// <summary>
        /// Desc:目录编码(唯一)
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "DIR_CODE")]
        public string DirCode { get; set; }

        /// <summary>
        /// Desc:目录负责人ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "OWNER_USERID")]
        public string OwnerUserid { get; set; }

        /// <summary>
        /// Desc:删除标记(0-未删 1-已删)
        /// Default:0
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "DELETED")]
        public int Deleted { get; set; } = 0;
    }
}