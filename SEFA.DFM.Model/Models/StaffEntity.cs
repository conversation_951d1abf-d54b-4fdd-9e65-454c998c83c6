using SEFA.Base.Model.BASE;
using SqlSugar;
using System;

namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///员工表
    ///</summary>
    [SugarTable("DFM_M_STAFF")]
    public class StaffEntity : EntityBase
    {
        public StaffEntity()
        {
        }
        /// <summary>
        /// Desc:名称
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "NAME")]
        public string Name { get; set; }
        /// <summary>
        /// Desc:工号
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "CODE")]
        public string Code { get; set; }

        /// <summary>
        /// Desc:员工子组
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "USER_AVATAR")]
        public string UserAvatar { get; set; }


        /// <summary>
        /// Desc:人事子范围
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "REGION")]
        public string Region { get; set; }
        /// <summary>
        /// Desc:员工子组
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "STAFF_GROUP")]
        public string StaffGroup { get; set; }
        /// <summary>
        /// Desc:厂区
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "FACTORY")]
        public string Factory { get; set; }
        /// <summary>
        /// Desc:业务类型描述
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "BUSINESS_DESC")]
        public string BusinessDesc { get; set; }
        /// <summary>
        /// Desc:车间描述
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "WORKSHOP_DESC")]
        public string WorkshopDesc { get; set; }
        /// <summary>
        /// Desc:虚拟组织
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "VIRTUAL_ORGANIZATION")]
        public string VirtualOrganization { get; set; }
        /// <summary>
        /// Desc:上级领导
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "LEADER_NAME")]
        public string LeaderName { get; set; }
        /// <summary>
        /// Desc:上级领导工号
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "LEADER_CODE")]
        public string LeaderCode { get; set; }
        /// <summary>
        /// Desc:UWB标签号
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "UWB")]
        public string Uwb { get; set; }
        /// <summary>
        /// Desc:职位
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "RANK")]
        public string Rank { get; set; }
        /// <summary>
        /// Desc:职称
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "JOB_TITLE")]
        public string JobTitle { get; set; }
        /// <summary>
        /// Desc:星级
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "STAR_LEVEL")]
        public string StarLevel { get; set; }


        /// <summary>
        /// Desc:星级
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "FEISHU")]
        public string Feishu { get; set; }



        /// <summary>
        /// 维修成本单价
        /// </summary>
        [SugarColumn(ColumnName = "PRICE")]
        public decimal Price { get; set; }


        /// <summary>
        /// 维修成本单价
        /// </summary>
        [SugarColumn(ColumnName = "START_VALID_TIME")]
        public DateTime? StartValidTime { get; set; }


        /// <summary>
        /// 维修成本单价
        /// </summary>
        [SugarColumn(ColumnName = "END_VALID_TIME")]
        public DateTime? EndValidTime { get; set; }


        /// <summary>
        /// Desc:是否删除
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "DELETED")]
        public int Deleted { get; set; }


    }
}