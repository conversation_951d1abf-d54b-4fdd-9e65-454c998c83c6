using SEFA.Base.Model.BASE;
using SEFA.DFM.Model;
using SqlSugar;
using System;

namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///人员排班表
    ///</summary>

    [SugarTable("DFM_M_STAFF_SCHEDULING")]
    public class StaffSchedulingEntity : EntityBase
    {
        public StaffSchedulingEntity()
        {
        }
        /// <summary>
        /// Desc:员工号
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "STAFF_NAME")]
        public string StaffName { get; set; }
        /// <summary>
        /// Desc:员工名
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "STAFF_CODE")]
        public string StaffCode { get; set; }
        /// <summary>
        /// Desc:员工UWB
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "STAFF_UWB")]
        public string StaffUwb { get; set; }
        /// <summary>
        /// Desc:区域编号
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "GEONUM")]
        public string Geonum { get; set; }
        /// <summary>
        /// Desc:产线
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "LINE")]
        public string Line { get; set; }
        /// <summary>
        /// Desc:产线ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "LINE_ID")]
        public string LineId { get; set; }
        /// <summary>
        /// Desc:产线CODE
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "LINE_CODE")]
        public string LineCode { get; set; }
        /// <summary>
        /// Desc:工段
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SEGMENT")]
        public string Segment { get; set; }
        /// <summary>
        /// Desc:工段ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SEGMENT_ID")]
        public string SegmentId { get; set; }
        /// <summary>
        /// Desc:工段简称
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SEGMENT_SHORT_NAME")]
        public string SegmentShortName { get; set; }
        /// <summary>
        /// Desc:工序
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "PROCESS")]
        public string Process { get; set; }
        /// <summary>
        /// Desc:班组
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "TEAM")]
        public string Team { get; set; }
        /// <summary>
        /// Desc:班组ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "TEAM_ID")]
        public string TeamId { get; set; }
        /// <summary>
        /// Desc:班次
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SHIFT")]
        public string Shift { get; set; }
        /// <summary>
        /// Desc:班次ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SHIFT_ID")]
        public string ShiftId { get; set; }

        /// <summary>
        /// Desc:uwb 工时
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "WORK_TIMES")]
        public decimal? WorkTimes { get; set; }

        /// <summary>
        /// 确认工时
        /// </summary>
        [SugarColumn(ColumnName = "CONFIRM_TIMES")]
        public decimal? ConfirmTimes { get; set; }


        /// <summary>
        /// UWB_考勤工时
        /// </summary>
        [SugarColumn(ColumnName = "UWB_TIMES")]
        public decimal? UwbWorkTimes { get; set; }

        /// <summary>
        /// Desc:考勤工时
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "HR_TIMES")]
        public decimal? HrTimes { get; set; }

        /// <summary>
        /// Desc:加班工时
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "OVER_TIMES")]
        public decimal? OverTimes { get; set; }


        /// <summary>
        /// Desc:上班 打卡时间
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "CLOCK_IN_TIME")]
        public DateTime? ClockInTime { get; set; }

        /// <summary>
        /// Desc:下班 打卡时间
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "CLOCK_OFF_TIME")]
        public DateTime? ClockOffTime { get; set; }

        /// <summary>
        /// Desc:休息时长
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "REST_TIMES")]
        public decimal? RestTimes { get; set; }


        /// <summary>
        /// Desc:状态
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "STATE")]
        public StaffSchedulingState State { get; set; }
        /// <summary>
        /// Desc:计件薪资
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "PIECEWORK_WAGE")]
        public decimal? PieceworkWage { get; set; }
        /// <summary>
        /// Desc:超产薪资
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "BONUS")]
        public decimal? Bonus { get; set; }
        /// <summary>
        /// Desc:产量
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "YIELD")]
        public decimal? Yield { get; set; }
        /// <summary>
        /// Desc:日期
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "DATE")]
        public DateTime? Date { get; set; }
        /// <summary>
        /// Desc:计件岗位
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "PIECEWORK_POST")]
        public string PieceworkPost { get; set; }
        /// <summary>
        /// Desc:安灯岗位
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "ANDON_POST")]
        public string AndonPost { get; set; }
        /// <summary>
        /// Desc:职称
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "JOB_TITLE")]
        public string JobTitle { get; set; }
        /// <summary>
        /// Desc:小时基准产量
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "HOURLY_OUTPUT")]
        public decimal? HourlyOutput { get; set; }



        /// <summary>
        /// 人员分类
        /// </summary>
        [SugarColumn(ColumnName = "TYPE")]
        public string Type { get; set; }

        /// <summary>
        /// 类型
        /// </summary>
        [SugarColumn(ColumnName = "TYPE2")]
        public string Type2 { get; set; }

        /// <summary>
        /// 类别
        /// </summary>
        [SugarColumn(ColumnName = "TYPE3")]
        public string Type3 { get; set; }

        /// <summary>
        ///  来源 考勤：借调
        /// </summary>
        [SugarColumn(ColumnName = "SOURCE")]
        public string Source { get; set; }


        /// <summary>
        /// 物料号
        /// </summary>
        [SugarColumn(ColumnName = "MATERIAL_CODE")]
        public string MaterialCode { get; set; }


        /// <summary>
        /// Desc:安灯岗位职责范围
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "ANDON_POST_RANGE")]
        public string AndonPostRange { get; set; }

        /// <summary>
        /// Desc:是否删除
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "DELETED")]
        public int Deleted { get; set; }



        ///// <summary>
        ///// 班组ID 拆分
        ///// </summary>
        //public List<string> TeamIds { get { return this.TeamId==null?new List<string>(): this.TeamId.Split(",").ToList(); } }



    }
}