using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("DFM_M_TURNMODE")] 
    public class TurnmodeEntity : EntityBase
    {
           public TurnmodeEntity()
           {
           }
           /// <summary>
           /// Desc:名称
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="NAME")]
           public string Name { get; set; }
           /// <summary>
           /// Desc:班组数
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="TEAMS")]
           public int? Teams { get; set; }
           /// <summary>
           /// Desc:班次数
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="SHIFTS")]
           public int? Shifts { get; set; }
           /// <summary>
           /// Desc:序号
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="SEQUENCE")]
           public int? Sequence { get; set; }
           /// <summary>
           /// Desc:启用标识
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="ENABLEDMARK")]
           public int? Enabledmark { get; set; }
           /// <summary>
           /// Desc:描述
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="DESCRIPTION")]
           public string Description { get; set; }
           /// <summary>
           /// Desc:
           /// Default:0
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DELETED")]
           public int Deleted { get; set; }
    }
}