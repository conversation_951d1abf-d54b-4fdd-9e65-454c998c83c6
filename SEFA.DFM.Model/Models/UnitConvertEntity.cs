using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("DFM_M_UNIT_CONVERT")] 
    public class UnitConvertEntity : EntityBase
    {
        public UnitConvertEntity()
        {
        }
           /// <summary>
           /// Desc:物料编码
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="MATERIAL_CODE")]
        public string MaterialCode { get; set; }
           /// <summary>
           /// Desc:源单位ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="FORM_UNIT_ID")]
        public string FormUnitId { get; set; }
           /// <summary>
           /// Desc:源单位名称
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="FORM_UNIT_NAME")]
        public string FormUnitName { get; set; }
           /// <summary>
           /// Desc:转换单位ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="TO_UNIT_ID")]
        public string ToUnitId { get; set; }
           /// <summary>
           /// Desc:转换单位名称
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="TO_UNIT_NAME")]
        public string ToUnitName { get; set; }
           /// <summary>
           /// Desc:源单位数值
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="CONVERT_FORM_QTY")]
        public decimal? ConvertFormQty { get; set; }
           /// <summary>
           /// Desc:转化单位数值
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="CONVERT_TO_QTY")]
        public decimal? ConvertToQty { get; set; }
           /// <summary>
           /// Desc:生效开始时间
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="EFFECTIVE_BEGIN_DATE")]
        public DateTime? EffectiveBeginDate { get; set; }
           /// <summary>
           /// Desc:生效结束时间
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="EFFECTIVE_END_DATE")]
        public DateTime? EffectiveEndDate { get; set; }
           /// <summary>
           /// Desc:备注
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="REMARK")]
        public string Remark { get; set; }
           /// <summary>
           /// Desc:
           /// Default:0
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DELETED")]
        public int Deleted { get; set; }
    }
}