using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    
    [SugarTable("DFM_M_UNIT")] 
    public class UnitEntity : EntityBase
    {
           public UnitEntity()
           {
           }
           /// <summary>
           /// Desc:单位一（比较大的单位）
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="UNIT1")]
           public string Unit1 { get; set; }
           /// <summary>
           /// Desc:数值1
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="VALUE1")]
           public decimal? Value1 { get; set; }
           /// <summary>
           /// Desc:单位二（比较小的单位）
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="UNIT2")]
           public string Unit2 { get; set; }
           /// <summary>
           /// Desc:数值2
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="VALUE2")]
           public decimal? Value2 { get; set; }
           /// <summary>
           /// Desc:描述
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="DESCRIPTION")]
           public string Description { get; set; }
           /// <summary>
           /// Desc:
           /// Default:0
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="DELETED")]
           public int Deleted { get; set; }
    }
}