using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>

    [SugarTable("BASE_M_USER_ROLE")]
    public class UserRoleEntity : EntityBase
    {
        public UserRoleEntity()
        {
        }
        /// <summary>
        /// Desc:角色ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "ROLE_ID")]
        public string RoleId { get; set; }
        /// <summary>
        /// Desc:用户ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "USER_ID")]
        public string UserId { get; set; }
        /// <summary>
        /// Desc:删除标识
        /// Default:0
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "DELETED")]
        public int Deleted { get; set; }

        ///// <summary>
        ///// 用户名称
        ///// </summary>
        //public string UserName { get; set; }


    }
}