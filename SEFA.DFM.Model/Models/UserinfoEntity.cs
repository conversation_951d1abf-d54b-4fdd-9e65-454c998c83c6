using SEFA.Base.Model.BASE;
using SqlSugar;
using System;

namespace SEFA.DFM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    [SugarTable("BASE_M_USERINFO")]
    public class UserinfoEntity : EntityBase
    {
        public UserinfoEntity()
        {
        }


        /// <summary>
        /// Desc:登录名
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "LOGIN_NAME")]
        public string LoginName { get; set; }

        /// <summary>
        /// Desc:密码
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "PASSWORD")]
        public string Password { get; set; }

        /// <summary>
        /// Desc:员工号
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "USER_NO")]
        public string UserNo { get; set; }

        /// <summary>
        /// Desc:用户姓名
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "USER_NAME")]
        public string UserName { get; set; }

        /// <summary>
        /// Desc:状态(0禁用，1启用)
        /// Default:1
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "STATUS")]
        public int? Status { get; set; }

        /// <summary>
        /// Desc:备注
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "REMARK")]
        public string Remark { get; set; }

        /// <summary>
        /// Desc:性别
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SEX")]
        public int? Sex { get; set; }

        /// <summary>
        /// Desc:年龄
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "AGE")]
        public int? Age { get; set; }

        /// <summary>
        /// Desc:生日
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "BIRTH")]
        public string Birth { get; set; }

        /// <summary>
        /// Desc:公司ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "COMPANYID")]
        public string Companyid { get; set; }

        /// <summary>
        /// Desc:部门ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "DEPARTMENTID")]
        public string Departmentid { get; set; }

        [SugarColumn(IsIgnore = true)] public string DepartmentCode { get; set; }

        /// <summary>
        /// Desc:产线ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "PRODUCTLINEID")]
        public string Productlineid { get; set; }

        /// <summary>
        /// Desc:岗位ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "POSTID")]
        public string Postid { get; set; }

        /// <summary>
        /// Desc:班组ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "TEAMID")]
        public string Teamid { get; set; }

        /// <summary>
        /// Desc:电话
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "TEL")]
        public string Tel { get; set; }

        /// <summary>
        /// Desc:微信号
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "WECHAT_NO")]
        public string WechatNo { get; set; }

        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "ERROR_COUNT")]
        public int? ErrorCount { get; set; }

        /// <summary>
        /// Desc:
        /// Default:0
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "LOCKED")]
        public int Locked { get; set; }

        /// <summary>
        /// Desc:
        /// Default:1
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "ENABLE")]
        public int Enable { get; set; }

        /// <summary>
        /// Desc:是否已删除
        /// Default:0
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "DELETED")]
        public int Deleted { get; set; }

        /// <summary>
        /// Desc:邮箱
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "EMAIL")]
        public string EMAIL { get; set; }

        /// <summary>
        /// Desc:修改密码时间
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "LAST_MODIFY_PASSWORD")]
        public DateTime? LastModifyPassword { get; set; }

        /// <summary>
        /// Desc:域用户标识：0-否，1-是
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "IS_DOMAIN_USER")]
        public string IsDomainUser { get; set; }
    }
}