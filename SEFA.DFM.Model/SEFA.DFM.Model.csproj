<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net6.0</TargetFramework>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
		<DocumentationFile>..\SEFA.DFM.Api\SEFA.DFM.Model.xml</DocumentationFile>
		<NoWarn>1701;1702;1591</NoWarn>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
		<DocumentationFile>..\SEFA.DFM\SEFA.DFM.Model.xml</DocumentationFile>
		<NoWarn>1701;1702;1591</NoWarn>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Abp" Version="7.3.0" />
		<PackageReference Include="Magicodes.IE.Excel" Version="2.6.4" />
		<PackageReference Include="Magicodes.IE.Excel.AspNetCore" Version="2.6.4" />
		<PackageReference Include="Oracle.ManagedDataAccess.Core" Version="3.21.120" />
		<PackageReference Include="sqlSugarCore" Version="5.1.4.151" />
		<PackageReference Include="AutoMapper" Version="10.1.1" />
		<PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="8.1.0" />
	</ItemGroup>

	<ItemGroup>
	  <Reference Include="SEFA.Base.Model">
	    <HintPath>..\..\common\SEFA.Base.Model.dll</HintPath>
	  </Reference>
	</ItemGroup>

</Project>
