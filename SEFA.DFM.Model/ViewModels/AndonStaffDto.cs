using SqlSugar;
using System;

namespace SEFA.DFM.Model.ViewModels
{
  
    public class AndonStaffDto
    {
        /// <summary>
        /// Desc:名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Desc:编号
        /// </summary>
        public string Code { get; set; }

        ///// <summary>
        ///// 性别
        ///// </summary>
        //public string Gender { get; set; }

        ///// <summary>
        ///// Desc:状态;0：请假；1：正常：调休：离职
        ///// </summary>
        //[SugarColumn(ColumnName = "STATE")]
        //public string State { get; set; }


        /// <summary>
        /// Desc:工厂
        /// </summary>
        public string Factory { get; set; }


        ///// <summary>
        ///// Desc:工厂
        ///// </summary>
        //public string FactoryId { get; set; }



        ///// <summary>
        ///// Desc:部门
        ///// </summary>
        //public string Departement { get; set; }


        ///// <summary>
        ///// Desc:部门
        ///// </summary>
        //public string DepartementId { get; set; }


        ///// <summary>
        ///// Desc:岗位
        ///// </summary>
        //public string JobContent { get; set; }

        ///// <summary>
        ///// Desc:岗位
        ///// </summary>
        //public string JobContentId { get; set; }


        ///// <summary>
        ///// Desc:岗位类别
        ///// </summary>
        //public string JobContentType { get; set; }

        /// <summary>
        /// Desc:职称 名称
        /// </summary>
        public string JobTitle { get; set; }


        ///// <summary>
        ///// Desc:职称
        ///// </summary>
        //public string JobTitleId { get; set; }




        /// <summary>
        /// Desc:职位
        /// </summary>
        public string Rank { get; set; }

        ///// <summary>
        ///// Desc:职位ID
        ///// </summary>
        //public string RankId { get; set; }



        /// <summary>
        /// Desc:星级
        /// </summary>
        public string StarLevel { get; set; }

        ///// <summary>
        ///// Desc:班组
        ///// </summary>
        //public string ShiftGroupId { get; set; }


        ///// <summary>
        ///// Desc:工段
        ///// </summary>
        //public string WorkshopSectionId { get; set; }

        /// <summary>
        /// 飞书  
        /// </summary>
        public string Feishu { get; set; }

        /// <summary>
        /// 微信
        ///// </summary>
        //public string Wechat { get; set; }

        ///// <summary>
        ///// 手机
        ///// </summary>
        //public string Phone { get; set; }

        ///// <summary>
        ///// 邮箱账号
        ///// </summary>
        //public string Email { get; set; }

        /// <summary>
        /// UWB标签ID
        /// </summary>
        public string Uwb { get; set; }


        ///// <summary>
        ///// 入职日期
        ///// </summary>
        //public DateTime? EntryDate { get; set; }

        /// <summary>
        /// 员工头像
        /// </summary>
        public string UserAvatar { get; set; }



        ///// <summary>
        ///// 是否计件
        ///// </summary>
        //public bool IsPiecework { get; set; }


        ///// <summary>
        ///// 是否全职
        ///// </summary>
        //public bool IsFulltime { get; set; }

        ///// <summary>
        ///// 精力占比
        ///// </summary>
        //public string EnergyPercent { get; set; }

        ///// <summary>
        ///// 离职时间
        ///// </summary>
        //public DateTime? DimissionDate { get; set; }

        ///// <summary>
        ///// 离职方式
        ///// </summary>
        //public string DimissionType { get; set; }

        ///// <summary>
        ///// 备注
        ///// </summary>
        //public string Remark { get; set; }


        ///// <summary>
        ///// 上级领导ID
        ///// </summary>
        //public string LeaderId { get; set; }


        /// <summary>
        /// 上级领导名称
        /// </summary>
        public string LeaderName { get; set; }

        /// <summary>
        /// 上级领导员工编号
        /// </summary>
        public string LeaderCode { get; set; }


        ///// <summary>
        ///// 计件岗位
        ///// </summary>
        //public string PieceRatePosition { get; set; }


        ///// <summary>
        ///// Desc:是否删除
        ///// </summary>
        //public int Deleted { get; set; }


        ///// <summary>
        ///// Desc:楼层
        ///// </summary>
        //public string FactorySite { get; set; }


        ///// <summary>
        ///// Desc:楼栋
        ///// </summary>
        //public string Building { get; set; }


        ///// <summary>
        ///// Desc:业务类型
        ///// </summary>
        //public string BusinessType { get; set; }

        ///// <summary>
        ///// Desc:产线
        ///// </summary>
        //public string Line { get; set; }

        ///// <summary>
        ///// Desc:车间
        ///// </summary>
        //public string Workshop { get; set; }

        ///// <summary>
        ///// Desc:物料号
        ///// </summary>
        //public string MaterialCode { get; set; }

        ///// <summary>
        ///// Desc:物料描述
        ///// </summary>
        //public string MaterialDescribe { get; set; }

        ///// <summary>
        ///// Desc:线体
        ///// </summary>
        //public string LineEntity { get; set; }


        ///// <summary>
        ///// Desc:项目阶段
        ///// </summary>
        //public string ProjectPhase { get; set; }


        ///// <summary>
        ///// Desc:工段
        ///// </summary>
        //public string Segment { get; set; }


        ///// <summary>
        ///// Desc:班组
        ///// </summary>
        //public string ShiftGroup { get; set; }


        ///// <summary>
        ///// 员工类型
        ///// </summary>
        //public string StaffType { get; set; }


        ///// <summary>
        ///// 星级
        ///// </summary>
        //public string StarLevel { get; set; }

        ///// <summary>
        ///// 事业部
        ///// </summary>
        //public string BusinessUnit { get; set; }

        ///// <summary>
        ///// 区域
        ///// </summary>
        //public string Region { get; set; }

        ///// <summary>
        ///// 工序
        ///// </summary>
        //public string Process { get; set; }

        ///// <summary>
        ///// 班制
        ///// </summary>
        //public decimal? ShiftSystem { get; set; }

        ///// <summary>
        ///// 安灯 岗位
        ///// </summary>
        //public string AndonPost { get; set; }

        ///// <summary>
        ///// 每小时基准产能
        ///// </summary>
        //public decimal BasicOutputHours { get; set; }


        ///// <summary>
        ///// 车间描述
        ///// </summary>
        //public string WorkshopDescribe { get; set; }

        ///// <summary>
        ///// 数据来源
        ///// </summary>
        //public string DateSources { get; set; }


        ///// <summary>
        ///// 班次
        ///// </summary>
        //public string Shift { get; set;}
    }


}