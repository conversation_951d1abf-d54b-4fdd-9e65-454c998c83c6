using System;
using System.Collections.Generic;

namespace SEFA.DFM.Model.ViewModels
{

    public class AndonStaffReuqestModel
    {

        /// <summary>
        /// 产线
        /// </summary>
        public string Line { get; set; }
        /// <summary>
        /// 班组Code
        /// </summary>
        public string TeamId { get; set; }


        /// <summary>
        /// 工段
        /// </summary>
        public string Segment { get; set; }

        /// <summary>
        ///// 安灯岗位
        ///// </summary>
        public List<string> AndonPosts { get; set; }=new List<string>();


        /// <summary>
        /// 岗位职责范围
        /// </summary>
        public List<string> PostDutyRange { get; set; }
        /// <summary>
        /// 安灯岗位
        /// </summary>
        public List<AndonPostDto> AndonPostDto { get; set; } = new List<AndonPostDto>();
    }

    public class AndonPostDto
    {
        /// <summary>
        /// 安灯岗位名称
        /// </summary>
        public string PostName { get; set; }

        public string PostDutyRange { get; set; }
    }


}