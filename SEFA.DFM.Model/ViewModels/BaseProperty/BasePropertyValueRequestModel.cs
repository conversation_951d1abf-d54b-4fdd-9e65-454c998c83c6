using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.DFM.Model.ViewModels
{
    public class BasePropertyValueRequestModel : RequestPageModelBase
    {
        public BasePropertyValueRequestModel()
        {
        }
           /// <summary>
           /// Desc:属性ID
           /// Default:
           /// Nullable:False
           /// </summary>
        public string PropertyId { get; set; }
           /// <summary>
           /// Desc:业务表关联行
           /// Default:
           /// Nullable:False
           /// </summary>
        public string TableRowId { get; set; }
           /// <summary>
           /// Desc:实际值
           /// Default:
           /// Nullable:False
           /// </summary>
        public string Value { get; set; }

    }
}