using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SEFA.DFM.Model.ViewModels
{
    public enum ClassTypeEnum
    {
        /// <summary>
        /// 物理模型
        /// </summary>
        EquipmentClass,
        /// <summary>
        /// 组织架构模型
        /// </summary>
        DepartmentClass,
        /// <summary>
        /// 物料模型
        /// </summary>
        MaterialClass,
        /// <summary>
        /// 工艺模型
        /// </summary>
        ProcessClass

    }
    /// <summary>
    /// 审核状态
    /// </summary>
    public enum AuditStatus
    {
        [Description("待提交")]
        PendingSubmission = 0,
        [Description("待审核")]
        PendingApproval = 1,
        [Description("已通过")]
        Approved = 2,
        [Description("未通过")]
        Unapproved = 3,
        [Description("作废")]
        Void = 4,
    }
    public static class EnumExtensions
    {
        public static string GetDescription(this Enum val)
        {
            var field = val.GetType().GetField(val.ToString());
            var customAttribute = Attribute.GetCustomAttribute(field, typeof(DescriptionAttribute));
            return customAttribute == null ? val.ToString() : ((DescriptionAttribute)customAttribute).Description;
        }
    }
}
