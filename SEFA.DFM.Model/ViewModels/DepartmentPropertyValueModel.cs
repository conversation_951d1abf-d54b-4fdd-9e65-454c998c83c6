using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;

namespace SEFA.DFM.Model.Models
{
    public class DepartmentPropertyValueModel
    {
		/// <summary>
		/// Desc:语种
		/// Default:
		/// Nullable:False
		/// </summary>
		public string lang { get; set; }

        /// <summary>
        /// Desc:分类编码
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "ID")]
        public string ID { get; set; }
        /// <summary>
        /// Desc:分类编码
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "CLASS_CODE")]
        public string ClassCode { get; set; }
        /// <summary>
        /// Desc:分类名称
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "CLASS_NAME")]
        public string ClassName { get; set; }
        /// <summary>
        /// Desc:分类类型
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "CLASS_TYPE")]
        public string ClassType { get; set; }
        /// <summary>
        /// Desc:是否删除（0未删除，1已删除）
        /// Default:0
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "DELETED")]
        public int Deleted { get; set; }
    }
}
