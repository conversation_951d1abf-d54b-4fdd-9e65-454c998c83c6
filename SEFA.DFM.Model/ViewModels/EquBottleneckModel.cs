using SEFA.DFM.Model.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SEFA.DFM.Model.ViewModels
{
    public class EquBottleneckModel
    {
        public string ID { get; set; }
        public string Factory { get; set; }
        public string LineId { get; set; }
        public string MaterialCode { get; set; }
        public string MaterialGroupId { get; set; }
        public string Machines { get; set; }
        public DateTime? EffectiveStartDate { get; set; }
        public DateTime? EffectiveEndDate { get; set; }
        public List<EquBottleneckDetailsEntity> EquipmentList { get; set; }
    }
}
