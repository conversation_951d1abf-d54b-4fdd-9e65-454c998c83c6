using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.DFM.Model.ViewModels
{
    public class EquipmentClassAssocRequestModel : RequestPageModelBase
    {
        public EquipmentClassAssocRequestModel()
        {
        }
           /// <summary>
           /// Desc:物料批次ID
           /// Default:
           /// Nullable:False
           /// </summary>
        public string LotId { get; set; }
           /// <summary>
           /// Desc:物料子批次ID
           /// Default:
           /// Nullable:True
           /// </summary>
        public string SublotId { get; set; }
           /// <summary>
           /// Desc:数量
           /// Default:
           /// Nullable:False
           /// </summary>
        public decimal Quantity { get; set; }
           /// <summary>
           /// Desc:数量单位
           /// Default:
           /// Nullable:False
           /// </summary>
        public string QuantityUomId { get; set; }
           /// <summary>
           /// Desc:存储位置
           /// Default:
           /// Nullable:False
           /// </summary>
        public string StorageLocation { get; set; }
           /// <summary>
           /// Desc:存储区ID
           /// Default:
           /// Nullable:True
           /// </summary>
        public string EquipmentId { get; set; }
           /// <summary>
           /// Desc:是否预检查
           /// Default:
           /// Nullable:True
           /// </summary>
        public string IsPrechecked { get; set; }
           /// <summary>
           /// Desc:容器ID
           /// Default:
           /// Nullable:True
           /// </summary>
        public string ContainerId { get; set; }
           /// <summary>
           /// Desc:产品工单ID
           /// Default:
           /// Nullable:True
           /// </summary>
        public string ProductionRequestId { get; set; }
           /// <summary>
           /// Desc:顺序号
           /// Default:
           /// Nullable:True
           /// </summary>
        public int? SortOrder { get; set; }

    }
}