using SEFA.DFM.Model.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SEFA.DFM.Model.ViewModels
{
    /// <summary>
    /// 工厂模型
    /// </summary>
    public class EquipmentClassModel
    {
        ///// <summary>
        ///// 组织、产线ID
        ///// </summary>
        //public string EQUIPMENT_ID { get; set; }

        ///// <summary>
        ///// 编码
        ///// </summary>
        //public string EQUIPMENT_CODE { get; set; }

        /// <summary>
        /// 分类ID
        /// </summary>
        public string CLASS_ID { get; set; }

        /// <summary>
        /// 分类编码
        /// </summary>
        public string CLASS_CODE { get; set; }

        /// <summary>
        /// 分类名称
        /// </summary>
        public string CLASS_NAME { get; set; }

        /// <summary>
        /// 分类类型
        /// </summary>
        public string CLASS_TYPE { get; set; }

        /// <summary>
        /// 是否删除  0-有效  1-删除
        /// </summary>
        public int DELETED { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CREATEDATE { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string CREATEUSERID { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? MODIFYDATE { get; set; }

        /// <summary>
        /// 更新人
        /// </summary>
        public string MODIFYUSERID { get; set; }

        ///// <summary>
        ///// 属性ID
        ///// </summary>
        //public string PROPERTY_ID { get; set; }

        ///// <summary>
        ///// 属性值
        ///// </summary>
        //public string PROPERTY_VALUE { get; set; }
    }

    public class EquipmentPropertyValue : EquipmentPropertyValueEntity
    {
    }
}
