using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.DFM.Model.ViewModels
{
    public class EquipmentGroupEquipRequestModel : RequestPageModelBase
    {
        public EquipmentGroupEquipRequestModel()
        {
        }
        /// <summary>
        /// Desc:LineId
        /// Default:
        /// Nullable:False
        /// </summary>
        public string LineId { get; set; }
        /// <summary>
        /// Desc:Key
        /// Default:
        /// Nullable:False
        /// </summary>
        public string Key { get; set; }
        /// <summary>
        /// Desc:仓储分组节点
        /// Default:
        /// Nullable:False
        /// </summary>
        public string EquipmentGroupRowId { get; set; }
           /// <summary>
           /// Desc:仓储节点
           /// Default:
           /// Nullable:False
           /// </summary>
        public string EquipmentRowId { get; set; }
           /// <summary>
           /// Desc:X坐标
           /// Default:
           /// Nullable:True
           /// </summary>
        public string TopLeftX { get; set; }
           /// <summary>
           /// Desc:Y坐标
           /// Default:
           /// Nullable:True
           /// </summary>
        public string TopLeftY { get; set; }
           /// <summary>
           /// Desc:排序
           /// Default:
           /// Nullable:True
           /// </summary>
        public string SortOrder { get; set; }
           /// <summary>
           /// Desc:描述
           /// Default:
           /// Nullable:False
           /// </summary>
        public string Description { get; set; }
           /// <summary>
           /// Desc:状态
           /// Default:
           /// Nullable:False
           /// </summary>
        public string Status { get; set; }

    }
}