using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.DFM.Model.ViewModels
{
    public class EquipmentRequirementRequestModel : RequestPageModelBase
    {
        public EquipmentRequirementRequestModel()
        {
        }
           /// <summary>
           /// Desc:父节点
           /// Default:
           /// Nullable:True
           /// </summary>
        public string ParentId { get; set; }
           /// <summary>
           /// Desc:仓储编码
           /// Default:
           /// Nullable:False
           /// </summary>
        public string Code { get; set; }
           /// <summary>
           /// Desc:等级
           /// Default:
           /// Nullable:False
           /// </summary>
        public string Level { get; set; }
           /// <summary>
           /// Desc:是否管理库存
           /// Default:
           /// Nullable:True
           /// </summary>
        public string ManageInventory { get; set; }

    }
}