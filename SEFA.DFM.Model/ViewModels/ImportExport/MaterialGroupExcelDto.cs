using Magicodes.ExporterAndImporter.Core;
using SqlSugar;
using System;
using System.Xml.Linq;

namespace SEFA.DFM.Model.ViewModels;

/// <summary>
///  物料属性导入导出
/// </summary>
public class MaterialPropertyExcelDto
{

   
    /// <summary>
    /// Desc:物料编码
    /// Default:
    /// Nullable:False
    /// </summary>
    [ImporterHeader(Name = "物料编码")]
    [ExporterHeader(DisplayName = "物料编码")]
    public string MaterialCode { get; set; }
    /// <summary>
    /// Desc:物料名称
    /// Default:
    /// Nullable:False
    /// </summary>
    [ImporterHeader(Name = "物料名称")]
    [ExporterHeader(DisplayName = "物料名称")]
    public string MaterialName { get; set; }

    /// <summary>
    /// Desc:属性编码
    /// Default:
    /// Nullable:False
    /// </summary>
    [ImporterHeader(Name = "属性编码")]
    [ExporterHeader(DisplayName = "属性编码")]
    public string PropertyCode { get; set; }

    /// <summary>
    /// Desc:属性名称
    /// Default:
    /// Nullable:False
    /// </summary>
    [ImporterHeader(Name = "属性名称")]
    [ExporterHeader(DisplayName = "属性名称")]
    public string PropertyName { get; set; }

    /// <summary>
    /// Desc:属性值
    /// Default:
    /// Nullable:False
    /// </summary>
    [ImporterHeader(Name = "属性值")]
    [ExporterHeader(DisplayName = "属性值")]
    public string PropertyValue { get; set; }

}
