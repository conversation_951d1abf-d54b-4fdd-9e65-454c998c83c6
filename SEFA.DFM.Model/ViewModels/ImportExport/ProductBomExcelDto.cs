using Magicodes.ExporterAndImporter.Core;
using SqlSugar;
using System;
using System.Xml.Linq;

namespace SEFA.DFM.Model.ViewModels;

/// <summary>
///  产品BOM导入导出
/// </summary>
public class ProductBomExcelDto
{
    /// <summary>
    /// Desc:投料顺序
    /// Default:
    /// Nullable:False
    /// </summary>
    [ImporterHeader(Name = "投料顺序")]
    [ExporterHeader(DisplayName = "投料顺序")]
    public int? SortOrder { get; set; }
    /// <summary>
    /// Desc:产品编码
    /// Default:
    /// Nullable:False
    /// </summary>
    [ImporterHeader(Name = "产品编码")]
    [ExporterHeader(DisplayName = "产品编码")]
    public string ProductCode { get; set; }

    /// <summary>
    /// Desc:产品名称
    /// Default:
    /// Nullable:False
    /// </summary>
    [ImporterHeader(Name = "产品名称")]
    [ExporterHeader(DisplayName = "产品名称")]
    public string ProductName { get; set; }

    /// <summary>
    /// Desc:产品版本
    /// Default:
    /// Nullable:False
    /// </summary>
    [ImporterHeader(Name = "产品版本")]
    [ExporterHeader(DisplayName = "产品版本")]
    public string ProductVersion { get; set; }

    /// <summary>
    /// Desc:工序
    /// Default:
    /// Nullable:False
    /// </summary>
    [ImporterHeader(Name = "工序")]
    [ExporterHeader(DisplayName = "工序")]
    public string SegmentName { get; set; }

    /// <summary>
    /// Desc:原物料编码
    /// Default:
    /// Nullable:False
    /// </summary>
    [ImporterHeader(Name = "原物料编码")]
    [ExporterHeader(DisplayName = "原物料编码")]
    public string MaterialCode { get; set; }

    /// <summary>
    /// Desc:原物料名称
    /// Default:
    /// Nullable:False
    /// </summary>
    [ImporterHeader(Name = "原物料名称")]
    [ExporterHeader(DisplayName = "原物料名称")]
    public string MaterialName { get; set; }

    /// <summary>
    /// Desc:标准产量
    /// Default:
    /// Nullable:False
    /// </summary>
    [ImporterHeader(Name = "标准产量")]
    [ExporterHeader(DisplayName = "标准产量")] 
    public decimal ParentQuantity { get; set; }
    /// <summary>
    /// Desc:标准用量
    /// Default:
    /// Nullable:True
    /// </summary>
    [ImporterHeader(Name = "标准用量")]
    [ExporterHeader(DisplayName = "标准用量")]
    public decimal? Quantity { get; set; }

    /// <summary>
    /// Desc:预调量
    /// Default:
    /// Nullable:True
    /// </summary>
    [ImporterHeader(Name = "预调量")]
    [ExporterHeader(DisplayName = "预调量")]
    public decimal? AdjustPercentQuantity { get; set; }

    /// <summary>
    /// Desc:损耗数量
    /// Default:
    /// Nullable:True
    /// </summary>
    [ImporterHeader(Name = "带损耗量")]
    [ExporterHeader(DisplayName = "带损耗量")]
    public decimal? LossQuantity { get; set; }

    /// <summary>
    /// Desc:是否需要预处理
    /// Default:
    /// Nullable:True
    /// </summary>
    [ImporterHeader(Name = "是否需要预处理")]
    [ExporterHeader(DisplayName = "是否需要预处理")] 
    public string NeedPreprocess { get; set; }

}
