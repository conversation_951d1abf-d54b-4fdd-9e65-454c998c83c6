using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.DFM.Model.ViewModels
{
    public class LabelCountryMOverrideRequestModel : RequestPageModelBase
    {
        public LabelCountryMOverrideRequestModel()
        {
        }
        
           /// <summary>
           /// Desc:国家配置ID
           /// Default:
           /// Nullable:False
           /// </summary>
        public string CountryId { get; set; }

        public string CountryName { get; set; }
           /// <summary>
           /// Desc:月份
           /// Default:
           /// Nullable:False
           /// </summary>
        public string Month { get; set; }
           /// <summary>
           /// Desc:覆写值
           /// Default:
           /// Nullable:False
           /// </summary>
        public string OverrideText { get; set; }

        public string ID { get; set; }

        public DateTime? CreateDate { get; set; }

        public string CreateUserId { get; set; }

        public DateTime? ModifyDate { get; set; }

        public string ModifyUserId { get; set; }
    }
}