using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.DFM.Model.ViewModels
{
    public class LabelFormatRequestModel : RequestPageModelBase
    {
        public LabelFormatRequestModel()
        {
        }
           /// <summary>
           /// Desc:描述
           /// Default:
           /// Nullable:False
           /// </summary>
        public string Description { get; set; }
           /// <summary>
           /// Desc:国家语言配置ID
           /// Default:
           /// Nullable:False
           /// </summary>
        public string CountryId { get; set; }

        public string CountryName { get; set; }
           /// <summary>
           /// Desc:批次号前缀
           /// Default:
           /// Nullable:True
           /// </summary>
        public string LotPrefix { get; set; }
           /// <summary>
           /// Desc:批次号后缀
           /// Default:
           /// Nullable:True
           /// </summary>
        public string LotSuffix { get; set; }
           /// <summary>
           /// Desc:日期格式前缀
           /// Default:
           /// Nullable:True
           /// </summary>
        public string DayFormatPrefix { get; set; }
           /// <summary>
           /// Desc:日期格式
           /// Default:
           /// Nullable:True
           /// </summary>
        public string DayFormat { get; set; }
           /// <summary>
           /// Desc:日期格式后缀
           /// Default:
           /// Nullable:True
           /// </summary>
        public string DayFormatSuffix { get; set; }
           /// <summary>
           /// Desc:月份格式前缀
           /// Default:
           /// Nullable:True
           /// </summary>
        public string MonthFormatPrefix { get; set; }
           /// <summary>
           /// Desc:月份格式
           /// Default:
           /// Nullable:True
           /// </summary>
        public string MonthFormat { get; set; }
           /// <summary>
           /// Desc:月份格式后缀
           /// Default:
           /// Nullable:True
           /// </summary>
        public string MonthFormatSuffix { get; set; }
           /// <summary>
           /// Desc:年份格式前缀
           /// Default:
           /// Nullable:True
           /// </summary>
        public string YearFormatPrefix { get; set; }
           /// <summary>
           /// Desc:年份格式
           /// Default:
           /// Nullable:True
           /// </summary>
        public string YearFormat { get; set; }
           /// <summary>
           /// Desc:年份格式后缀
           /// Default:
           /// Nullable:True
           /// </summary>
        public string YearFormatSuffix { get; set; }
           /// <summary>
           /// Desc:是否大写
           /// Default:
           /// Nullable:False
           /// </summary>
        public string IsUpperCase { get; set; }
           /// <summary>
           /// Desc:日期格式前缀2
           /// Default:
           /// Nullable:True
           /// </summary>
        public string DayFormatPrefix2 { get; set; }
           /// <summary>
           /// Desc:日期格式2
           /// Default:
           /// Nullable:True
           /// </summary>
        public string DayFormat2 { get; set; }
           /// <summary>
           /// Desc:日期格式后缀2
           /// Default:
           /// Nullable:True
           /// </summary>
        public string DayFormatSuffix2 { get; set; }
           /// <summary>
           /// Desc:月份格式前缀2
           /// Default:
           /// Nullable:True
           /// </summary>
        public string MonthFormatPrefix2 { get; set; }
           /// <summary>
           /// Desc:月份格式2
           /// Default:
           /// Nullable:True
           /// </summary>
        public string MonthFormat2 { get; set; }
           /// <summary>
           /// Desc:月份格式后缀2
           /// Default:
           /// Nullable:True
           /// </summary>
        public string MonthFormatSuffix2 { get; set; }
           /// <summary>
           /// Desc:年份格式前缀2
           /// Default:
           /// Nullable:True
           /// </summary>
        public string YearFormatPrefix2 { get; set; }
           /// <summary>
           /// Desc:年份格式2
           /// Default:
           /// Nullable:True
           /// </summary>
        public string YearFormat2 { get; set; }
           /// <summary>
           /// Desc:年份格式后缀2
           /// Default:
           /// Nullable:True
           /// </summary>
        public string YearFormatSuffix2 { get; set; }
           /// <summary>
           /// Desc:月份格式偏置
           /// Default:
           /// Nullable:False
           /// </summary>
        public string MonthFormatOffset { get; set; }
           /// <summary>
           /// Desc:日期格式偏置
           /// Default:
           /// Nullable:False
           /// </summary>
        public string DayFormatOffset { get; set; }

        public string ID { get; set; }

        public DateTime? CreateDate { get; set; }

        public string CreateUserId { get; set; }

        public DateTime? ModifyDate { get; set; }

        public string ModifyUserId { get; set; }
    }
}