using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.DFM.Model.ViewModels
{
    public class LabelPrinterClassPropRequestModel : RequestPageModelBase
    {
        public LabelPrinterClassPropRequestModel()
        {
        }
           /// <summary>
           /// Desc:打印机类型
           /// Default:
           /// Nullable:False
           /// </summary>
        public string PrinterClassId { get; set; }

        public string PrinterClassName { get; set; }
           /// <summary>
           /// Desc:属性
           /// Default:
           /// Nullable:False
           /// </summary>
        public string PropertyDefinitionId { get; set; }

        public string PropertyDefinitionName { get; set; }
           /// <summary>
           /// Desc:是否需要
           /// Default:
           /// Nullable:False
           /// </summary>
        public string IsRequired { get; set; }

        public string ID { get; set; }

        public DateTime? CreateDate { get; set; }

        public string CreateUserId { get; set; }

        public DateTime? ModifyDate { get; set; }

        public string ModifyUserId { get; set; }

    }
}