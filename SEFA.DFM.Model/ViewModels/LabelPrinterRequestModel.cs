using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.DFM.Model.ViewModels
{
    public class LabelPrinterRequestModel : RequestPageModelBase
    {
        public LabelPrinterRequestModel()
        {
        }
           /// <summary>
           /// Desc:名称
           /// Default:
           /// Nullable:False
           /// </summary>
        public string Code { get; set; }
           /// <summary>
           /// Desc:设备
           /// Default:
           /// Nullable:True
           /// </summary>
        public string EquipmentId { get; set; }
        public string EquipmentName { get; set; }
           /// <summary>
           /// Desc:打印类型
           /// Default:
           /// Nullable:False
           /// </summary>
        public string PrinterClassId { get; set; }
        public string PrinterClassName { get; set; }
           /// <summary>
           /// Desc:描述
           /// Default:
           /// Nullable:False
           /// </summary>
        public string Description { get; set; }
           /// <summary>
           /// Desc:状态
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Status { get; set; }
           /// <summary>
           /// Desc:允许手动打印
           /// Default:
           /// Nullable:False
           /// </summary>
        public string AllowManualPrint { get; set; }
           /// <summary>
           /// Desc:类型
           /// Default:
           /// Nullable:False
           /// </summary>
        public string Type { get; set; }
           /// <summary>
           /// Desc:生成规则
           /// Default:
           /// Nullable:False
           /// </summary>
        public string SequenceType { get; set; }
           /// <summary>
           /// Desc:标签大小
           /// Default:
           /// Nullable:False
           /// </summary>
        public string LabelSizeId { get; set; }
        public string LabelSizeName { get; set; }

        public string ID { get; set; }

        public DateTime? CreateDate { get; set; }

        public string CreateUserId { get; set; }

        public DateTime? ModifyDate { get; set; }

        public string ModifyUserId { get; set; }
    }
}