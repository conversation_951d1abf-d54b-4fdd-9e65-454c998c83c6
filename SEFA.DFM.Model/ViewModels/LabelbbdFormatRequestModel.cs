using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.DFM.Model.ViewModels
{
    public class LabelbbdFormatRequestModel : RequestPageModelBase
    {
        public LabelbbdFormatRequestModel()
        {
        }
           /// <summary>
           /// Desc:有效期类型
           /// Default:
           /// Nullable:False
           /// </summary>
        public string ExpirationType { get; set; }
           /// <summary>
           /// Desc:格式ID
           /// Default:
           /// Nullable:False
           /// </summary>
        public string FormatId { get; set; }
           /// <summary>
           /// Desc:周期
           /// Default:
           /// Nullable:False
           /// </summary>
        public string PeriodIndForSled { get; set; }
           /// <summary>
           /// Desc:BBD显示格式
           /// Default:
           /// Nullable:False
           /// </summary>
        public string BbdDisplayFormat { get; set; }
           /// <summary>
           /// Desc:BBD显示内容
           /// Default:
           /// Nullable:False
           /// </summary>
        public string BbdDisplayText { get; set; }
           /// <summary>
           /// Desc:大写日期
           /// Default:
           /// Nullable:False
           /// </summary>
        public string UpperCaseDate { get; set; }
           /// <summary>
           /// Desc:条形码
           /// Default:
           /// Nullable:False
           /// </summary>
        public string BarcodeAi { get; set; }
           /// <summary>
           /// Desc:条形码格式
           /// Default:
           /// Nullable:False
           /// </summary>
        public string BarcodeDateFormat { get; set; }

    }
}