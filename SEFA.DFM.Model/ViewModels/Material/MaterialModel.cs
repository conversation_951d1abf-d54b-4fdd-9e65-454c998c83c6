using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.DFM.Model.ViewModels
{
    public class MaterialModel
    {
        /// <summary>
        /// Desc:MappingId
        /// Default:
        /// Nullable:False
        /// </summary>
        public string ID { get; set; }
        /// <summary>
        /// Desc:物料组ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string MaterialGroupId { get; set; }
        /// <summary>
        /// Desc:物料组名称
        /// Default:
        /// Nullable:False
        /// </summary>
        public string MaterialGroupName { get; set; }
        /// <summary>
        /// Desc:物料ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string MaterialId { get; set; }
        /// <summary>
        /// Desc:编码
        /// Default:
        /// Nullable:False
        /// </summary>
        public string MaterialCode { get; set; }
        /// <summary>
        /// Desc:名称
        /// Default:
        /// Nullable:False
        /// </summary>
        public string MaterialName { get; set; }
       
        /// <summary>
        /// Desc:d默认单位Id
        /// Default:
        /// Nullable:True
        /// </summary>
        public string UnitId { get; set; }
        /// <summary>
        /// Desc:d默认单位
        /// Default:
        /// Nullable:True
        /// </summary>
        public string Unit { get; set; }
       
    }
}