using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.DFM.Model.ViewModels
{
    public class MaterialVersionModel : EntityBase
    {

        /// <summary>
        /// Desc:名称
        /// Default:
        /// Nullable:False
        /// </summary>
        public string MaterialId { get; set; }
        /// <summary>
        /// Desc:名称
        /// Default:
        /// Nullable:False
        /// </summary>
        public string MaterialCode { get; set; }
        /// <summary>
        /// Desc:名称
        /// Default:
        /// Nullable:False
        /// </summary>
        public string MaterialName { get; set; }
        /// <summary>
        /// Desc:类型
        /// Default:
        /// Nullable:True
        /// </summary>
        public string FormulaCode { get; set; }
        /// <summary>
        /// Desc:状态
        /// Default:
        /// Nullable:False
        /// </summary>
        public string MaterialVersionNumber { get; set; }
        /// <summary>
        /// Desc:备注
        /// Default:
        /// Nullable:True
        /// </summary>
        public string Remark { get; set; }

        /// <summary>
        /// Desc:删除标识
        /// Default:
        /// Nullable:False
        /// </summary>
        public int Deleted { get; set; }
        public string ShowName { get; set; }
    }
}