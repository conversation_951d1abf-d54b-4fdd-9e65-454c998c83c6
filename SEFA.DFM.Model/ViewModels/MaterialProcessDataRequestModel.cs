using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.DFM.Model.ViewModels
{
    public class MaterialProcessDataRequestModel : RequestPageModelBase
    {
        public MaterialProcessDataRequestModel()
        {
        }
           /// <summary>
           /// Desc:产品版本
           /// Default:
           /// Nullable:False
           /// </summary>
        public string VersionId { get; set; }
           /// <summary>
           /// Desc:工艺长文本
           /// Default:
           /// Nullable:False
           /// </summary>
        public string ProcessData { get; set; }
           /// <summary>
           /// Desc:长文本编码
           /// Default:
           /// Nullable:False
           /// </summary>
        public string HashData { get; set; }
           /// <summary>
           /// Desc:长文本版本
           /// Default:
           /// Nullable:True
           /// </summary>
        public string TextVersion { get; set; }
           /// <summary>
           /// Desc:已提醒
           /// Default:
           /// Nullable:False
           /// </summary>
        public string IsReminded { get; set; }
           /// <summary>
           /// Desc:状态
           /// Default:
           /// Nullable:False
           /// </summary>
        public string Status { get; set; }
           /// <summary>
           /// Desc:工单号
           /// Default:
           /// Nullable:False
           /// </summary>
        public string OrderId { get; set; }
           /// <summary>
           /// Desc:审核时间
           /// Default:
           /// Nullable:False
           /// </summary>
        public DateTime Reviewtime { get; set; }
           /// <summary>
           /// Desc:审核人员
           /// Default:
           /// Nullable:False
           /// </summary>
        public string Reviewuserid { get; set; }
        /// <summary>
        /// Desc:类型
        /// Default:
        /// Nullable:False
        /// </summary>
        public int Type { get; set; }
        /// <summary>
        /// Desc:Token
        /// Default:
        /// Nullable:False
        /// </summary>
        public string Token { get; set; }
    }
}