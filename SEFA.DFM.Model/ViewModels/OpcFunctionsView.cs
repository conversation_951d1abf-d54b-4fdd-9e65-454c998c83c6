using SEFA.DFM.Model.Models;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SEFA.DFM.Model.ViewModels
{
    /// <summary>
    /// Opc Function View
    /// </summary>
    public class OpcFunctionsView
    {
       
        public string ID { get; set; }
       
        public DateTime CreateDate { get; set; }
        
        public string CreateUserId { get; set; }

        public DateTime ModifyDate { get; set; }

        public string ModifyUserId { get; set; }

        /// <summary>
        /// Desc:名称
        /// Default:
        /// Nullable:False
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// Desc:描述
        /// Default:
        /// Nullable:False
        /// </summary>
        public string Description { get; set; }
        /// <summary>
        /// Desc:OPC动作ID
        /// Default:
        /// Nullable:True
        /// </summary>
        public string OpcActionClassId { get; set; }
        /// <summary>
        /// Desc:超时时间
        /// Default:
        /// Nullable:True
        /// </summary>
        public int? Timeout { get; set; }
        /// <summary>
        /// Desc:日志事物
        /// Default:
        /// Nullable:False
        /// </summary>
        public string LogTransactions { get; set; }
        /// <summary>
        /// Desc:是否允许多实例
        /// Default:
        /// Nullable:False
        /// </summary>
        public string AllowMultipleInstances { get; set; }
        /// <summary>
        /// Desc:是否阻塞读取
        /// Default:
        /// Nullable:False
        /// </summary>
        public string BlockRead { get; set; }
        /// <summary>
        /// Desc:命令格式
        /// Default:
        /// Nullable:True
        /// </summary>
        public string StringCommandFormat { get; set; }
        /// <summary>
        /// Desc:分隔符
        /// Default:
        /// Nullable:True
        /// </summary>
        public string Separator { get; set; }
        /// <summary>
        /// Desc:命令符
        /// Default:
        /// Nullable:True
        /// </summary>
        public string Terminator { get; set; }
        /// <summary>
        /// Desc:版本
        /// Default:
        /// Nullable:True
        /// </summary>
        public int? VersionNumber { get; set; }
        /// <summary>
        /// Desc:删除标识
        /// Default:
        /// Nullable:False
        /// </summary>
        public int Deleted { get; set; }

        /// <summary>
        /// Desc:Property数
        /// Default:
        /// Nullable:False
        /// </summary>
        public int PropertyCount { get; set; }

        /// <summary>
        /// Desc:Trigger数
        /// Default:
        /// Nullable:False
        /// </summary>
        public int TriggerCount { get; set; }
    }
}
