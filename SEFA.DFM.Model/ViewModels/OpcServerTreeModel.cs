using SEFA.DFM.Model.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SEFA.DFM.Model.ViewModels
{
    /// <summary>
    /// Opc Server
    /// </summary>
    public class OpcServerTreeModel
    {
        /// <summary>
        /// Server Name
        /// </summary>
        public string WhereServerDesc { get; set; }

        /// <summary>
        /// Group Name
        /// </summary>
        public string WhereGroupName { get; set; }

        /// <summary>
        /// Tag Name
        /// </summary>
        public string WhereTagName { get; set; }

        /// <summary>
        /// Id
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// ParentId
        /// </summary>
        public string ParentId { get; set; }

        /// <summary>
        /// 名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; }
       
        /// <summary>
        /// 节点类型：Server、Group、Tag
        /// </summary>
        public string NodeType { get; set; }

        /// <summary>
        /// 数据类型
        /// </summary>
        public string DataType { get; set; }

        /// <summary>
        /// 数字长度
        /// </summary>
        public int? ArrayLength { get; set; }

        /// <summary>
        /// 因子
        /// </summary>
        public string Factor { get; set; }

        /// <summary>
        /// 原始PLC标签ID
        /// </summary>
        public string RawPlcTagId { get; set; }

        /// <summary>
        /// 删除标识
        /// </summary>
        public int Deleted { get; set; }

    }
}
