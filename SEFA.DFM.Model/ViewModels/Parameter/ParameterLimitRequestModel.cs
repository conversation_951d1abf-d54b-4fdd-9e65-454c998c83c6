using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;
using System.Collections.Generic;

namespace SEFA.DFM.Model.ViewModels
{
    public class ParameterLimitRequestModel : RequestPageModelBase
    {
        public ParameterLimitRequestModel()
        {
        }
        /// <summary>
        /// Desc:参数配置ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public List<string> ParameterConfigIdArray { get; set; }
        /// <summary>
        /// Desc:参数配置ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string ParameterConfigId { get; set; }
           /// <summary>
           /// Desc:排序号
           /// Default:
           /// Nullable:False
           /// </summary>
        public int SortOrder { get; set; }
           /// <summary>
           /// Desc:对比类型
           /// Default:
           /// Nullable:False
           /// </summary>
        public string ScoreNumber { get; set; }
           /// <summary>
           /// Desc:上限值
           /// Default:
           /// Nullable:True
           /// </summary>
        public decimal? UpperLimit { get; set; }
           /// <summary>
           /// Desc:是否包含上限值
           /// Default:
           /// Nullable:False
           /// </summary>
        public string IncludeUpperLimit { get; set; }
           /// <summary>
           /// Desc:显示文本
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Description { get; set; }
           /// <summary>
           /// Desc:IsCommontRequired
           /// Default:
           /// Nullable:True
           /// </summary>
        public string IsCommentRequired { get; set; }
           /// <summary>
           /// Desc:OpcTagId
           /// Default:
           /// Nullable:True
           /// </summary>
        public string OpcTagId { get; set; }
           /// <summary>
           /// Desc:类型
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Type { get; set; }
           /// <summary>
           /// Desc:扩展状态
           /// Default:
           /// Nullable:True
           /// </summary>
        public string ExtrenalStatus { get; set; }
           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:False
           /// </summary>
        public int Deleted { get; set; }

    }


    public class ParameterLimitData {
        public string ParameterConfigId { get; set; }
        public string TemplateName { get; set; }
        public string Type { get; set; }
        public List<ParameterLimitRequestModel> ParameterLimitList { get; set; }
    }
}