using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.DFM.Model.ViewModels
{
    public class ParameterTemplateRequestModel : RequestPageModelBase
    {
        public ParameterTemplateRequestModel()
        {
        }
           /// <summary>
           /// Desc:参数组名称
           /// Default:
           /// Nullable:False
           /// </summary>
        public string Name { get; set; }
           /// <summary>
           /// Desc:参数组类型
           /// Default:
           /// Nullable:False
           /// </summary>
        public string Type { get; set; }
           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:False
           /// </summary>
        public int Deleted { get; set; }

    }
}