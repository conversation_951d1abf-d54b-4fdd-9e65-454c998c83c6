using Magicodes.ExporterAndImporter.Core;
using System.Xml.Linq;

namespace SEFA.DFM.Model.ViewModels
{
    /// <summary>
    ///  岗位导入导出Dto
    /// </summary>
    public class PostExcelDto
    {
        /// <summary>
        /// 岗位名称
        /// </summary>
        [ImporterHeader(Name = "岗位名称")]
        [ExporterHeader(DisplayName = "岗位名称")]
        public string Name { get; set; }


        /// <summary>
        /// 班制（岗位工作时长）
        /// </summary>
        [ImporterHeader(Name = "班制")]
        [ExporterHeader(DisplayName = "班制")]
        public decimal? WorkHours { get; set; }



        /// <summary>
        /// 岗位类型
        /// </summary>
        [ImporterHeader(Name = "岗位类型(Andonwork、piecework)")]
        [ExporterHeader(DisplayName = "岗位类型(Andonwork、piecework)")]
        public string Type { get; set; }


        /// <summary>
        /// 岗位描述
        /// </summary>
        [ImporterHeader(Name = "岗位描述")]
        [ExporterHeader(DisplayName = "岗位描述")]
        public string Description { get; set; }




        /// <summary>
        /// 岗位单价（提供给设备模块使用 2023年5月23日）
        /// </summary>
        [ImporterHeader(Name = "维修成本单价/小时")]
        [ExporterHeader(DisplayName = "维修成本单价/小时")]
        public string PostPrice { get; set; }

        ///// <summary>
        ///// 岗位职责范围
        ///// </summary>
        //public string PostRange { get; set; }




    }
}
