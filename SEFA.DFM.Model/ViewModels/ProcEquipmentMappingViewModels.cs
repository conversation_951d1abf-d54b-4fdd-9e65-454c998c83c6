using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SEFA.DFM.Model.ViewModels
{
    public class ProcEquipmentMappingViewModels
    {



        /// <summary>
        /// Desc:工艺路线ID
        /// Default:
        /// Nullable:False
        /// </summary>

        public string ProcId { get; set; }
        /// <summary>
        /// Desc:物理模型ID
        /// Default:
        /// Nullable:False
        /// </summary>
        
        public string EquipmentId { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>

        public string Id { get; set; }
        /// <summary>
        /// Desc:工序编码
        /// Default:
        /// Nullable:False
        /// </summary>

        public string ProcCode { get; set; }
        /// <summary>
        /// Desc:工序名称
        /// Default:
        /// Nullable:True
        /// </summary>

        public string ProcName { get; set; }

        /// <summary>
        /// Desc:工序类型
        /// Default:
        /// Nullable:True
        /// </summary>

        public string ProcType { get; set; }
        /// <summary>
        /// Desc:父节点code
        /// Default:
        /// Nullable:True
        /// </summary>

        public string ParentId { get; set; }

        /// <summary>
        /// Desc:标准加工时间
        /// Default:
        /// Nullable:True
        /// </summary>

        public decimal? StandardTime { get; set; }
        /// <summary>
        /// Desc:标准加工单位
        /// Default:
        /// Nullable:True
        /// </summary>

        public string StandardUom { get; set; }
        /// <summary>
        /// Desc:准备时间
        /// Default:
        /// Nullable:True
        /// </summary>

        public decimal? PrepareTime { get; set; }
        /// <summary>
        /// Desc:运输时间
        /// Default:
        /// Nullable:True
        /// </summary>

        public decimal? TransTime { get; set; }
        /// <summary>
        /// Desc:标准用工人数
        /// Default:
        /// Nullable:True
        /// </summary>

        public int StandardPeronNum { get; set; }
        /// <summary>
        /// Desc:设计产能
        /// Default:
        /// Nullable:True
        /// </summary>

        public string UnitCapacity { get; set; }

        /// <summary>
        /// Desc:描述
        /// Default:
        /// Nullable:True
        /// </summary>

        public string Description { get; set; }


        /// <summary>
        /// Desc:删除标识
        /// Default:
        /// Nullable:False
        /// </summary>

        public int Deleted { get; set; }

        /// <summary>
        /// Desc:是否创建工单
        /// Default:
        /// Nullable:False
        /// </summary>

        public int IsCreateOrder { get; set; }




        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>

        public string EquipmentCode { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>

        public string EquipmentName { get; set; }
        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>

        public string Level { get; set; }
        /// <summary>
        /// Desc:备注
        /// Default:
        /// Nullable:False
        /// </summary>

        public string Remark { get; set; }


        /// <summary>
        /// Desc:启用标记
        /// Default:
        /// Nullable:False
        /// </summary>

        public int Enabled { get; set; }

    }
}
