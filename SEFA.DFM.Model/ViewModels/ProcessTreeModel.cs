using SEFA.DFM.Model.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SEFA.DFM.Model.ViewModels
{
    public class ProcessTreeModel : TreeModel
    {

        /// <summary>
        /// Desc:工序编码
        /// Default:
        /// Nullable:False
        /// </summary>
        public string ProcCode { get; set; }
        /// <summary>
        /// Desc:工序名称
        /// Default:
        /// Nullable:True
        /// </summary>
        public string ProcName { get; set; }

        /// <summary>
        /// Desc:工序类型
        /// Default:
        /// Nullable:True
        /// </summary>
        public string ProcType { get; set; }
        /// <summary>
        /// Desc:父节点code
        /// Default:
        /// Nullable:True
        /// </summary>
        public string ParentId { get; set; }

        /// <summary>
        /// Desc:标准加工时间
        /// Default:
        /// Nullable:True
        /// </summary>
        public decimal? StandardTime { get; set; }
        /// <summary>
        /// Desc:标准加工单位
        /// Default:
        /// Nullable:True
        /// </summary>
        public string StandardUom { get; set; }
        /// <summary>
        /// Desc:准备时间
        /// Default:
        /// Nullable:True
        /// </summary>
        public decimal? PrepareTime { get; set; }
        /// <summary>
        /// Desc:运输时间
        /// Default:
        /// Nullable:True
        /// </summary>
        public decimal? TransTime { get; set; }
        /// <summary>
        /// Desc:标准用工人数
        /// Default:
        /// Nullable:True
        /// </summary>
        public int StandardPeronNum { get; set; }
        /// <summary>
        /// Desc:设计产能
        /// Default:
        /// Nullable:True
        /// </summary>
        public string UnitCapacity { get; set; }

        /// <summary>
        /// Desc:描述
        /// Default:
        /// Nullable:True
        /// </summary>
        public string Description { get; set; } 

        public int Deleted { get; set; }
        public string ID { get; set; }

        public DateTime CreateDate { get; set; }

        public string CreateUserId { get; set; }

        public DateTime ModifyDate { get; set; }

        public string ModifyUserId { get; set; }
        public int IsCreateOrder { get; set; }
    }
}
