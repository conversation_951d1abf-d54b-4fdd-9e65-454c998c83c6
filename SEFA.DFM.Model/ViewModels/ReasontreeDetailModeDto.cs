using SEFA.DFM.Model.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SEFA.DFM.Model.ViewModels
{
    public class ReasontreeDetailModeDto
    {
        public string ParentId { get; set; }

        public string ReasontreeName { get; set; }
        /// <summary>
        /// Desc:原因树Code
        /// Default:
        /// Nullable:False
        /// </summary>
        public string ReasontreeCode { get; set; }
        /// <summary>
        /// Desc:原因明细代码
        /// Default:
        /// Nullable:False
        /// </summary>
        public string DetailCode { get; set; }
        /// <summary>
        /// Desc:原因明细描述
        /// Default:
        /// Nullable:False
        /// </summary>
        public string DetailDescription { get; set; }
        /// <summary>
        /// Desc:层级
        /// Default:
        /// Nullable:False
        /// </summary>
        public int DetailLevel { get; set; }
        /// <summary>
        /// Desc:父节点代码
        /// Default:
        /// Nullable:False
        /// </summary>
        public string DetailParentcode { get; set; }
        /// <summary>
        /// Desc:预留字段1
        /// Default:
        /// Nullable:True
        /// </summary>
        public string Reserve1 { get; set; }
        /// <summary>
        /// Desc:预留字段2
        /// Default:
        /// Nullable:True
        /// </summary>
        public string Reserve2 { get; set; }
        /// <summary>
        /// Desc:备注
        /// Default:
        /// Nullable:True
        /// </summary>
        public string Remark { get; set; }
        /// <summary> 
        /// 父原因明细代码 
        /// </summary> 
        /// <returns></returns> 
        public string PDetail_ID { get; set; }
        /// <summary>
        /// Desc:
        /// Default:0
        /// Nullable:False
        /// </summary>
        public int Deleted { get; set; }
        /// <summary>
        /// 最小层级
        /// </summary>
        public int? Min_Detail_Level { get; set; }


        public List<ReasontreeDetailEntity> ListData { get; set; }
        public List<ReasonTreeDetailTreeModel> ListTree { get; set; }
    }
}
