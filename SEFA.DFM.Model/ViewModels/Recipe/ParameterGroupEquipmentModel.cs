using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.DFM.Model.ViewModels
{
    public class ParameterGroupEquipmentModel : EntityBase
    {
        public ParameterGroupEquipmentModel ()
        {
        }
           /// <summary>
           /// Desc:配方ID
           /// Default:
           /// Nullable:True
           /// </summary>
        public string RecipeId { get; set; }
           /// <summary>
           /// Desc:配方版块ID
           /// Default:
           /// Nullable:True
           /// </summary>
        public string RecipeSectionId { get; set; }
           /// <summary>
           /// Desc:配方版块版本ID
           /// Default:
           /// Nullable:False
           /// </summary>
        public string RecipeSectionVersionId { get; set; }
           /// <summary>
           /// Desc:参数组ID
           /// Default:
           /// Nullable:False
           /// </summary>
        public string ParameterGroupId { get; set; }
           /// <summary>
           /// Desc:设备ID
           /// Default:
           /// Nullable:False
           /// </summary>
        public string EquipmentId { get; set; }
        /// <summary>
        /// Desc:设备名称
        /// Default:
        /// Nullable:False
        /// </summary>
        public string EquipmentName { get; set; }
        /// <summary>
        /// Desc:采集点位ID
        /// Default:
        /// Nullable:True
        /// </summary>
        public string EnableTagId { get; set; }
           /// <summary>
           /// Desc:采集频率ID
           /// Default:
           /// Nullable:True
           /// </summary>
        public string FrequencyTagId { get; set; }
           /// <summary>
           /// Desc:SCANNABLED_ID
           /// Default:
           /// Nullable:True
           /// </summary>
        public string ScannabledId { get; set; }
           /// <summary>
           /// Desc:自动触发
           /// Default:
           /// Nullable:True
           /// </summary>
        public string AutoTrigger { get; set; }
           /// <summary>
           /// Desc:OPC_FUNCTION_INSTANCE_ID
           /// Default:
           /// Nullable:True
           /// </summary>
        public string OpcFunctionInstanceId { get; set; }
           /// <summary>
           /// Desc:删除标识
           /// Default:
           /// Nullable:False
           /// </summary>
        public int Deleted { get; set; }

    }
}