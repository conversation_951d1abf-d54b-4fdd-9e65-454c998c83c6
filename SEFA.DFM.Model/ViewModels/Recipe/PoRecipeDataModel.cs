using SEFA.Base.Model.BASE;
using SEFA.DFM.Model.Models;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SEFA.DFM.Model.ViewModels
{
    public class PoRecipeDataModel
    {
        /// <summary>
        /// 工单ID
        /// </summary>
        public string ProductionOrderId { get; set; }
        /// <summary>
        /// 工单绑定的RecipeTable参数数据
        /// </summary>
        public RecipeTableModel RecipeTableData { get; set; }

        /// <summary>
        /// 工单绑定的RecipeTable参数数据
        /// </summary>
        public List<RecipeParameterModel> RecipeTableList { get; set; }

        /// <summary>
        /// 工单绑定的RecipeTable参数数据
        /// </summary>
        public List<RecipeParameterModel> ControlRecipeList { get; set; }
        /// <summary>
        /// 工单绑定的控制配方参数
        /// </summary>
        public List<ControlRecipeModel> ControlRecipeEquipmentList { get; set; }
    }

    public class RecipeTableModel
    {
        /// <summary>
        /// 工单绑定的RecipeTable参数 表
        /// </summary>
        public DataTable dataTable { get; set; }
        /// <summary>
        /// 每步绑定的设备信息集合
        /// </summary>
        public List<StepEquipmentModel> StepEquipmentData { get; set; }
        
    }

    public class StepEquipmentModel
    {
        /// <summary>
        /// 步骤名称(参数组名称)
        /// </summary>
        public string StepName { get; set; }
        /// <summary>
        /// 步骤绑定的设备列表
        /// </summary>
        public List<StepEquipment> EquipmentList { get; set; }

    }

    public class StepEquipment
    {
        /// <summary>
        /// 设备ID
        /// </summary>
        public string EquipmentId { get; set; }
        /// <summary>
        /// 设备编码
        /// </summary>
        public string EquipmentCode { get; set; }
        /// <summary>
        /// 设备名称
        /// </summary>
        public string EquipmentName { get; set; }
    }

    public class ControlRecipeModel
    {
        /// <summary>
        /// 设备ID
        /// </summary>
        public string EquipmentId { get; set; }
        /// <summary>
        /// 设备编码
        /// </summary>
        public string EquipmentCode { get; set; }
        /// <summary>
        /// 设备名称
        /// </summary>
        public string EquipmentName { get; set; }

        /// <summary>
        /// 设备绑定的配方参数明细列表
        /// </summary>
        public List<RecipeParameterModel> parameterList { get; set; }

    }
}
