using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using System.Threading.Channels;

namespace SEFA.DFM.Model.Models
{
	public class RecipeDataType
	{
        /// <summary>
        /// 参数组类型
        /// </summary>
        public static class ParameterGroupType
        {
            /// <summary>
            /// 配方:1
            /// </summary>
            public static string RECIPE = "1";
            /// <summary>
            /// 表单:2
            /// </summary>
            public static string LOGSHEET = "2";

        }
        /// <summary>
        /// 参数组模型
        /// </summary>
        public static class ParameterGroupModel
        {
            /// <summary>
            /// 物料映射:M
            /// </summary>
            public static string MATERIAL = "M";
            /// <summary>
            /// 设备映射:E
            /// </summary>
            public static string EQUIPMENT = "E";
         
        }

        /// <summary>
        /// 激活状态
        /// </summary>
        public static class ActiveStatus
        {
			/// <summary>
			/// 激活
			/// </summary>
			public static string Active = "1";
			/// <summary>
			/// 未激活
			/// </summary>
            public static string NotActive = "0";
        }

        /// <summary>
        /// 签出状态
        /// </summary>
        public static class CheckStatus
        {
            /// <summary>
            /// 签出 0
            /// </summary>
            public static string CheckOut = "0";
            /// <summary>
            /// 签入 10
            /// </summary>
            public static string CheckIn = "10";
            /// <summary>
            /// 驳回 20
            /// </summary>
            public static string Rejected = "20";
            /// <summary>
            /// 审批通过 30
            /// </summary>
            public static string Approved = "30";
        }

        /// <summary>
        /// 日志类型
        /// </summary>
        public static class AuditType
        {
            /// <summary>
            /// 部门
            /// </summary>
            public static string Section = "1";
            /// <summary>
            /// 
            /// </summary>
            public static string Context = "2";
            /// <summary>
            /// 参数组
            /// </summary>
            public static string ParameterGroup = "3";
            /// <summary>
            /// 参数组设备
            /// </summary>
            public static string ParameterGroupEquipment = "4";
            /// <summary>
            /// 参数定义
            /// </summary>
            public static string ParameterDefine = "5";
            /// <summary>
            /// 配置参数
            /// </summary>
            public static string ParameterConfig = "6";
            /// <summary>
            /// 物料映射
            /// </summary>
            public static string MapMaterials = "9";
            /// <summary>
            /// 使能激活
            /// </summary>
            public static string RecipeActive = "10";
        }
        /// <summary>
        /// 日志操作
        /// </summary>
        public static class AuditAction {
            /// <summary>
            /// CehckIn
            /// </summary>
            public static string CehckIn = "1";
            /// <summary>
            /// CehckOut
            /// </summary>
            public static string CehckOut = "2";
            /// <summary>
            /// 生效
            /// </summary>
            public static string Effective = "4";
            /// <summary>
            /// 编辑
            /// </summary>
            public static string Edit = "7";
            /// <summary>
            /// 更改值
            /// </summary>
            public static string EditValue = "7";
        }

        /// <summary>
        /// 参数类型
        /// </summary>
        public static class ParameterType
        {
            /// <summary>
            /// 表单 2
            /// </summary>
            public static string 表单 = "2";
            /// <summary>
            /// 输入 1
            /// </summary>
            public static string 输入 = "1";
        }

        /// <summary>
        /// 参数数据类型
        /// </summary>
        public static class ParameterDataType
        {
            /// <summary>
            /// 自由文本 1
            /// </summary>
            public static string 自由文本 = "1";
            /// <summary>
            /// 数字 2
            /// </summary>
            public static string 数字 = "2";
            /// <summary>
            /// 小数 3
            /// </summary>
            public static string 小数 = "3";
            /// <summary>
            /// 日期 4
            /// </summary>
            public static string 日期 = "4";
        }
    }

    public class ParameterScoringNumber {
        public static string IN  ="1";
        public static string OUT = "2";
        public static string JUSTIN = "3";
        public static string JUSTOUT = "4";
    }

}
