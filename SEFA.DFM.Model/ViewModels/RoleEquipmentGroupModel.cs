using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;
using System.Collections.Generic;

namespace SEFA.DFM.Model.ViewModels
{
    public class RoleEquipmentGroupModel :EntityBase
    {
        /// <summary>
        /// Desc:角色与设备组绑定ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string RoleEquipmentGroupId { get; set; }
        /// <summary>
        /// Desc:角色ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string RoleId { get; set; }
        /// <summary>
        /// Desc:设备组ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string EquipmentGroupId { get; set; }

        /// <summary>
        /// Desc:设备组名称
        /// Default:
        /// Nullable:False
        /// </summary>
        public string EquipmentGroupName { get; set; }

        /// <summary>
        /// Desc:分组
        /// Default:
        /// Nullable:True
        /// </summary>
        public string ConsoleGroup { get; set; }

    }
}