using SEFA.Base.Model;
using SEFA.Base.Model.BASE;
using SEFA.DFM.Model.Models;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SEFA.DFM.Model.ViewModels
{
    public class SapBomPhaseInjectionModel : EntityBase
    {
        /// <summary>
        /// Desc:物料组ID
        /// Default:
        /// Nullable:True
        /// </summary>
        public string MaterialGroupId { get; set; }
        /// <summary>
        /// Desc:物料组名称
        /// Default:
        /// Nullable:True
        /// </summary>
        public string MaterialGroupName { get; set; }
        /// <summary>
        /// Desc:物料ID
        /// Default:
        /// Nullable:True
        /// </summary>
        public string MaterialId { get; set; }
        /// <summary>
        /// Desc:物料编码
        /// Default:
        /// Nullable:True
        /// </summary>
        public string MaterialCode { get; set; }
        /// <summary>
        /// Desc:物料名称
        /// Default:
        /// Nullable:True
        /// </summary>
        public string MaterialName { get; set; }
        /// <summary>
        /// Desc:物料版本ID
        /// Default:
        /// Nullable:True
        /// </summary>
        public string MaterialVersionId { get; set; }
        /// <summary>
        /// Desc:物料版本号
        /// Default:
        /// </summary>
        public string MaterialVersionNumber { get; set; }
        /// <summary>
        /// Desc:产出工序Id
        /// Default:
        /// Nullable:False
        /// </summary>
        public string ProductSapSegmentId { get; set; }
        /// <summary>
        /// Desc:产出工序编码
        /// Default:
        /// Nullable:False
        public string ProductSapSegmenCode { get; set; }
        /// <summary>
        /// Desc:产出工序名称
        /// Default:
        /// Nullable:False
        public string ProductSapSegmentName { get; set; }
        /// <summary>
        /// Desc:产出工段Id
        /// Default:
        /// Nullable:False
        public string ProductParentSapSegmentId { get; set; }
        /// <summary>
        /// Desc:产出工段编码
        /// Default:
        /// Nullable:False
        public string ProductParentSapSegmentCode { get; set; }
        /// <summary>
        /// Desc:产出工段名称
        /// Default:
        /// Nullable:False
        public string ProductParentSapSegmentName { get; set; }
        /// <summary>
        /// Desc:产出Sap资源Id
        /// Default:
        /// Nullable:False
        public string ProductSapEquipmentId { get; set; }
        /// <summary>
        /// Desc:产出Sap资源编码
        /// Default:
        /// Nullable:False
        public string ProductSapEquipmentCode { get; set; }
        /// <summary>
        /// Desc:产出Sap资源名称
        /// Default:
        /// Nullable:False
        public string ProductSapEquipmentName { get; set; }
        /// <summary>
        /// Desc:消耗工序Id
        /// Default:
        /// Nullable:False
        /// </summary>
        public string ConsumeSapSegmentId { get; set; }
        /// <summary>
        /// Desc:消耗工序编码
        /// Default:
        /// Nullable:False
        /// </summary>
        public string ConsumeSapSegmenCode { get; set; }
        /// <summary>
        /// Desc:消耗工序名称
        /// Default:
        /// Nullable:False
        /// </summary>
        public string ConsumeSapSegmentName { get; set; }
        /// <summary>
        /// Desc:消耗工段Id
        /// Default:
        /// Nullable:False
        /// </summary>
        public string ConsumeParentSapSegmentId { get; set; }
        /// <summary>
        /// Desc:消耗工段编码
        /// Default:
        /// Nullable:False
        /// </summary>
        public string ConsumeParentSapSegmentCode { get; set; }
        /// <summary>
        /// Desc:消耗工段名称
        /// Default:
        /// Nullable:False
        /// </summary>
        public string ConsumeParentSapSegmentName { get; set; }
        /// <summary>
        /// Desc:消耗Sap资源Id
        /// Default:
        /// Nullable:False
        /// </summary>
        public string ConsumeSapEquipmentId { get; set; }
        /// <summary>
        /// Desc:消耗Sap资源编码
        /// Default:
        /// Nullable:False
        public string ConsumeSapEquipmentCode { get; set; }
        /// <summary>
        /// Desc:消耗Sap资源名称
        /// Default:
        /// Nullable:False
        public string ConsumeSapEquipmentName { get; set; }
        /// <summary>
        /// Desc:产出物料ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string ProductMaterialId { get; set; }
        /// <summary>
        /// Desc:产出物料编码
        /// Default:
        /// Nullable:False
        /// </summary>
        public string ProductMaterialCode { get; set; }
        /// <summary>
        /// Desc:产出物料名称
        /// Default:
        /// Nullable:False
        /// </summary>
        public string ProductMaterialName { get; set; }
        /// <summary>
        /// Desc:产出物料单位
        /// Default:
        /// Nullable:True
        /// </summary>
        public string ProductMaterialUnitId { get; set; }
        /// <summary>
        /// Desc:类型
        /// Default:
        /// Nullable:False
        /// </summary>
        public int Type { get; set; }
        /// <summary>
        /// Desc:删除标识
        /// Default:
        /// Nullable:False
        /// </summary>
        public int Deleted { get; set; }

        /// <summary>
        /// 转换率
        /// </summary>
        public decimal? PercentQuantity { get; set; }
        /// <summary>
        /// Desc:优先级
        /// Default:
        /// Nullable:False
        /// </summary>
        public int? Priority { get; set; }
        /// <summary>
        /// Desc:显示名称
        /// Default:
        /// Nullable:False
        /// </summary>
        public string RunMaterial { get; set; }

        /// <summary>
        /// Desc:消耗数量
        /// Default:
        /// Nullable:True
        /// </summary>
        public decimal? ConsumeQuantity { get; set; }
        /// <summary>
        /// Desc:消耗单位
        /// Default:
        /// Nullable:True
        /// </summary>
        public string ConsumeUnitId { get; set; }



    }

    public class SapBomPhaseInjectionRequestModel : RequestPageModelBase
    {
        public string key { get; set; }
    }
}
