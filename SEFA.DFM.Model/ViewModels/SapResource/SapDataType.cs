using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using System.Threading.Channels;

namespace SEFA.DFM.Model.Models
{
	public class SapDataType
    {

        /// <summary>
        /// 激活状态
        /// </summary>
        public enum ActiveStatus
        {
			/// <summary>
			/// 激活
			/// </summary>
			Active = 1,
			/// <summary>
			/// 未激活
			/// </summary>
            NotActive = 2
        }

        /// <summary>
        /// BomInjection类型
        /// </summary>
        public enum BomInjectionType
        {
            /// <summary>
            /// WIP
            /// </summary>
            WIP = 1,
            /// <summary>
            /// 仅消耗
            /// </summary>
            ConsumeOnly,
            /// <summary>
            /// 仅生产
            /// </summary>
            ProductOnly
        }

    }

}
