using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.DFM.Model.ViewModels
{
    public class SapSegmentEquipmentModel : EntityBase
    {
        public SapSegmentEquipmentModel ()
        {
        }
        /// <summary>
        /// Desc:Key
        /// Default:
        /// Nullable:False
        /// </summary>
        public string Key { get; set; }

        /// <summary>
        /// Desc:SAP资源ID
        /// Default:
        /// Nullable:True
        /// </summary>
        public string SapEquipmentId { get; set; }
        /// <summary>
        /// Desc:工序ID
        /// Default:
        /// Nullable:True
        /// </summary>
        public string SapSegmentId { get; set; }
        /// <summary>
        /// Desc:工序编码
        /// Default:
        /// Nullable:False
        /// </summary>
        public string SapSegmentCode { get; set; }
        /// <summary>
        /// Desc:工序名称
        /// Default:
        /// Nullable:False
        /// </summary>
        public string SapSegmentName { get; set; }
        /// <summary>
        /// Desc:设备ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string EquipmentId { get; set; }
        /// <summary>
        /// Desc:设备名称
        /// Default:
        /// Nullable:False
        /// </summary>
        public string EquipmentName { get; set; }
        /// <summary>
        /// Desc:删除标识
        /// Default:
        /// Nullable:False
        /// </summary>
        public int Deleted { get; set; }

    }
}