using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;
using Magicodes.ExporterAndImporter.Core;

namespace SEFA.DFM.Model.ViewModels
{
    public class SapSegmentMaterialStepModel : EntityBase
    {
        public SapSegmentMaterialStepModel () {
        }
        /// <summary>
        /// Desc:key
        /// Default:
        /// Nullable:False
        /// </summary>
        public string key { get; set; }
        /// <summary>
        /// Desc:物料版本Id
        /// Default:
        /// Nullable:False
        /// </summary>
        public string MaterialVersionId { get; set; }
        /// <summary>
        /// Desc:物料Bom版本
        /// Default:
        /// Nullable:False
        /// </summary>
        public string BomVersion { get; set; }
        /// <summary>
        /// Desc:产品BOMID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string SapMaterialBomId { get; set; }
        /// <summary>
        /// Desc:工序名称
        /// Default:
        /// Nullable:False
        /// </summary>
        public string SapSegmentName { get; set; }
        /// <summary>
        /// Desc:工序ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string SapSegmentId { get; set; }
        /// <summary>
        /// Desc:物料全称
        /// Default:
        /// Nullable:False
        /// </summary>
        public string Material { get; set; }
        /// <summary>
        /// Desc:物料ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string MaterialId { get; set; }
        /// <summary>
        /// Desc:物料编码
        /// Default:
        /// Nullable:False
        /// </summary>
        public string MaterialCode { get; set; }
        /// <summary>
        /// Desc:物料名称
        /// Default:
        /// Nullable:False
        /// </summary>
        public string MaterialName { get; set; }
        /// <summary>
        /// Desc:基本单位Id
        /// Default:
        /// Nullable:False
        /// </summary>
        public string UnitId { get; set; }
        /// <summary>
        /// Desc:基本单位
        /// Default:
        /// Nullable:False
        /// </summary>
        public string Unit { get; set; }
        /// <summary>
        /// Desc:利用率
        /// Default:
        /// Nullable:False
        /// </summary>
        public decimal PercentQuantity { get; set; }
        /// <summary>
        /// Desc:调整利用率
        /// Default:
        /// Nullable:True
        /// </summary>
        public decimal? AdjustPercentQuantity { get; set; }
        /// <summary>
        /// Desc:排序号
        /// Default:
        /// Nullable:False
        /// </summary>
        public int SortOrder { get; set; }
        /// <summary>
        /// Desc:删除标识
        /// Default:
        /// Nullable:False
        /// </summary>
        public int Deleted { get; set; }
        /// <summary>
        /// Desc:父级数量
        /// Default:
        /// Nullable:False
        /// </summary>
        public decimal ParentQuantity { get; set; }
        /// <summary>
        /// Desc:数量
        /// Default:
        /// Nullable:False
        /// </summary>
        public decimal Quantity { get; set; }
        /// <summary>
        /// Desc:损耗数量
        /// Default:
        /// Nullable:True
        /// </summary>
        public decimal? LossQuantity { get; set; }

        /// <summary>
        /// Desc:是否需要预处理
        /// Default:
        /// Nullable:True
        /// </summary>
        public string NeedPreprocess { get; set; }

        /// <summary>
        /// Desc:产品编码
        /// Default:
        /// Nullable:True
        /// </summary>
        public string ProductCode { get; set; }
        /// <summary>
        /// Desc:产品名称
        /// Default:
        /// Nullable:True
        /// </summary>
        public string ProductName { get; set; }
        /// <summary>
        /// Desc:产品版本
        /// Default:
        /// Nullable:True
        /// </summary>
        public string ProductVersion { get; set; }
        public string FormulaCode { get; set; }
        public int IsSubcontract { get; set; }
        public string LineWarehouseCode { get; set; }
        public string FeedPort { get; set; }
    }
}