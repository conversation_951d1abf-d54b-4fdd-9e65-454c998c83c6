namespace SEFA.DFM.Model.ViewModels;

public class SopDirDto
{
    /// <summary>
    /// 主键ID
    /// </summary>
    public string ID { get; set; }

    /// <summary>
    /// 父目录ID
    /// </summary>
    public string ParentId { get; set; }

    /// <summary>
    /// 目录名称
    /// </summary>
    public string DirName { get; set; }

    /// <summary>
    /// 目录编码(唯一)
    /// </summary>
    public string DirCode { get; set; }

    /// <summary>
    /// 目录负责人ID
    /// </summary>
    public string OwnerUserid { get; set; }

    /// <summary>
    /// 删除标记(0-未删 1-已删)
    /// </summary>
    public int Deleted { get; set; }

    /// <summary>
    /// 所属用户信息
    /// </summary>
    public string OwnerUser { get; set; }
}