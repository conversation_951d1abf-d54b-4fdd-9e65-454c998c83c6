using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.DFM.Model.ViewModels
{
    public class SopDirRequestModel : RequestPageModelBase
    {
        public SopDirRequestModel()
        {
        }
           /// <summary>
           /// Desc:父目录ID
           /// Default:
           /// Nullable:True
           /// </summary>
        public string ParentId { get; set; }
           /// <summary>
           /// Desc:目录名称
           /// Default:
           /// Nullable:False
           /// </summary>
        public string DirName { get; set; }
           /// <summary>
           /// Desc:目录编码(唯一)
           /// Default:
           /// Nullable:False
           /// </summary>
        public string DirCode { get; set; }
           /// <summary>
           /// Desc:目录负责人ID
           /// Default:
           /// Nullable:True
           /// </summary>
        public string OwnerUserid { get; set; }
           /// <summary>
           /// Desc:删除标记(0-未删 1-已删)
           /// Default:0
           /// Nullable:True
           /// </summary>
        public int? Deleted { get; set; }
    }
}