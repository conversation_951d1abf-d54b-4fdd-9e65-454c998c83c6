using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.DFM.Model.ViewModels
{
    public class SopDocRequestModel : RequestPageModelBase
    {
        public SopDocRequestModel()
        {
        }
           /// <summary>
           /// Desc:所属目录ID
           /// Default:
           /// Nullable:False
           /// </summary>
        public string DirId { get; set; }
           /// <summary>
           /// Desc:文档名称
           /// Default:
           /// Nullable:False
           /// </summary>
        public string DocName { get; set; }
           /// <summary>
           /// Desc:文档编码(唯一)
           /// Default:
           /// Nullable:False
           /// </summary>
        public string DocCode { get; set; }
           /// <summary>
           /// Desc:Minio文件UUID
           /// Default:
           /// Nullable:False
           /// </summary>
        public string FileUuid { get; set; }
           /// <summary>
           /// Desc:文件存储路径
           /// Default:
           /// Nullable:False
           /// </summary>
        public string FilePath { get; set; }
           /// <summary>
           /// Desc:文件大小(字节)
           /// Default:
           /// Nullable:True
           /// </summary>
        public long? FileSize { get; set; }
           /// <summary>
           /// Desc:状态(1-生效 2-失效)
           /// Default:1
           /// Nullable:True
           /// </summary>
        public byte? DocStatus { get; set; }
           /// <summary>
           /// Desc:删除标记(0-未删 1-已删)
           /// Default:0
           /// Nullable:True
           /// </summary>
        public int? Deleted { get; set; }
           /// <summary>
           /// Desc:文档版本
           /// Default:
           /// Nullable:True
           /// </summary>
        public string DocVersion { get; set; }
    }
}