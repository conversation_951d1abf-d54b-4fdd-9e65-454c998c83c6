using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.DFM.Model.ViewModels
{
    public class SopPermApplyRequestModel : RequestPageModelBase
    {
        public SopPermApplyRequestModel()
        {
        }
           /// <summary>
           /// Desc:申请人ID
           /// Default:
           /// Nullable:False
           /// </summary>
        public string ApplicantId { get; set; }
           /// <summary>
           /// Desc:申请对象ID
           /// Default:
           /// Nullable:False
           /// </summary>
        public string TargetId { get; set; }
           /// <summary>
           /// Desc:对象类型(1-目录 2-文档)
           /// Default:
           /// Nullable:False
           /// </summary>
        public byte TargetType { get; set; }
           /// <summary>
           /// Desc:申请权限级别
           /// Default:
           /// Nullable:False
           /// </summary>
        public byte RequestPerm { get; set; }
           /// <summary>
           /// Desc:审批状态(0-待处理 1-通过 2-拒绝)
           /// Default:0
           /// Nullable:True
           /// </summary>
        public byte? ApproveStatus { get; set; }
           /// <summary>
           /// Desc:审批人ID
           /// Default:
           /// Nullable:True
           /// </summary>
        public string ApproverId { get; set; }
           /// <summary>
           /// Desc:审批时间
           /// Default:
           /// Nullable:True
           /// </summary>
        public DateTime? ApproveTime { get; set; }
           /// <summary>
           /// Desc:删除标记(0-未删 1-已删)
           /// Default:0
           /// Nullable:True
           /// </summary>
        public int? Deleted { get; set; }
    }
}