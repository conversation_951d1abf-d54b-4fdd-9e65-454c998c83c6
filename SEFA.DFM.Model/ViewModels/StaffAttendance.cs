using System;

namespace SEFA.DFM.Model.ViewModels;

public class StaffAttendance
{
    /// <summary>
    /// 员工工号
    /// </summary>
    public string StaffCode { get; set; }
    /// <summary>
    /// 员工类型(0-直接、1-间接)
    /// </summary>
    public string StaffType { get; set; }
    /// <summary>
    /// 班别（白班、夜班）
    /// </summary>
    public string Shift { get; set; }
    /// <summary>
    /// 排班-上班时间
    /// </summary>
    public DateTime PlanStartTime { get; set; }
    /// <summary>
    /// 排班-下班时间
    /// </summary>
    public  DateTime PlanEndTime { get; set; }
}