using SqlSugar;
using System;

namespace SEFA.DFM.Model.ViewModels
{
    public class StaffDto
    {
        /// <summary>
        /// Desc:ID
        /// </summary>
        public string ID { get; set; }

        /// <summary>
        /// 名称
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// Desc:工号
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// Desc:员工头像
        /// </summary>
        public string UserAvatar { get; set; }

        /// <summary>
        /// 人事子范围
        /// </summary>
        public string Region { get; set; }
        /// <summary>
        /// Desc:员工子组
        /// Default:
        /// Nullable:True
        /// </summary>
        public string StaffGroup { get; set; }
        /// <summary>
        /// Desc:厂区
        /// </summary>
        public string Factory { get; set; }
        /// <summary>
        /// Desc:业务类型描述
        /// </summary>
        public string BusinessDesc { get; set; }
        /// <summary>
        /// Desc:车间描述
        /// </summary>
        public string WorkshopDesc { get; set; }

        /// <summary>
        /// Desc:虚拟组织
        /// </summary>
        public string VirtualOrganization { get; set; }

        /// <summary>
        /// Desc:上级领导
        /// </summary>
        public string LeaderName { get; set; }
        /// <summary>
        /// Desc:上级领导工号
        /// </summary>
        public string LeaderCode { get; set; }

        /// <summary>
        /// Desc:UWB标签号
        /// </summary>
        public string Uwb { get; set; }
        /// <summary>
        /// Desc:职位
        /// </summary>
        public string Rank { get; set; }
        /// <summary>
        /// Desc:职称
        /// </summary>
        public string JobTitle { get; set; }

        /// <summary>
        /// Desc:星级
        /// </summary>
        public string StarLevel { get; set; }

        /// <summary>
        /// Desc:飞书
        /// </summary>
        public string Feishu { get; set; }


        /// <summary>
        /// 维修成本单价
        /// </summary>
        public decimal Price { get; set; }


        /// <summary>
        /// 维修成本单价
        /// </summary>
        public DateTime? StartValidTime { get; set; }


        /// <summary>
        /// 维修成本单价
        /// </summary>
        public DateTime? EndValidTime { get; set; }

    }
}
