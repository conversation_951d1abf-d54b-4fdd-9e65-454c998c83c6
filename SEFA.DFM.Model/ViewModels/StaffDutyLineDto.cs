using SqlSugar;

namespace SEFA.DFM.Model.ViewModels
{
    /// <summary>
    ///  人员负责产线 信息
    /// </summary>
    public class StaffDutyLineDto
    {
        /// <summary>
        /// 记录ID
        /// </summary>
        public string ID { get; set; }

        /// <summary>
        /// Desc:员工工号
        /// </summary>
        public string StaffCode { get; set; }
        /// <summary>
        /// Desc:员工名称
        /// </summary>
        public string StaffName { get; set; }

        /// <summary>
        /// Desc:产线Code
        /// </summary>
        public string LineId { get; set; }

        /// <summary>
        /// Desc:工段Id
        /// </summary>
        public string SegmentId { get; set; }


        /// <summary>
        /// Desc:计件岗位
        /// </summary>
        public string PricePost { get; set; }
        

        /// <summary>
        /// Desc:安灯岗位
        /// </summary>
        public string AndonPost { get; set; }

        /// <summary>
        /// Desc:工序
        /// </summary>
        public string Process { get; set; }

        /// <summary>
        /// Desc:类型
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// Desc:类型
        /// </summary>
        public string Type2 { get; set; }

        /// <summary>
        /// Desc:类型
        /// </summary>
        public string Type3 { get; set; }


        /// <summary>
        /// Desc:班制
        /// </summary>
        public decimal? WokrHours { get; set; }

        /// <summary>
        /// Desc:物料号
        /// </summary>
        public string MaterialCode { get; set; }

        /// <summary>
        /// 是否转班（固定白夜班）
        /// </summary>
        public int ChangeShifts { get; set; }

        /// <summary>
        /// 上级领导账号
        /// </summary>
        public string LeaderCode { get; set; }
        /// <summary>
        /// 上级领导名称
        /// </summary>
        public string LeaderName { get; set; }

    }
}
