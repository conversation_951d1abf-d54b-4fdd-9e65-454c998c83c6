using Magicodes.ExporterAndImporter.Core;

namespace SEFA.DFM.Model.ViewModels
{
    /// <summary>
    /// 导入、导出、视图模型
    /// </summary>
    public class StaffDutyLineExcelDto
    {

        /// <summary>
        /// Desc:员工工号
        /// </summary>
        [ImporterHeader(Name = "工号")]
        [ExporterHeader(DisplayName = "工号")]
        public string StaffCode { get; set; }
        /// <summary>
        /// Desc:员工名称
        /// </summary>
        [ImporterHeader(Name = "姓名")]
        [ExporterHeader(DisplayName = "姓名")]
        public string StaffName { get; set; }

        /// <summary>
        /// Desc:产线Code
        /// </summary>
        [ImporterHeader(Name = "线体")]
        [ExporterHeader(DisplayName = "线体")]
        public string LineCode { get; set; }

        /// <summary>
        /// Desc:工段简称
        /// </summary>
        [ImporterHeader(Name = "工段")]
        [ExporterHeader(DisplayName = "工段")]
        public string SegmentShortName { get; set; }


        /// <summary>
        /// Desc:计件岗位
        /// </summary>
        [ImporterHeader(Name = "计件岗位")]
        [ExporterHeader(DisplayName = "计件岗位")]
        public string PricePost { get; set; }
        

        /// <summary>
        /// Desc:安灯岗位
        /// </summary>
        [ImporterHeader(Name = "安灯岗位")]
        [ExporterHeader(DisplayName = "安灯岗位")]
        public string AndonPost { get; set; }

        /// <summary>
        /// Desc:工序
        /// </summary>
        [ImporterHeader(Name = "工序")]
        [ExporterHeader(DisplayName = "工序")]
        public string Process { get; set; }


        ///// <summary>
        ///// Desc:班组
        ///// </summary>
        //[ImporterHeader(Name = "班组")]
        //public string Team { get; set; }

        /// <summary>
        /// Desc:类型
        /// </summary>
        [ImporterHeader(Name = "员工分类")]
        [ExporterHeader(DisplayName = "员工分类")]
        public string Type { get; set; }


        /// <summary>
        /// Desc:类型
        /// </summary>
        [ImporterHeader(Name = "类型")]
        [ExporterHeader(DisplayName = "类型")]
        public string Type2 { get; set; }

        /// <summary>
        /// Desc:类型
        /// </summary>
        [ImporterHeader(Name = "类别")]
        [ExporterHeader(DisplayName = "类别")]
        public string Type3 { get; set; }

    }
}
