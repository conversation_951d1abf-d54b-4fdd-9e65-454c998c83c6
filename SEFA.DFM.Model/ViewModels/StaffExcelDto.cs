using Magicodes.ExporterAndImporter.Core;
using SqlSugar;
using System;
using System.Xml.Linq;

namespace SEFA.DFM.Model.Models
{

    ///<summary>
    ///员工信息导入Dto
    ///</summary>
    public class StaffExcelDto
    {
        /// <summary>
        /// 名称
        /// </summary>
        [ImporterHeader(Name = "姓名")]
        public string Name { get; set; }
        /// <summary>
        /// Desc:工号
        /// </summary>
        [ImporterHeader(Name = "工号")]
        public string Code { get; set; }

        /// <summary>
        /// 人事子范围
        /// </summary>
        [ImporterHeader(Name = "人事子范围")]
        public string Region { get; set; }
        /// <summary>
        /// Desc:员工子组
        /// </summary>
        [ImporterHeader(Name = "员工子组")]
        public string StaffGroup { get; set; }
        /// <summary>
        /// Desc:厂区
        /// </summary>
        [ImporterHeader(Name = "厂区")]
        public string Factory { get; set; }

        /// <summary>
        /// Desc:业务类型描述
        /// </summary>
        [ImporterHeader(Name = "业务类型描述")]

        public string BusinessDesc { get; set; }

        /// <summary>
        /// Desc:车间描述
        /// </summary>
        [ImporterHeader(Name = "车间描述")]
        public string WorkshopDesc { get; set; }

        /// <summary>
        /// Desc:虚拟组织
        /// </summary>
        [ImporterHeader(Name = "虚拟组织")]
        public string VirtualOrganization { get; set; }

        /// <summary>
        /// Desc:上级领导
        /// </summary>
        [ImporterHeader(Name = "上级领导")]
        public string LeaderName { get; set; }
        /// <summary>
        /// Desc:上级领导工号
        /// </summary>
        [ImporterHeader(Name = "上级领导工号")]
        public string LeaderCode { get; set; }

        /// <summary>
        /// Desc:UWB标签号
        /// </summary>
        [ImporterHeader(Name = "UWB标签号")]
        public string Uwb { get; set; }
        /// <summary>
        /// Desc:职位
        /// </summary>
        [ImporterHeader(Name = "职位")]
        public string Rank { get; set; }
        /// <summary>
        /// Desc:职称
        /// </summary>
        [ImporterHeader(Name = "职称")]
        public string JobTitle { get; set; }
        
        /// <summary>
        /// Desc:星级
        /// </summary>
        [ImporterHeader(Name = "星级")]
        public string StarLevel { get; set; }



        /// <summary>
        /// 维修成本单价
        /// </summary>
        [ImporterHeader(Name = "维修成本单价")]
        public decimal Price { get; set; }


        /// <summary>
        /// 成本单价起时间
        /// </summary>
        [ImporterHeader(Name = "维修成本单价起时间")]
        public DateTime? StartValidTime { get; set; }


        /// <summary>
        /// 成本单价止时间
        /// </summary>
        [ImporterHeader(Name = "维修成本单价止时间")]
        public DateTime? EndValidTime { get; set; }

    }
}