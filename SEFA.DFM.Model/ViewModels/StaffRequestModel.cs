using System.Collections.Generic;
using SEFA.Base.Model;

namespace SEFA.DFM.Model.ViewModels;

public class StaffRequestModel : RequestPageModelBase
{
    /// <summary>
    /// 产线ID
    /// </summary>
    public string LineId { get; set; }

    /// <summary>
    /// 岗位Code
    /// </summary>
    public List<string> PostCodes { get; set; }

    public string JobContent { get; set; }

    /// <summary>
    /// 员工名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 员工编号
    /// </summary>
    public string Code { get; set; }


    /// <summary>
    /// 员工编号集合
    /// </summary>
    public List<string> Codes { get; set; } = new List<string>();

    /// <summary>
    /// 员工部门
    /// </summary>
    public string DepId { get; set; }


    /// <summary>
    /// 产线/工段Id
    /// </summary>
    public string TreeId { get; set; }


    public string key { get; set; }
    public string UWB { get; set; }


    /// <summary>
    /// 是否查询所有
    /// </summary>
    public bool IsSelectAll { get; set; }

    /// <summary>
    /// 飞书
    /// </summary>
    public List<string> FeiShu { get; set; } = new List<string>();
}