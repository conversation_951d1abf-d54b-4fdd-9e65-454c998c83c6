using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;

namespace SEFA.DFM.Model.Models
{
    public class EquipmentGroupModel : EntityBase
    {

        /// <summary>
        /// Desc:类型
        /// Default:
        /// Nullable:False
        /// </summary>
        public string Type { get; set; }
        /// <summary>
        /// Desc:描述
        /// Default:
        /// Nullable:False
        /// </summary>
        public string Description { get; set; }
        /// <summary>
        /// Desc:分组父节点
        /// Default:
        /// Nullable:True
        /// </summary>
        public string ParentEquipmentGroupRowId { get; set; }
        /// <summary>
        /// Desc:排序
        /// Default:
        /// Nullable:True
        /// </summary>
        public string SortOrder { get; set; }
        /// <summary>
        /// Desc:状态
        /// Default:
        /// Nullable:False
        /// </summary>
        public string Status { get; set; }
        /// <summary>
        /// Desc:分组
        /// Default:
        /// Nullable:True
        /// </summary>
        public string ConsoleGroup { get; set; }
        /// <summary>
        /// Desc:设备名称集合
        /// Default:
        /// Nullable:False
        /// </summary>
        public string EquipmentNames { get; set; }

    }

    public class EquipmentGroupEquModel : EntityBase
    {

        /// <summary>
        /// Desc:类型
        /// Default:
        /// Nullable:False
        /// </summary>
        public string EquipmentGroupRowId { get; set; }
        /// <summary>
        /// Desc:ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string EquipmentRowId { get; set; }
        /// <summary>
        /// Desc:EquipmentCode
        /// Default:
        /// Nullable:True
        /// </summary>
        public string EquipmentCode{ get; set; }
        /// <summary>
        /// Desc:EquipmentName
        /// Default:
        /// Nullable:True
        /// </summary>
        public string EquipmentName { get; set; }
        /// <summary>
        /// Desc:Type
        /// Default:
        /// Nullable:True
        /// </summary>
        public string Type { get; set; }
        /// <summary>
        /// Desc:LineName
        /// Default:
        /// Nullable:True
        /// </summary>
        public string LineName { get; set; }
        /// <summary>
        /// Desc:LineName
        /// Default:
        /// Nullable:True
        /// </summary>
        public string LineId { get; set; }
        /// <summary>
        /// Desc:排序
        /// Default:
        /// Nullable:True
        /// </summary>
        public string SortOrder { get; set; }
        /// <summary>
        /// Desc:状态
        /// Default:
        /// Nullable:False
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// Desc:ParentId
        /// Default:
        /// Nullable:False
        /// </summary>
        public string ParentId { get; set; }
        

    }
}
