using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.DFM.Model.ViewModels
{
    public class EquipmentMaterialRequestModel : RequestPageModelBase
    {
        public EquipmentMaterialRequestModel()
        {
        }
      
        /// <summary>
        /// Desc:设备ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string EquipmentId { get; set; }
        /// <summary>
        /// Desc:设备名称
        /// Default:
        /// Nullable:False
        /// </summary>
        public string EquipmentName { get; set; }
        /// <summary>
        /// Desc:物料ID
        /// Default:
        /// Nullable:True
        /// </summary>
        public string MaterialId { get; set; }
           /// <summary>
           /// Desc:物料组ID
           /// Default:
           /// Nullable:True
           /// </summary>
        public string GroupId { get; set; }
           /// <summary>
           /// Desc:类型（是否包含）
           /// Default:
           /// Nullable:False
           /// </summary>
        public string Type { get; set; }
        /// <summary>
        /// Desc:状态（是否启用）
        /// Default:
        /// Nullable:False
        /// </summary>
        public int Status { get; set; } = 1;

        /// <summary>
        /// Desc:Code
        /// Default:
        /// Nullable:True
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// Desc:描述
        /// Default:
        /// Nullable:True
        /// </summary>
        public string Description { get; set; }
        /// <summary>
        /// Desc:映射类型
        /// Default:
        /// Nullable:True
        /// </summary>
        public string MappingType { get; set; }
        /// <summary>
        /// Desc:ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string Key { get; set; }
        /// <summary>
        /// Desc:ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string ID { get; set; }
        /// <summary>
        /// Desc:设备ID
        /// Default:
        /// Nullable:False
        /// </summary>

        public DateTime CreateDate { get; set; }

        public string CreateUserId { get; set; }

        public DateTime ModifyDate { get; set; }

        public string ModifyUserId { get; set; }

    }
}