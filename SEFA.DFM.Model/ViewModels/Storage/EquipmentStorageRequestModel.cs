using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.DFM.Model.ViewModels
{
    public class EquipmentStorageRequestModel : RequestPageModelBase
    {
        public EquipmentStorageRequestModel()
        {
        }
        /// <summary>
         /// Desc:Id
         /// Default:
         /// Nullable:False
         /// </summary>
        public string ID { get; set; }
        /// <summary>
        /// Desc:Id
        /// Default:
        /// Nullable:False
        /// </summary>
        public string EquipmentId { get; set; }
           /// <summary>
           /// Desc:存储节点ID
           /// Default:
           /// Nullable:True
           /// </summary>
        public string EquipmentRequirementId { get; set; }
           /// <summary>
           /// Desc:存储类型
           /// Default:
           /// Nullable:False
           /// </summary>
        public string StorageType { get; set; }
           /// <summary>
           /// Desc:最大批次数量
           /// Default:
           /// Nullable:True
           /// </summary>
        public string MaxLots { get; set; }
           /// <summary>
           /// Desc:最大唯一码数量
           /// Default:
           /// Nullable:True
           /// </summary>
        public string MaxSublots { get; set; }
           /// <summary>
           /// Desc:最大数量
           /// Default:
           /// Nullable:True
           /// </summary>
        public string MaxQuantity { get; set; }
           /// <summary>
           /// Desc:是否默认
           /// Default:
           /// Nullable:False
           /// </summary>
        public string IsDefault { get; set; }
           /// <summary>
           /// Desc:库存允许负值
           /// Default:
           /// Nullable:False
           /// </summary>
        public string AllowNegativeQuantity { get; set; }
           /// <summary>
           /// Desc:物料进仓模式
           /// Default:
           /// Nullable:False
           /// </summary>
        public string IncomingStorageMode { get; set; }
           /// <summary>
           /// Desc:物料出仓模式
           /// Default:
           /// Nullable:False
           /// </summary>
        public string OutgoingStorageMode { get; set; }
           /// <summary>
           /// Desc:物料类型
           /// Default:
           /// Nullable:False
           /// </summary>
        public string MaterialType { get; set; }
           /// <summary>
           /// Desc:上一种物料
           /// Default:
           /// Nullable:True
           /// </summary>
        public string LastMaterialId { get; set; }
           /// <summary>
           /// Desc:MES生成唯一码
           /// Default:
           /// Nullable:False
           /// </summary>
        public string MesSuManaged { get; set; }

    }
}