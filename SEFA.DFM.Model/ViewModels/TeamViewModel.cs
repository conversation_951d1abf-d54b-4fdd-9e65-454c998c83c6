using SEFA.Base.Model.BASE;
using SEFA.DFM.Model.Models;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SEFA.DFM.Model.ViewModels
{
    public class TeamViewModel : EntityBase
    {

        /// <summary>
        /// Desc:公司ID
        /// Default:
        /// Nullable:True
        /// </summary>
        public string Companyid { get; set; }
        /// <summary>
        /// Desc:部门ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string Departmentid { get; set; }
        /// <summary>
        /// Desc:名称
        /// Default:
        /// Nullable:True
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// Desc:打包班组编号
        /// Default:
        /// Nullable:True
        /// </summary>
        public string BOXTeamCode { get; set; }
        /// <summary>
        /// Desc:简称
        /// Default:
        /// Nullable:True
        /// </summary>
        public string Shortname { get; set; }
        /// <summary>
        /// Desc:工作班组
        /// Default:
        /// Nullable:True
        /// </summary>
        public int? Isworkteam { get; set; }
        /// <summary>
        /// Desc:序号
        /// Default:
        /// Nullable:True
        /// </summary>
        public int? Sequence { get; set; }
        /// <summary>
        /// Desc:启用标识
        /// Default:
        /// Nullable:True
        /// </summary>
        public int? Enabledmark { get; set; }
        /// <summary>
        /// Desc:描述
        /// Default:
        /// Nullable:True
        /// </summary>
        public string Description { get; set; }
        /// <summary>
        /// Desc:
        /// Default:0
        /// Nullable:False
        /// </summary>
        public int Deleted { get; set; }
        public List<EquipmentEntity> EQList { get; set; }

    }
}
