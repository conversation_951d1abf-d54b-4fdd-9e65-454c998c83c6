using SEFA.Base.Model.BASE;
using System;

namespace SEFA.DFM.Model.ViewModels
{
    public class UserInfoViewModel : EntityBase
    {
        /// <summary>
        /// Desc:登录名
        /// Default:
        /// Nullable:True
        /// </summary>

        public string LoginName { get; set; }

        /// <summary>
        /// Desc:密码
        /// Default:
        /// Nullable:True
        /// </summary>

        public string Password { get; set; }

        /// <summary>
        /// Desc:旧密码
        /// Default:
        /// Nullable:True
        /// </summary>

        public string OldPassword { get; set; }

        /// <summary>
        /// Desc:员工号
        /// Default:
        /// Nullable:True
        /// </summary> 
        public string UserNo { get; set; }

        /// <summary>
        /// Desc:用户姓名
        /// Default:
        /// Nullable:True
        /// </summary>

        public string UserName { get; set; }

        /// <summary>
        /// Desc:状态(0禁用，1启用)
        /// Default:1
        /// Nullable:True
        /// </summary>

        public int? Status { get; set; }

        /// <summary>
        /// Desc:备注
        /// Default:
        /// Nullable:True
        /// </summary>

        public string Remark { get; set; }

        /// <summary>
        /// Desc:性别
        /// Default:
        /// Nullable:True
        /// </summary>

        public int? Sex { get; set; }

        /// <summary>
        /// Desc:年龄
        /// Default:
        /// Nullable:True
        /// </summary>

        public int? Age { get; set; }

        /// <summary>
        /// Desc:生日
        /// Default:
        /// Nullable:True
        /// </summary>

        public string Birth { get; set; }

        /// <summary>
        /// Desc:公司ID
        /// Default:
        /// Nullable:True
        /// </summary>

        public string Companyid { get; set; }

        /// <summary>
        /// Desc:部门ID
        /// Default:
        /// Nullable:True
        /// </summary>

        public string Departmentid { get; set; }

        /// <summary>
        /// Desc:产线ID
        /// Default:
        /// Nullable:True
        /// </summary>

        public string Productlineid { get; set; }

        /// <summary>
        /// Desc:岗位ID
        /// Default:
        /// Nullable:True
        /// </summary>

        public string Postid { get; set; }

        /// <summary>
        /// Desc:班组ID
        /// Default:
        /// Nullable:True
        /// </summary>

        public string Teamid { get; set; }

        /// <summary>
        /// Desc:电话
        /// Default:
        /// Nullable:True
        /// </summary>

        public string Tel { get; set; }

        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>

        public string WechatNo { get; set; }

        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:True
        /// </summary>

        public int? ErrorCount { get; set; }

        /// <summary>
        /// Desc:
        /// Default:0
        /// Nullable:False
        /// </summary>

        public int Locked { get; set; }

        /// <summary>
        /// Desc:
        /// Default:1
        /// Nullable:False
        /// </summary>

        public int Enable { get; set; }

        /// <summary>
        /// Desc:是否已删除
        /// Default:0
        /// Nullable:False
        /// </summary>

        public int Deleted { get; set; }

        /// <summary>
        /// Desc:邮箱
        /// Default:0
        /// Nullable:False
        /// </summary>
        public string EMAIL { get; set; }

        public string LEVEL { get; set; }
        public string CompanyName { get; set; }
        public string DepartmentName { get; set; }
        public string PostName { get; set; }

        public string UserRoleId { get; set; }

        /// <summary>
        /// Desc:修改密码时间
        /// Default:
        /// Nullable:True
        /// </summary>
        public DateTime? LastModifyPassword { get; set; }

        /// <summary>
        /// Desc:域用户标识：0-否，1-是
        /// Default:
        /// Nullable:True
        /// </summary>
        public string IsDomainUser { get; set; }
    }
}