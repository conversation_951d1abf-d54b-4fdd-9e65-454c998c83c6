using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// ActionPropertyRepository
	/// </summary>
    public class ActionPropertyRepository : BaseRepository<ActionPropertyEntity>, IActionPropertyRepository
    {
        public ActionPropertyRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}