using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// CompanyMappingRepository
	/// </summary>
    public class CompanyMappingRepository : BaseRepository<CompanyMappingEntity>, ICompanyMappingRepository
    {
        public CompanyMappingRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}