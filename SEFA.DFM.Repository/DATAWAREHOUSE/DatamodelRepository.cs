using SEFA.DFM.IRepository.DATAWAREHOUSE;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models.DATAWAREHOUSE;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository.DATAWAREHOUSE
{
	/// <summary>
	/// DatamodelRepository
	/// </summary>
    public class DatamodelRepository : BaseRepository<DatamodelEntity>, IDatamodelRepository
    {
        public DatamodelRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}