using SEFA.DFM.IRepository.DATAWAREHOUSE;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models.DATAWAREHOUSE;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository.DATAWAREHOUSE
{
	/// <summary>
	/// RelMaterialTagRepository
	/// </summary>
    public class RelMaterialTagRepository : BaseRepository<RelMaterialTagEntity>, IRelMaterialTagRepository
    {
        public RelMaterialTagRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}