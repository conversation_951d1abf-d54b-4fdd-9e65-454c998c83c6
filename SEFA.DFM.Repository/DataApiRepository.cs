using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// DataApiRepository
	/// </summary>
    public class DataApiRepository : BaseRepository<DataApiEntity>, IDataApiRepository
    {
        public DataApiRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}