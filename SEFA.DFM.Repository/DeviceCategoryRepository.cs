using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// DeviceCategoryRepository
	/// </summary>
    public class DeviceCategoryRepository : BaseRepository<DeviceCategoryEntity>, IDeviceCategoryRepository
    {
        public DeviceCategoryRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}