using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// EnergyMappingRepository
	/// </summary>
    public class EnergyMappingRepository : BaseRepository<EnergyMappingEntity>, IEnergyMappingRepository
    {
        public EnergyMappingRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}