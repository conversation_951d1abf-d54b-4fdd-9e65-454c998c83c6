using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// EquipAssocInterlockRepository
	/// </summary>
    public class EquipAssocInterlockRepository : BaseRepository<EquipAssocInterlockEntity>, IEquipAssocInterlockRepository
    {
        public EquipAssocInterlockRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}