using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// EquipmentActionRepository
	/// </summary>
    public class EquipmentActionRepository : BaseRepository<EquipmentActionEntity>, IEquipmentActionRepository
    {
        public EquipmentActionRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}