using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// EquipmentClassAssocRepository
	/// </summary>
    public class EquipmentClassAssocRepository : BaseRepository<EquipmentClassAssocEntity>, IEquipmentClassAssocRepository
    {
        public EquipmentClassAssocRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}