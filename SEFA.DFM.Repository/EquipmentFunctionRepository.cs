using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// EquipmentFunctionRepository
	/// </summary>
    public class EquipmentFunctionRepository : BaseRepository<EquipmentFunctionEntity>, IEquipmentFunctionRepository
    {
        public EquipmentFunctionRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}