using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// EquipmentPropertyValueRepository
	/// </summary>
    public class EquipmentPropertyValueRepository : BaseRepository<EquipmentPropertyValueEntity>, IEquipmentPropertyValueRepository
    {
        public EquipmentPropertyValueRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}