using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// EquipmentattrRepository
	/// </summary>
    public class EquipmentattrRepository : BaseRepository<EquipmentattrEntity>, IEquipmentattrRepository
    {
        public EquipmentattrRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}