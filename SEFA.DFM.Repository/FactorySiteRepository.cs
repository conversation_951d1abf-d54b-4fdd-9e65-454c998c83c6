using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// FactorySiteRepository
	/// </summary>
    public class FactorySiteRepository : BaseRepository<FactorySiteEntity>, IFactorySiteRepository
    {
        public FactorySiteRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}