using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// FunctionPropertyRepository
	/// </summary>
    public class FunctionPropertyRepository : BaseRepository<FunctionPropertyEntity>, IFunctionPropertyRepository
    {
        public FunctionPropertyRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}