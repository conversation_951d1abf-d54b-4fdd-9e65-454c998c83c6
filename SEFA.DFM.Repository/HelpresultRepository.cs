using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// HelpresultRepository
	/// </summary>
    public class HelpresultRepository : BaseRepository<HelpresultEntity>, IHelpresultRepository
    {
        public HelpresultRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}