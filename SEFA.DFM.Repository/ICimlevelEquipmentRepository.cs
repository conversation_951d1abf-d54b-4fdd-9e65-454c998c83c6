using SEFA.Base.IRepository.Base;
using SEFA.DFM.Model.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SEFA.DFM.IRepository
{
	/// <summary>
	/// ICimlevelEquipmentRepository
	/// </summary>	
    public interface ICimlevelEquipmentRepository : IBaseRepository<CimlevelEquipmentEntity>
    {

        public Task<List<EquipmentEntity>> GetEquipmentList(string CimID);
        
    }
}