using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace SEFA.DFM.IRepository
{
    /// <summary>
    /// IDataItemRepository
    /// </summary>	
    public interface IDataItemDetailRepository : IBaseRepository<DataItemDetailEntity>
    {

        public Task<List<DataItemDetailEntity>> GetBy(string itemCode,string lang);
        Task<PageModel<DataItemDetailEntity>> QueryPageList(Expression<Func<DataItemDetailEntity, DataItemEntity, bool>> whereExpression, int page, int intPageSize);
    }
}