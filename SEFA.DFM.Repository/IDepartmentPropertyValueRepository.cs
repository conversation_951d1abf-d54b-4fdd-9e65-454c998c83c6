using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace SEFA.DFM.IRepository
{
	/// <summary>
	/// IDepartmentPropertyValueRepository
	/// </summary>	
    public interface IDepartmentPropertyValueRepository : IBaseRepository<DepartmentPropertyValueEntity>
    {
        public Task<List<DepartmentPropertyValueModel>> GetBy(string itemCode, string lang);
        Task<PageModel<DepartmentPropertyValueModel>> QueryPageList(Expression<Func<DepartmentPropertyValueEntity, ClassEntity, bool>> whereExpression, int page, int intPageSize);
    }

}