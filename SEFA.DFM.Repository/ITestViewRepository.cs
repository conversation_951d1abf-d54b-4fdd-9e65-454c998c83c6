using SEFA.Base.IRepository.Base;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SEFA.DFM.IRepository
{
	/// <summary>
	/// IPostRepository
	/// </summary>	
    public interface IOrderRequireRepository : IBaseRepository<OrderRequireEntity>
    {
        public Task<List<RoutingModel>> GetRoutingByMaterial(string materialCode);
    }
}