using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// InterlockDefinitionRepository
	/// </summary>
    public class InterlockDefinitionRepository : BaseRepository<InterlockDefinitionEntity>, IInterlockDefinitionRepository
    {
        public InterlockDefinitionRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}