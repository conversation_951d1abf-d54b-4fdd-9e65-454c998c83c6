using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// InterlockLogRepository
	/// </summary>
    public class InterlockLogRepository : BaseRepository<InterlockLogEntity>, IInterlockLogRepository
    {
        public InterlockLogRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}