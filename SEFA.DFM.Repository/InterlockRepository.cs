using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// InterlockRepository
	/// </summary>
    public class InterlockRepository : BaseRepository<InterlockEntity>, IInterlockRepository
    {
        public InterlockRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}