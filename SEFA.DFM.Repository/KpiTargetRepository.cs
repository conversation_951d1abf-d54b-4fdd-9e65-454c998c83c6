using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// KpiTargetRepository
	/// </summary>
    public class KpiTargetRepository : BaseRepository<KpiTargetEntity>, IKpiTargetRepository
    {
        public KpiTargetRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}