using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// LabelCountryMOverrideRepository
	/// </summary>
    public class LabelCountryMOverrideRepository : BaseRepository<LabelCountryMOverrideEntity>, ILabelCountryMOverrideRepository
    {
        public LabelCountryMOverrideRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}