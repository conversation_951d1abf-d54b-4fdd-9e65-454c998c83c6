using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// LabelEquipmentPrinterRepository
	/// </summary>
    public class LabelEquipmentPrinterRepository : BaseRepository<LabelEquipmentPrinterEntity>, ILabelEquipmentPrinterRepository
    {
        public LabelEquipmentPrinterRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}