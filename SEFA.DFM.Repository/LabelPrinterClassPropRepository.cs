using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// LabelPrinterClassPropRepository
	/// </summary>
    public class LabelPrinterClassPropRepository : BaseRepository<LabelPrinterClassPropEntity>, ILabelPrinterClassPropRepository
    {
        public LabelPrinterClassPropRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}