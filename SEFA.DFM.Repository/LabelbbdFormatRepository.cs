using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// LabelbbdFormatRepository
	/// </summary>
    public class LabelbbdFormatRepository : BaseRepository<LabelbbdFormatEntity>, ILabelbbdFormatRepository
    {
        public LabelbbdFormatRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}