using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// MaterialBomRepository
	/// </summary>
    public class MaterialBomRepository : BaseRepository<MaterialBomEntity>, IMaterialBomRepository
    {
        public MaterialBomRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}