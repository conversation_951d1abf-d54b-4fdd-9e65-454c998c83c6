using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// MaterialPropertyTypeRepository
	/// </summary>
    public class MaterialPropertyTypeRepository : BaseRepository<MaterialPropertyTypeEntity>, IMaterialPropertyTypeRepository
    {
        public MaterialPropertyTypeRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}