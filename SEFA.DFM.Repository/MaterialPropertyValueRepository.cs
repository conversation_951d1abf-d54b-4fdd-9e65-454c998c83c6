using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// MaterialPropertyValueRepository
	/// </summary>
    public class MaterialPropertyValueRepository : BaseRepository<MaterialPropertyValueEntity>, IMaterialPropertyValueRepository
    {
        public MaterialPropertyValueRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}