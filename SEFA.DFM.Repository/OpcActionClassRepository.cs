using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// OpcActionClassRepository
	/// </summary>
    public class OpcActionClassRepository : BaseRepository<OpcActionClassEntity>, IOpcActionClassRepository
    {
        public OpcActionClassRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}