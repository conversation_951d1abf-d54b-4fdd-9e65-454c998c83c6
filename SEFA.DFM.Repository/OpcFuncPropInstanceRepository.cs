using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// OpcFuncPropInstanceRepository
	/// </summary>
    public class OpcFuncPropInstanceRepository : BaseRepository<OpcFuncPropInstanceEntity>, IOpcFuncPropInstanceRepository
    {
        public OpcFuncPropInstanceRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}