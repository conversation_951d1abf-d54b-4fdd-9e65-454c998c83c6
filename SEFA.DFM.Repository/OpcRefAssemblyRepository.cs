using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// OpcRefAssemblyRepository
	/// </summary>
    public class OpcRefAssemblyRepository : BaseRepository<OpcRefAssemblyEntity>, IOpcRefAssemblyRepository
    {
        public OpcRefAssemblyRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}