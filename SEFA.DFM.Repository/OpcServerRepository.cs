using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// OpcServerRepository
	/// </summary>
    public class OpcServerRepository : BaseRepository<OpcServerEntity>, IOpcServerRepository
    {
        public OpcServerRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}