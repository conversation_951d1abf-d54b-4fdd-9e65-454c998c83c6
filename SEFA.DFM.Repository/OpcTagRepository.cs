using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// OpcTagRepository
	/// </summary>
    public class OpcTagRepository : BaseRepository<OpcTagEntity>, IOpcTagRepository
    {
        public OpcTagRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}