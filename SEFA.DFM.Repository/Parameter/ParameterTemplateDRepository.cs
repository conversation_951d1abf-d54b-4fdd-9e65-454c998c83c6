using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// ParameterTemplateDRepository
	/// </summary>
    public class ParameterTemplateDRepository : BaseRepository<ParameterTemplateDEntity>, IParameterTemplateDRepository
    {
        public ParameterTemplateDRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}