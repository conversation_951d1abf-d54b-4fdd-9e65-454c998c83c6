using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// ParameterTemplateRepository
	/// </summary>
    public class ParameterTemplateRepository : BaseRepository<ParameterTemplateEntity>, IParameterTemplateRepository
    {
        public ParameterTemplateRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}