using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// PhaseSalescontainerRepository
	/// </summary>
    public class PhaseSalescontainerRepository : BaseRepository<PhaseSalescontainerEntity>, IPhaseSalescontainerRepository
    {
        public PhaseSalescontainerRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}