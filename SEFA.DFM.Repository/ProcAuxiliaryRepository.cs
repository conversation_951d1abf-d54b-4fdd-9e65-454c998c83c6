using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// ProcAuxiliaryRepository
	/// </summary>
    public class ProcAuxiliaryRepository : BaseRepository<ProcAuxiliaryEntity>, IProcAuxiliaryRepository
    {
        public ProcAuxiliaryRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}