using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// ProcCutterRepository
	/// </summary>
    public class ProcCutterRepository : BaseRepository<ProcCutterEntity>, IProcCutterRepository
    {
        public ProcCutterRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}