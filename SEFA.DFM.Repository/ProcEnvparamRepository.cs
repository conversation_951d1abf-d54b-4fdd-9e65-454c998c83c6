using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// ProcEnvparamRepository
	/// </summary>
    public class ProcEnvparamRepository : BaseRepository<ProcEnvparamEntity>, IProcEnvparamRepository
    {
        public ProcEnvparamRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}