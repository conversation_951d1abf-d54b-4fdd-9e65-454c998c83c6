using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// ProcFixturesRepository
	/// </summary>
    public class ProcFixturesRepository : BaseRepository<ProcFixturesEntity>, IProcFixturesRepository
    {
        public ProcFixturesRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}