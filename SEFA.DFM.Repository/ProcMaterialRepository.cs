using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// ProcMaterialRepository
	/// </summary>
    public class ProcMaterialRepository : BaseRepository<ProcMaterialEntity>, IProcMaterialRepository
    {
        public ProcMaterialRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}