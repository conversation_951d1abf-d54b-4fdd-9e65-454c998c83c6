using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// ProcProgramDetailRepository
	/// </summary>
    public class ProcProgramDetailRepository : BaseRepository<ProcProgramDetailEntity>, IProcProgramDetailRepository
    {
        public ProcProgramDetailRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}