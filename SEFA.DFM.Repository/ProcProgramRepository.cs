using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// ProcProgramRepository
	/// </summary>
    public class ProcProgramRepository : BaseRepository<ProcProgramEntity>, IProcProgramRepository
    {
        public ProcProgramRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}