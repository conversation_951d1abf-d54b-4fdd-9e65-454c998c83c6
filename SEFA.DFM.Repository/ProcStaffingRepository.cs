using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// ProcStaffingRepository
	/// </summary>
    public class ProcStaffingRepository : BaseRepository<ProcStaffingEntity>, IProcStaffingRepository
    {
        public ProcStaffingRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}