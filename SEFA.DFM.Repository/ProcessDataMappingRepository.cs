using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// ProcessDataMappingRepository
	/// </summary>
    public class ProcessDataMappingRepository : BaseRepository<ProcessDataMappingEntity>, IProcessDataMappingRepository
    {
        public ProcessDataMappingRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}