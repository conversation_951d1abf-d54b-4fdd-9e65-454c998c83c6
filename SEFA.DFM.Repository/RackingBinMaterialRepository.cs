using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;
using System.Threading.Tasks;
using System.Collections.Generic;
using SqlSugar;

namespace SEFA.DFM.Repository
{
    /// <summary>
    /// RackingBinMaterialRepository
    /// </summary>
    public class RackingBinMaterialRepository : BaseRepository<RackingBinMaterialEntity>, IRackingBinMaterialRepository
    {
        public RackingBinMaterialRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
       
    }
}