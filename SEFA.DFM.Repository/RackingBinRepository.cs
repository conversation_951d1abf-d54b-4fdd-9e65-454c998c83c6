using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// RackingBinRepository
	/// </summary>
    public class RackingBinRepository : BaseRepository<RackingBinEntity>, IRackingBinRepository
    {
        public RackingBinRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}