using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;
using SqlSugar;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SEFA.DFM.Repository
{
    /// <summary>
    /// RackingRepository
    /// </summary>
    public class RackingRepository : BaseRepository<RackingEntity>, IRackingRepository
    {
        public RackingRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
        public async Task<List<RackingValueModel>> GetTree(string itemCode)
        {
            List<RackingValueModel> l = await QueryTwoTables
         <RackingEntity, RackingBinEntity, RackingValueModel>(
        (r, m) => new object[] {
                    JoinType.Inner,m.RackingId ==  r.ID
        },

       (r, m) => new RackingValueModel()
       {
           ID = m.RackingId,
           Code = m.BinCode,
           Name = m.BinName,
           ClassType = m.BinType,
           RackingID=r.ID,
       },

        (r, m) => r.Deleted == 0 && m.Deleted == 0);
            return l;
        }
    }
}