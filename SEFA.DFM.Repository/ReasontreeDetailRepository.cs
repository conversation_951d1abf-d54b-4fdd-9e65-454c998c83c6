using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// ReasontreeDetailRepository
	/// </summary>
    public class ReasontreeDetailRepository : BaseRepository<ReasontreeDetailEntity>, IReasontreeDetailRepository
    {
        public ReasontreeDetailRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}