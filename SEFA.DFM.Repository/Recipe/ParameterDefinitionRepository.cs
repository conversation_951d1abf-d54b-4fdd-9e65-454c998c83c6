using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// ParameterDefinitionRepository
	/// </summary>
    public class ParameterDefinitionRepository : BaseRepository<ParameterDefinitionEntity>, IParameterDefinitionRepository
    {
        public ParameterDefinitionRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}