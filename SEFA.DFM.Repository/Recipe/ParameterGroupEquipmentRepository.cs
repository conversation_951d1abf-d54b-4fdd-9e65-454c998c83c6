using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// ParameterGroupEquipmentRepository
	/// </summary>
    public class ParameterGroupEquipmentRepository : BaseRepository<ParameterGroupEquipmentEntity>, IParameterGroupEquipmentRepository
    {
        public ParameterGroupEquipmentRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}