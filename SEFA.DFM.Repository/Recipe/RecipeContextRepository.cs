using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// RecipeContextRepository
	/// </summary>
    public class RecipeContextRepository : BaseRepository<RecipeContextEntity>, IRecipeContextRepository
    {
        public RecipeContextRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}