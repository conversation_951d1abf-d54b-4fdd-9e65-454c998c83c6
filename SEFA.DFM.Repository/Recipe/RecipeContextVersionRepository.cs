using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// RecipeContextVersionRepository
	/// </summary>
    public class RecipeContextVersionRepository : BaseRepository<RecipeContextVersionEntity>, IRecipeContextVersionRepository
    {
        public RecipeContextVersionRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}