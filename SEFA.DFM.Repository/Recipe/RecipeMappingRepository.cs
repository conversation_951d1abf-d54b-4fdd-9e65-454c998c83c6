using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// RecipeMappingRepository
	/// </summary>
    public class RecipeMappingRepository : BaseRepository<RecipeMappingEntity>, IRecipeMappingRepository
    {
        public RecipeMappingRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}