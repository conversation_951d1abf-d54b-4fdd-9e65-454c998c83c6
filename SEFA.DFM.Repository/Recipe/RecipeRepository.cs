using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// RecipeRepository
	/// </summary>
    public class RecipeRepository : BaseRepository<RecipeEntity>, IRecipeRepository
    {
        public RecipeRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}