using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// RecipeSectionRepository
	/// </summary>
    public class RecipeSectionRepository : BaseRepository<RecipeSectionEntity>, IRecipeSectionRepository
    {
        public RecipeSectionRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}