using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;
using System.Threading.Tasks;
using System.Collections.Generic;
using SqlSugar;

namespace SEFA.DFM.Repository
{
    /// <summary>
    /// RolePermissionRepository
    /// </summary>
    public class RolePermissionRepository : BaseRepository<RolePermissionEntity>, IRolePermissionRepository
    {
        public RolePermissionRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }

        public async Task<List<MenuEntity>> GetPermissionListByRoleId(UserRoleEntity roleEntity)
        {
            List<MenuEntity> l = await QueryTwoTables
                <MenuEntity, RolePermissionEntity, MenuEntity>(
                (r, m) => new object[] {
                    JoinType.Inner, r.ID == m.MouduleId
                },

                (r, m) => new MenuEntity()
                {
                    ID = r.ID,
                    ParentId = m.ID
                },

                (r, m) => r.Deleted == 0 && m.Deleted == 0 && m.RoleId == roleEntity.RoleId && r.ParentId != "0"
                );
            return l;
        }
    }
}