using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;
using System.Threading.Tasks;
using System.Collections.Generic;
using SqlSugar;
using System.Linq;

namespace SEFA.DFM.Repository
{
    /// <summary>
    /// RoleRepository
    /// </summary>
    public class RoleRepository : BaseRepository<RoleEntity>, IRoleRepository
    {
        public RoleRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
        /// <summary>
        /// 获取角色用户关联信息
        /// </summary>
        /// <returns></returns>
        public async Task<List<UserinfoEntity>> GetUserRole()
        {
            List<UserinfoEntity> l = await QueryTwoTables
                <UserinfoEntity, UserRoleEntity, UserinfoEntity>(
                (r, m) => new object[] {
                    JoinType.Inner, r.ID == m.UserId
                },

                (r, m) => new UserinfoEntity()
                {
                    ID = m.ID,
                    LoginName = r.LoginName,
                    Departmentid = m.RoleId
                    // UserName = m.UserName,

                },

                (r, m) => r.Deleted == 0 && m.Deleted == 0
                );
            return l;
        }



        /// <summary>
        /// 获取角色用户关联信息
        /// </summary>
        /// <returns></returns>
        public async Task<List<MenuEntity>> GetRolePermission(string menuCategory = "Web")
        {
            List<MenuEntity> l = await QueryTwoTables
                <MenuEntity, RolePermissionEntity, MenuEntity>(
                (r, m) => new object[] {
                    JoinType.Inner, r.ID == m.MouduleId
                },

                (r, m) => new MenuEntity()
                {
                    ID = m.ID,
                    CnName = r.CnName,
                    ParentId = m.RoleId
                    // UserName = m.UserName,

                },

                (r, m) => r.Deleted == 0 && m.Deleted == 0 && r.Category == menuCategory
                );
            return l;
        }

        public async Task<List<MenuEntity>> GetRolePermissionByRoleId(List<string> roleid, string menuCategory = "Web")
        {
            List<MenuEntity> l = await QueryTwoTables
                <MenuEntity, RolePermissionEntity, MenuEntity>(
                (r, m) => new object[] {
                    JoinType.Inner, r.ID == m.MouduleId
                },

                (r, m) => new MenuEntity()
                {
                    ID = r.ID,
                    CnName = r.CnName,
                    ParentId = r.ParentId,
                    CnCode = r.CnCode,
                    Code = r.Code,
                    CreateDate = r.CreateDate,
                    Icon = r.Icon,
                    MEMU_TYPE = r.MEMU_TYPE,
                    Category = r.Category,
                    Route = r.Route,
                    Seq = r.Seq,
                    EnName = r.EnName,
                    TcName = r.TcName,
                    Remark = r.Remark,
                    Enable = r.Enable,
                    IsKeepAlive = r.IsKeepAlive,
                    IsHide = r.IsHide,
                    CreateUserId = r.CreateUserId
                },
                (r, m) => r.Deleted == 0 && m.Deleted == 0 && roleid.Contains(m.RoleId) && r.Enable == 1 && r.Category == menuCategory);

            var endlist = l
                .GroupBy(p => new { p.ID })
                .Select(g => g.First())
                .ToList();
            // 去重操作
            return endlist;
        }


    }
}