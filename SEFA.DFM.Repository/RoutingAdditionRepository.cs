using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// RoutingAdditionRepository
	/// </summary>
    public class RoutingAdditionRepository : BaseRepository<RoutingAdditionEntity>, IRoutingAdditionRepository
    {
        public RoutingAdditionRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}