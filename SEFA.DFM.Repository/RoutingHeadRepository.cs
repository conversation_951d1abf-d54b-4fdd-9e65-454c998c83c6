using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// RoutingHeadRepository
	/// </summary>
    public class RoutingHeadRepository : BaseRepository<RoutingHeadEntity>, IRoutingHeadRepository
    {
        public RoutingHeadRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }

    }
}