using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// RoutingPathRepository
	/// </summary>
    public class RoutingPathRepository : BaseRepository<RoutingPathEntity>, IRoutingPathRepository
    {
        public RoutingPathRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}