using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// RoutingProductRepository
	/// </summary>
    public class RoutingProductRepository : BaseRepository<RoutingProductEntity>, IRoutingProductRepository
    {
        public RoutingProductRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}