using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// BatchRelationRepository
	/// </summary>
    public class BatchRelationRepository : BaseRepository<BatchRelationEntity>, IBatchRelationRepository
    {
        public BatchRelationRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}