using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// SegmentPropertyRepository
	/// </summary>
    public class SegmentPropertyRepository : BaseRepository<SegmentPropertyEntity>, ISegmentPropertyRepository
    {
        public SegmentPropertyRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}