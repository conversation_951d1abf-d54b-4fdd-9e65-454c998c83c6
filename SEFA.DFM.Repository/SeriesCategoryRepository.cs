using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// SeriesCategoryRepository
	/// </summary>
    public class SeriesCategoryRepository : BaseRepository<SeriesCategoryEntity>, ISeriesCategoryRepository
    {
        public SeriesCategoryRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}