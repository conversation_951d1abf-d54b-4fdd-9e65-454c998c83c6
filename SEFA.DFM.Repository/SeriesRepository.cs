using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// SeriesRepository
	/// </summary>
    public class SeriesRepository : BaseRepository<SeriesEntity>, ISeriesRepository
    {
        public SeriesRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}