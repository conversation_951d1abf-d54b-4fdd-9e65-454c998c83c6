using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// ShifttimeRepository
	/// </summary>
    public class ShifttimeRepository : BaseRepository<ShifttimeEntity>, IShifttimeRepository
    {
        public ShifttimeRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}