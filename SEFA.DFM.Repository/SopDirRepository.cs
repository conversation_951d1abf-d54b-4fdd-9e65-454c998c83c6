using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// SopDirRepository
	/// </summary>
    public class SopDirRepository : BaseRepository<SopDirEntity>, ISopDirRepository
    {
        public SopDirRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}