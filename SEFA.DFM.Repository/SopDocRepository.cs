using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// SopDocRepository
	/// </summary>
    public class SopDocRepository : BaseRepository<SopDocEntity>, ISopDocRepository
    {
        public SopDocRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}