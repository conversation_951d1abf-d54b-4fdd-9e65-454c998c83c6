using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// EquipmentCapacityRepository
	/// </summary>
    public class EquipmentCapacityRepository : BaseRepository<EquipmentCapacityEntity>, IEquipmentCapacityRepository
    {
        public EquipmentCapacityRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}