using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// SupplierRepository
	/// </summary>
    public class SupplierRepository : BaseRepository<SupplierEntity>, ISupplierRepository
    {
        public SupplierRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}