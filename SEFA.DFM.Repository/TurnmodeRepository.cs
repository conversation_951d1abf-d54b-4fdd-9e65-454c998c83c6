using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// TurnmodeRepository
	/// </summary>
    public class TurnmodeRepository : BaseRepository<TurnmodeEntity>, ITurnmodeRepository
    {
        public TurnmodeRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}