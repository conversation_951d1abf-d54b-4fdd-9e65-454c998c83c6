using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// UnitConvertRepository
	/// </summary>
    public class UnitConvertRepository : BaseRepository<UnitConvertEntity>, IUnitConvertRepository
    {
        public UnitConvertRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}