using SEFA.DFM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.DFM.Repository
{
	/// <summary>
	/// UnitmanageRepository
	/// </summary>
    public class UnitmanageRepository : BaseRepository<UnitmanageEntity>, IUnitmanageRepository
    {
        public UnitmanageRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}