
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SEFA.DFM.Model.ViewModels;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using System.Net.Http;
using SEFA.DFM.Model;

namespace SEFA.DFM.Services
{
    public class ActionMainServices : ActionServiceBase, IActionMainServices
    {
        public ActionMainServices(IBaseRepository<ActionEntity> dal) : base(dal) { }

        public async Task<MessageModel<ActionResponse>> Process(ActionRequest request, HttpContext httpContext)
        {
            var result = await CheckRequest(request, httpContext);
            if (result.Response.Result == false)
            {
                return SetReturnData(result);
            }

            result = await InvokeFunc(result);

            return SetReturnData(result);
        }
    }
}