using Microsoft.AspNetCore.Http;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SEFA.Base.Services.BASE;
using SEFA.DFM.IServices;
using SEFA.DFM.Model;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Services;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace SEFA.DFM.Services
{
    /// <summary>
    /// 功能对应Action服务基类
    /// </summary>
    public abstract class ActionServiceBase : BaseServices<ActionEntity>
    {
        public ActionContext _context;

        public HttpContext _httpContext;

        public readonly IBaseRepository<ActionEntity> _dal;

        private object mainActionInstance = null;

        public ActionServiceBase(IBaseRepository<ActionEntity> dal) : base()
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        /// <summary>
        /// 设置返回数据
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        protected MessageModel<ActionResponse> SetReturnData(ActionContext context)
        {
            var ret = new MessageModel<ActionResponse>();
            ret.success = context.Response.Result;
            ret.msg = context.Response.Message;
            ret.response = context.Response;
            return ret;
        }

        /// <summary>
        /// 获取服务对象
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        protected object? GetService(Type type)
        {
            return _httpContext.RequestServices.GetService(typeof(IProductNewServices));
        }

        protected ActionContext SetResponse(bool result = true, string errorCode = "", string msg = "")
        {
            _context.Response.Result = result;
            _context.Response.ErrorCode = errorCode;
            _context.Response.Message = msg;
            return _context;
        }

        protected ActionContext SetResponse(bool result = true, string errorCode = "", string msgCode = "", params string[] msgParams)
        {
            _context.Response.Result = result;
            _context.Response.ErrorCode = errorCode;
            //TODO 根据MSG Code获取MSG 模板内容；
            string msgFormat = "{0}:{1}";
            _context.Response.Message = string.Format(msgFormat, msgParams);
            return _context;
        }

        protected ActionContext SetResponseFail(string errorCode = "", string msg = "")
        {
            return SetResponse(false, errorCode, msg);
        }

        protected ActionContext SetResponseFail(string errorCode = "", string msgCode = "", params string[] msgParams)
        {
            return SetResponse(false, errorCode, msgCode, msgParams);
        }

        protected ActionContext SetResponseSuccess(string errorCode = "", string msg = "")
        {
            return SetResponse(true, errorCode, msg);
        }

        protected ActionContext SetResponseSuccess(string errorCode = "", string msgCode = "", params string[] msgParams)
        {
            return SetResponse(true, errorCode, msgCode, msgParams);
        }

        /// <summary>
        /// 检查传入参数
        /// </summary>
        /// <param name="request"></param>
        /// <param name="httpContext"></param>
        /// <returns></returns>
        protected async Task<ActionContext> CheckRequest(ActionRequest request, HttpContext httpContext)
        {
            _context = new ActionContext();
            _context.Requst = request;
            _httpContext = httpContext;

            if (request == null)
            {
                return SetResponseFail("-1", $"Request is null.");
            }

            if (string.IsNullOrWhiteSpace(request.ActionCode))
            {
                return SetResponseFail("-1", $"ActionCode is empty.");
            }

            if (httpContext == null)
            {
                return SetResponseFail("-1", $"HttpContext is null.");
            }

            var whereExpression = Expressionable.Create<ActionEntity>()
                .And(a => a.Deleted == 0)
                .And(m => m.ActionCode == request.ActionCode)
                .ToExpression();
            var actionList = await _dal.FindList(whereExpression);

            //TODO 获取配置的Action Class信息
            mainActionInstance = GetService(typeof(IProductNewServices));
            if(mainActionInstance== null)
            {
                return SetResponseFail("-1", $"The mainActionInstance is null.");
            }

            return _context;
        }

        #region 通过委托代理调用服务功能
        /// <summary>
        /// 通过委托代理调用服务功能
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        protected async Task<ActionContext> InvokeFunc(ActionContext context)
        {
            // 将反射找到的方法创建委托。
            var checkBeforeFunc = InstanceMethodBuilder<ActionContext, Task<ActionContext>>.CreateInstanceMethod(mainActionInstance, checkBefore);
            var checkFunc = InstanceMethodBuilder<ActionContext, Task<ActionContext>>.CreateInstanceMethod(mainActionInstance, check);
            var checkAfterFunc = InstanceMethodBuilder<ActionContext, Task<ActionContext>>.CreateInstanceMethod(mainActionInstance, checkAfter);
            var mainBeforeFunc = InstanceMethodBuilder<ActionContext, Task<ActionContext>>.CreateInstanceMethod(mainActionInstance, mainBefore);
            var mainProcessFunc = InstanceMethodBuilder<ActionContext, Task<ActionContext>>.CreateInstanceMethod(mainActionInstance, mainProcess);
            var mainAfterFunc = InstanceMethodBuilder<ActionContext, Task<ActionContext>>.CreateInstanceMethod(mainActionInstance, mainAfter);
            var dataUpdateBeforeFunc = InstanceMethodBuilder<ActionContext, Task<ActionContext>>.CreateInstanceMethod(mainActionInstance, dataUpdateBefore);
            var dataUpdateFunc = InstanceMethodBuilder<ActionContext, Task<ActionContext>>.CreateInstanceMethod(mainActionInstance, dataUpdate);
            var dataUpdateAfterFunc = InstanceMethodBuilder<ActionContext, Task<ActionContext>>.CreateInstanceMethod(mainActionInstance, dataUpdateAfter);

            context = await checkBeforeFunc(context);
            if (context.Response.Result == false)
            {
                return context;
            }

            context = await checkFunc(context);
            if (context.Response.Result == false)
            {
                return context;
            }

            context = await checkAfterFunc(context);
            if (context.Response.Result == false)
            {
                return context;
            }

            context = await mainBeforeFunc(context);
            if (context.Response.Result == false)
            {
                return context;
            }

            context = await mainProcessFunc(context);
            if (context.Response.Result == false)
            {
                return context;
            }

            context = await mainAfterFunc(context);
            if (context.Response.Result == false)
            {
                return context;
            }

            context = await dataUpdateBeforeFunc(context);
            if (context.Response.Result == false)
            {
                return context;
            }

            context = await dataUpdateFunc(context);
            if (context.Response.Result == false)
            {
                return context;
            }

            context = await dataUpdateAfterFunc(context);
            if (context.Response.Result == false)
            {
                return context;
            }

            return context;
        }
        #endregion

        #region 使用反射找到的方法
        protected MethodInfo checkBefore
        {
            get
            {
                return typeof(IFunctionAction).GetMethod(nameof(IFunctionAction.CheckBefore), new[] { typeof(ActionContext) });
            }
        }

        protected MethodInfo check
        {
            get
            {
                return typeof(IFunctionAction).GetMethod(nameof(IFunctionAction.Check), new[] { typeof(ActionContext) });
            }
        }

        protected MethodInfo checkAfter
        {
            get
            {
                return typeof(IFunctionAction).GetMethod(nameof(IFunctionAction.CheckAfter), new[] { typeof(ActionContext) });
            }
        }

        protected MethodInfo mainBefore
        {
            get
            {
                return typeof(IFunctionAction).GetMethod(nameof(IFunctionAction.MainProcessBefore), new[] { typeof(ActionContext) });
            }
        }

        protected MethodInfo mainProcess
        {
            get
            {
                return typeof(IFunctionAction).GetMethod(nameof(IFunctionAction.MainProcess), new[] { typeof(ActionContext) });
            }
        }

        protected MethodInfo mainAfter
        {
            get
            {
                return typeof(IFunctionAction).GetMethod(nameof(IFunctionAction.MainProcessAfter), new[] { typeof(ActionContext) });
            }
        }

        protected MethodInfo dataUpdateBefore
        {
            get
            {
                return typeof(IFunctionAction).GetMethod(nameof(IFunctionAction.DataUpdateBefore), new[] { typeof(ActionContext) });
            }
        }

        protected MethodInfo dataUpdate
        {
            get
            {
                return typeof(IFunctionAction).GetMethod(nameof(IFunctionAction.DataUpdate), new[] { typeof(ActionContext) });
            }
        }

        protected MethodInfo dataUpdateAfter
        {
            get
            {
                return typeof(IFunctionAction).GetMethod(nameof(IFunctionAction.DataUpdateAfter), new[] { typeof(ActionContext) });
            }
        }
        #endregion
    }
}
