
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SEFA.DFM.Model.ViewModels;
using System.Threading.Tasks;
using SqlSugar;
using System.Collections.Generic;
using System;
using SEFA.Base.IServices.BASE;
using SEFA.DFM.Model;

namespace SEFA.DFM.Services
{
    /// <summary>
    /// 生产类
    /// </summary>
    public class ProductServices : BaseServices<ActionEntity>, IProductServices, IFunctionAction
    {
        public int Sort { get { return 100; } }

        private readonly IBaseRepository<ActionEntity> _dal;

        public ProductServices(IBaseRepository<ActionEntity> dal)
        {
            _dal = dal;
            BaseDal = dal;
        }

        /// <summary>
        /// 检查前处理；可不写实现代码
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        public async Task<ActionContext> CheckBefore(ActionContext data)
        {
            return data;
        }

        //业务检查处理
        public async Task<ActionContext> Check(ActionContext data)
        {
            var whereExpression = Expressionable.Create<ActionEntity>()
                .And(a => a.Deleted == 0)
                .And(m => m.ActionCode == data.Requst.ActionCode)
                .ToExpression();
            var actionList = await _dal.FindList(whereExpression);

            data.Response.Data.Add("Check", "Check");
            return data;
        }

        /// <summary>
        /// 检查后处理；可不写实现代码
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        public async Task<ActionContext> CheckAfter(ActionContext data)
        {
            return data;
        }

        /// <summary>
        /// 业务主处理前处理；可不写实现代码
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        public async Task<ActionContext> MainProcessBefore(ActionContext data)
        {
            return data;
        }

        /// <summary>
        /// 业务主处理
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        public async Task<ActionContext> MainProcess(ActionContext data)
        {
            var whereExpression = Expressionable.Create<ActionEntity>()
                .And(a => a.Deleted == 0)
                .And(m => m.ActionCode == data.Requst.ActionCode)
                .ToExpression();
            var actionList = await _dal.FindList(whereExpression);

            data.Response.Data.Add("MainProcess", "MainProcess");
            return data;
        }

        /// <summary>
        /// 业务主处理后处理；可不写实现代码
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        public async Task<ActionContext> MainProcessAfter(ActionContext data)
        {
            return data;
        }

        /// <summary>
        /// 数据更新前处理；可不写实现代码
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        public async Task<ActionContext> DataUpdateBefore(ActionContext data)
        {
            return data;
        }

        /// <summary>
        /// 数据更新处理
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        public async Task<ActionContext> DataUpdate(ActionContext data)
        {
            var whereExpression = Expressionable.Create<ActionEntity>()
                .And(a => a.Deleted == 0)
                .And(m => m.ActionCode == data.Requst.ActionCode)
                .ToExpression();
            var actionList = await _dal.FindList(whereExpression);

            data.Response.Data.Add("DataUpdate", "DataUpdate");
            return data;
        }

        /// <summary>
        /// 数据更新后处理；可不写实现代码
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        public async Task<ActionContext> DataUpdateAfter(ActionContext data)
        {
            return data;
        }
    }
}