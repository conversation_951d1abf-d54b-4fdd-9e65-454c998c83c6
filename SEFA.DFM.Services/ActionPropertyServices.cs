
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;

namespace SEFA.DFM.Services
{
    public class ActionPropertyServices : BaseServices<ActionPropertyEntity>, IActionPropertyServices
    {
        private readonly IBaseRepository<ActionPropertyEntity> _dal;
        public ActionPropertyServices(IBaseRepository<ActionPropertyEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }
    }
}