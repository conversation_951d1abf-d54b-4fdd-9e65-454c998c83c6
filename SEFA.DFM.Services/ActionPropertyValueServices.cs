
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Common.HttpContextUser;
using SEFA.Base.Common.LogHelper;
using SEFA.Base.Model;
using SEFA.DFM.Model.ViewModels;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using SEFA.Base.IRepository.UnitOfWork;
using SqlSugar;
using System.Linq;
using System.DirectoryServices.ActiveDirectory;
using SEFA.DFM.Model;

namespace SEFA.DFM.Services
{
    public class ActionPropertyValueServices : BaseServices<ActionPropertyValueEntity>, IActionPropertyValueServices
    {
        private readonly IBaseRepository<ActionPropertyValueEntity> _dal;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IUser _user;
        public ActionPropertyValueServices (IBaseRepository<ActionPropertyValueEntity> dal, IUnitOfWork UnitOfWork, IUser User) {
            this._dal = dal;
            base.BaseDal = dal;
            this._unitOfWork = UnitOfWork;
            this._user = User;
        }


        /// <summary>
        /// 获取模型Action属性信息
        /// </summary>
        /// <param name="batchno"></param>
        /// <returns></returns>
        public async  Task<List<EquipmentActionPropertyModel>> GetEquipmentActionProperty (EquipmentActionPropertyModel model) {
      
            var list = new List<EquipmentActionPropertyModel>();

            try
            {
                list = await _dal.Db.Queryable<EquipmentEntity, EquipmentFunctionEntity, FunctionEntity, EquipmentActionEntity, ActionEntity, ActionPropertyEntity, ActionPropertyValueEntity>((Equ, EquFun, Fun, EquAct, Act, ActP, ActPv)
                => new object[]
                {
                    JoinType.Inner,Equ.ID == EquFun.EquipmentId,
                    JoinType.Inner,EquFun.FunctionId == Fun.ID && EquFun.Deleted == 0,
                    JoinType.Inner,EquAct.EquipmentFunctionId == EquFun.ID ,
                    JoinType.Inner,Act.ID == EquAct.ActionId,
                    JoinType.Inner,ActP.ActionId == Act.ID,
                    JoinType.Left, ActPv.EquipmentId == Equ.ID && ActPv.ActionId == Act.ID && ActPv.PropertyId == ActP.ID && ActPv.EquipmnetActionId == EquAct.ID,
                })
            .Select((Equ, EquFun, Fun, EquAct, Act, ActP, ActPv) => new EquipmentActionPropertyModel
            {
                EquipmentId = Equ.ID,
                EquipmentCode = Equ.EquipmentCode,
                EquipmentName = Equ.EquipmentName,
                EquipmentLevel = Equ.Level,
                FunctionId = Fun.ID,
                FunctionCode = Fun.FunctionCode,
                FunctionName = Fun.FunctoinName,
                FunctionType = Fun.FunctionType,
                ActionId = Act.ID,
                ActionCode = Act.ActionCode,
                ActionName = Act.ActionName,
                PropertyId = ActP.ID,
                PropertyCode = ActP.PropertyCode,
                PropertyName = ActP.PropertyName,
                PropertyType = ActP.PropertyType,
                DefaultValue = ActP.DefaultValue,
                ActualValue = string.IsNullOrEmpty(ActPv.PropertyValue) ? ActP.DefaultValue : ActPv.PropertyValue,
                EquipmentActionId = EquAct.ID,
                EquipmentFunctionId = EquFun.ID
            })
            .MergeTable()
            .WhereIF(!string.IsNullOrEmpty(model.EquipmentActionId),a => a.EquipmentActionId == model.EquipmentActionId)
            .WhereIF(!string.IsNullOrEmpty(model.EquipmentId), a => a.EquipmentId == model.EquipmentId)
            .WhereIF(!string.IsNullOrEmpty(model.ActionCode) , a => a.ActionCode == model.ActionCode)
            .ToListAsync();
            }
            catch(Exception ex)
            {
                return list;
            }

            foreach (var item in list.Where(a =>a.PropertyType == EnumClass.PropertyType.Type_StringList))
            {
                item.PropertyValueList =  _dal.Db.Queryable<PropertyValueListEntity>().Where(a => a.PropertyId == item.PropertyId).Select(a=>a.ItemValue).ToList();
            }
           

            return list.OrderBy(a=>a.FunctionId).OrderBy(a=>a.ActionId).ToList();
        }



        /// <summary>
        /// 设置模型Action属性值
        /// </summary>
        /// <param name="actionProrertyList"></param>
        /// <returns></returns>
        public async Task<MessageModel<string>> SaveEquipmentActionProperty (List<EquipmentActionPropertyModel> actionProrertyList) {

            var returnData = new MessageModel<string>();
            var insertList = new List<ActionPropertyValueEntity>();
            if (actionProrertyList.Count <= 0)
                return returnData;

            if (actionProrertyList.Select(a => a.EquipmentId).Distinct().Count() > 1)
            {
                returnData.msg = "模型数据异常！";
                return returnData;
            }

            /*获取模型默认值不一致的Action属性*/
            var changeList = actionProrertyList.FindAll(a => a.DefaultValue != a.ActualValue).ToList();

            if (changeList.Count > 0)
            {
                foreach (var change in changeList)
                {
                    ActionPropertyValueEntity entity = new ActionPropertyValueEntity();
                    entity.EquipmentId = change.EquipmentId;
                    entity.PropertyId = change.PropertyId;
                    entity.ActionId = change.ActionId;
                    entity.FunctionId = change.FunctionId;
                    entity.EquipmnetActionId = change.EquipmentActionId;
                    entity.PropertyValue = change.ActualValue;
                    entity.CreateCustomGuid(_user.Name);
                    insertList.Add(entity);
                }
            }

            /*查询模型已存在的Action属性*/
            string EquipmentId = actionProrertyList[0].EquipmentId;
            string EquipmentActionId = actionProrertyList[0].EquipmentActionId;
            var oldlist = await _dal.FindList(a => a.EquipmentId == EquipmentId && a.EquipmnetActionId == EquipmentActionId);

            _unitOfWork.BeginTran();

            try
            {
                if (oldlist.Count > 0)
                {
                    returnData.success = await _dal.DeleteByIds(oldlist.Select(a => a.ID).ToArray());
                    if (returnData.success == false)
                    {
                        _unitOfWork.RollbackTran();
                        return returnData;
                    }
                }
                if (insertList.Count > 0)
                {
                    returnData.success = await _dal.Add(insertList) > 0;
                    if (returnData.success == false)
                    {
                        _unitOfWork.RollbackTran();
                        return returnData;
                    }
                }

                _unitOfWork.CommitTran();

                returnData.success = true;
                return returnData;
            }
            catch (Exception ex)
            {
                SerilogServer.LogError(ex);
                returnData.msg = ex.Message;
                return returnData;
            }
        }
    }
}