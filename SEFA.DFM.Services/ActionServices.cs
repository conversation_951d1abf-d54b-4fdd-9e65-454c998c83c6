
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;

namespace SEFA.DFM.Services
{
    public class ActionServices : BaseServices<ActionEntity>, IActionServices
    {
        private readonly IBaseRepository<ActionEntity> _dal;
        public ActionServices(IBaseRepository<ActionEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }
    }
}