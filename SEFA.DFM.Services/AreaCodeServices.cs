
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;

namespace SEFA.DFM.Services
{
    public class AreaCodeServices : BaseServices<AreaCodeEntity>, IAreaCodeServices
    {
        private readonly IBaseRepository<AreaCodeEntity> _dal;
        public AreaCodeServices(IBaseRepository<AreaCodeEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }
    }
}