
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;

namespace SEFA.DFM.Services
{
    public class BasePropertyServices : BaseServices<BasePropertyEntity>, IBasePropertyServices
    {
        private readonly IBaseRepository<BasePropertyEntity> _dal;
        public BasePropertyServices(IBaseRepository<BasePropertyEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<BasePropertyEntity>> GetList(BasePropertyRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<BasePropertyEntity>()
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }

        public async Task<List<BasePropertyEntity>> GetListByIdList(List<string> idList)
        {
            var whereExpression = Expressionable.Create<BasePropertyEntity>()
                             .And(it => idList.Contains(it.ID))
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }

        public async Task<PageModel<BasePropertyEntity>> GetPageList(BasePropertyRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<BasePropertyEntity>()
                             .ToExpression();
            var data = await _dal.QueryPage(whereExpression,reqModel.pageIndex,reqModel.pageSize);
              
            return data;
        }

        public async Task<bool> SaveForm(BasePropertyEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}