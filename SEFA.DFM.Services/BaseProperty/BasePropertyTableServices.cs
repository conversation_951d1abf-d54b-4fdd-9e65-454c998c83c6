
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;

namespace SEFA.DFM.Services
{
    public class BasePropertyTableServices : BaseServices<BasePropertyTableEntity>, IBasePropertyTableServices
    {
        private readonly IBaseRepository<BasePropertyTableEntity> _dal;
        public BasePropertyTableServices(IBaseRepository<BasePropertyTableEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<BasePropertyTableEntity>> GetList(BasePropertyTableRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<BasePropertyTableEntity>()
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }

        public async Task<PageModel<BasePropertyTableEntity>> GetPageList(BasePropertyTableRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<BasePropertyTableEntity>()
                             .ToExpression();
            var data = await _dal.QueryPage(whereExpression,reqModel.pageIndex,reqModel.pageSize);
              
            return data;
        }

        public async Task<bool> SaveForm(BasePropertyTableEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}