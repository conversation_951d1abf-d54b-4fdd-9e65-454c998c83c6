
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;

namespace SEFA.DFM.Services
{
    public class BaseUniqueNumberServices : BaseServices<BaseUniqueNumberEntity>, IBaseUniqueNumberServices
    {
        private readonly IBaseRepository<BaseUniqueNumberEntity> _dal;
        public BaseUniqueNumberServices(IBaseRepository<BaseUniqueNumberEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<BaseUniqueNumberEntity>> GetList(BaseUniqueNumberRequestModel reqModel)
        {
            List<BaseUniqueNumberEntity> result = new List<BaseUniqueNumberEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<BaseUniqueNumberEntity>()
                             .ToExpression();
            var data = await _dal.Db.Queryable<BaseUniqueNumberEntity>()
                .Where(whereExpression).ToListAsync();
            return data;
        }

        public async Task<PageModel<BaseUniqueNumberEntity>> GetPageList(BaseUniqueNumberRequestModel reqModel)
        {
            PageModel<BaseUniqueNumberEntity> result = new PageModel<BaseUniqueNumberEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<BaseUniqueNumberEntity>()
                             .ToExpression();
            var data = await _dal.Db.Queryable<BaseUniqueNumberEntity>()
                .Where(whereExpression)
                .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
            result.dataCount = dataCount;
            result.data = data;
            return result;
        }

        public async Task<bool> SaveForm(BaseUniqueNumberEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}