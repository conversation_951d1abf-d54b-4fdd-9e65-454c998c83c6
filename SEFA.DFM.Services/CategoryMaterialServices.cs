
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;

namespace SEFA.DFM.Services
{
    public class CategoryMaterialServices : BaseServices<CategoryMaterialEntity>, ICategoryMaterialServices
    {
        private readonly IBaseRepository<CategoryMaterialEntity> _dal;
        public CategoryMaterialServices(IBaseRepository<CategoryMaterialEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }
    }
}