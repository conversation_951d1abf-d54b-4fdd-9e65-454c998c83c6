
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;

namespace SEFA.DFM.Services
{
    public class CategoryServices : BaseServices<CategoryEntity>, ICategoryServices
    {
        private readonly IBaseRepository<CategoryEntity> _dal;
        public CategoryServices(IBaseRepository<CategoryEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }
    }
}