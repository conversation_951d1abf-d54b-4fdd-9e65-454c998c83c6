
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.DFM.Model.ViewModels;
using SqlSugar;
using SEFA.DFM.IRepository;

namespace SEFA.DFM.Services
{
    public class CimlevelEquipmentServices : BaseServices<CimlevelEquipmentEntity>, ICimlevelEquipmentServices
    {
        private readonly ICimlevelEquipmentRepository _dal;
        public CimlevelEquipmentServices(ICimlevelEquipmentRepository dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        /// <summary>
        /// 获取用户角色关联 树
        /// </summary>
        /// <param name="MenuName"></param>
        /// <param name="MenuType"></param>
        /// <returns></returns>
        public async Task<List<TreeModel>> GetCimEquipmentTree(string CimID)
        {

            List<EquipmentEntity> equipment = await _dal.GetEquipmentList(CimID);


            //获取树形结构
            List<TreeModel> treeList = new List<TreeModel>();
            foreach (var item in equipment)
            {

                TreeModel node = new TreeModel
                {
                    id = item.ID,
                    name = item.EquipmentName,
                    value = "1",
                    showcheck = false,
                    checkstate = 0,
                    isexpand = false,
                    parentId = item.ParentId
                };
                treeList.Add(node);
            }

            return treeList;
        }


    }
}