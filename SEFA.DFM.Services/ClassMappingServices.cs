
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;

namespace SEFA.DFM.Services
{
    public class ClassMappingServices : BaseServices<ClassMappingEntity>, IClassMappingServices
    {
        private readonly IBaseRepository<ClassMappingEntity> _dal;
        public ClassMappingServices(IBaseRepository<ClassMappingEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }
    }
}