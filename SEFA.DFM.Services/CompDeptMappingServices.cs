
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;

namespace SEFA.DFM.Services
{
    public class CompDeptMappingServices : BaseServices<CompDeptMappingEntity>, ICompDeptMappingServices
    {
        private readonly IBaseRepository<CompDeptMappingEntity> _dal;
        public CompDeptMappingServices(IBaseRepository<CompDeptMappingEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }
    }
}