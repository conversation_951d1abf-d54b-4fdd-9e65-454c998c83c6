
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;

namespace SEFA.DFM.Services
{
    public class CompanyMappingServices : BaseServices<CompanyMappingEntity>, ICompanyMappingServices
    {
        private readonly IBaseRepository<CompanyMappingEntity> _dal;
        public CompanyMappingServices(IBaseRepository<CompanyMappingEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }
    }
}