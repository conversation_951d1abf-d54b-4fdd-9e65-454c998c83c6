
using SEFA.DFM.IServices.DATAWAREHOUSE;
using SEFA.DFM.Model.Models.DATAWAREHOUSE;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using System.Linq;
using System.Threading.Tasks;

namespace SEFA.DFM.Services.DATAWAREHOUSE
{
    public class ProductlineouttagServices : BaseServices<ProductlineouttagEntity>, IProductlineouttagServices
    {
        private readonly IBaseRepository<ProductlineouttagEntity> _dal;
        public ProductlineouttagServices(IBaseRepository<ProductlineouttagEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }
        public async Task<int> UpdateIsenabled(string[] ids, int state)
        {
            var data = await _dal.Db.Updateable<ProductlineouttagEntity>()
                .SetColumns(it => it.Enable == state)
                .Where(it => ids.Contains(it.ID))
                .ExecuteCommandAsync();
            return data;
        }
    }
}