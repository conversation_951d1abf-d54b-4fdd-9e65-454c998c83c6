
using SEFA.DFM.IServices.DATAWAREHOUSE;
using SEFA.DFM.Model.Models.DATAWAREHOUSE;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using System.Linq;
using System.Threading.Tasks;

namespace SEFA.DFM.Services.DATAWAREHOUSE
{
    public class RelMaterialTagServices : BaseServices<RelMaterialTagEntity>, IRelMaterialTagServices
    {
        private readonly IBaseRepository<RelMaterialTagEntity> _dal;
        public RelMaterialTagServices(IBaseRepository<RelMaterialTagEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }
        public async Task<int> UpdateIsenabled(string[] ids, int state)
        {
            var data = await _dal.Db.Updateable<RelMaterialTagEntity>()
                .SetColumns(it => it.Enable == state)
                .Where(it => ids.Contains(it.ID))
                .ExecuteCommandAsync();
            return data;
        }
    }
}