
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json;
using System.Threading.Tasks;
using System.Collections.Generic;
using SqlSugar;

namespace SEFA.DFM.Services
{
    public class DataApiServices : BaseServices<DataApiEntity>, IDataApiServices
    {
        private readonly IBaseRepository<DataApiEntity> _dal;

        private static readonly IsoDateTimeConverter _TimeFormat = new IsoDateTimeConverter
        {
            DateTimeFormat = "yyyy-MM-dd HH:mm:ss"
        };
        public DataApiServices(IBaseRepository<DataApiEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public string ApiDataByModel(DataApiEntity model, List<SugarParameter> parameters)
        {
            var data = _dal.Db.Ado.GetDataTable(model.Sql, parameters);

            return JsonConvert.SerializeObject(data, _TimeFormat);
        }

    }
}