
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.DFM.Model.ViewModels;
using System.Collections.Generic;
using System.Linq.Expressions;
using System;
using System.Threading.Tasks;
using SqlSugar;
using System.Linq;

namespace SEFA.DFM.Services
{
    public class DataItemServices : BaseServices<DataItemEntity>, IDataItemServices
    {
        private readonly IBaseRepository<DataItemEntity> _dal;
        public DataItemServices(IBaseRepository<DataItemEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<bool> isExistCode(DataItemEntity request, bool ismod)
        {
            if (ismod)
            {
                DataItemEntity old = await this.QueryById(request.ID);
                if (old == null || old.ItemCode.Equals(request.ItemCode))
                {
                    return false;
                }
            }
            var whereExpression = Expressionable.Create<DataItemEntity>().And(a => a.Deleted == 0)
                .And(a => a.ItemCode.Equals(request.ItemCode) && a.Lang.Equals(request.Lang))
                .ToExpression();
            List<DataItemEntity> list = await FindList(whereExpression);

            return list.Any();
        }

        public async Task<bool> isExistName(DataItemEntity request, bool ismod)
        {
            if (ismod)
            {
                DataItemEntity old = await this.QueryById(request.ID);
                if (old == null || old.ItemName.Equals(request.ItemName))
                {
                    return false;
                }
            }
            var whereExpression = Expressionable.Create<DataItemEntity>().And(a => a.Deleted == 0)
                .And(a => a.ItemName.Equals(request.ItemName) && a.Lang.Equals(request.Lang))
                .ToExpression();
            List<DataItemEntity> list = await FindList(whereExpression);

            return list.Any();
        }
        //private DataItemService dataItemService = new DataItemService();

        //private ICache cache = CacheFactory.CaChe();


    }
}