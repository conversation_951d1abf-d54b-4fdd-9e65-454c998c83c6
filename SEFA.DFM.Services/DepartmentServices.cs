using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using System.Threading.Tasks;
using SqlSugar;
using System.Collections.Generic;
using System.Linq;

namespace SEFA.DFM.Services
{
    public class DepartmentServices : BaseServices<DepartmentEntity>, IDepartmentServices
    {
        private readonly IBaseRepository<DepartmentEntity> _dal;
        public DepartmentServices(IBaseRepository<DepartmentEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }
        public async Task<bool> isExist(DepartmentEntity entity, bool mod)
        {
            if (mod)
            {
                DepartmentEntity old = await this.QueryById(entity.Companyid);
                if (old == null || old.Fullname.Equals(entity.Fullname))
                {
                    return false;
                }
            }
            //where = where.And(n => n.PROC_CODE == model.PROC_CODE && n.ROUTING_ID.Equals(model.ROUTING_ID));
            var whereExpression = Expressionable.Create<DepartmentEntity>().And(t => t.Deleted == 0)
                .And(a => a.Fullname.Equals(entity.Fullname) && a.Description.Equals(entity.Description) && a.Manager.Equals(entity.Manager)
                && a.Companyid == entity.Companyid)
                .ToExpression();
            List<DepartmentEntity> list = await FindList(whereExpression);

            return list.Any();

        }

        /// <summary>
        /// 获取Partid
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<List<DepartmentEntity>> GetCompanyIdByDepId(string depid)
        {
            var whereExpression = Expressionable.Create<DepartmentEntity>().And(a => a.Deleted == 0)
                                .AndIF(!string.IsNullOrEmpty(depid), a => a.ID == depid)
                                .ToExpression();
            List<DepartmentEntity> data = await FindList(whereExpression);

            return data;
        }
    }
}