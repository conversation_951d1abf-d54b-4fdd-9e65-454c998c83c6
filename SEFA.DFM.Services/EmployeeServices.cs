
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;

namespace SEFA.DFM.Services
{
    public class EmployeeServices : BaseServices<EmployeeEntity>, IEmployeeServices
    {
        private readonly IBaseRepository<EmployeeEntity> _dal;
        public EmployeeServices(IBaseRepository<EmployeeEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }
    }
}