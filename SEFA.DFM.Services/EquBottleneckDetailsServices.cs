
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;

namespace SEFA.DFM.Services
{
    public class EquBottleneckDetailsServices : BaseServices<EquBottleneckDetailsEntity>, IEquBottleneckDetailsServices
    {
        private readonly IBaseRepository<EquBottleneckDetailsEntity> _dal;
        public EquBottleneckDetailsServices(IBaseRepository<EquBottleneckDetailsEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }
    }
}