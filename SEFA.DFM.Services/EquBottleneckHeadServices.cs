
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Common.HttpContextUser;
using SEFA.Base.IRepository.UnitOfWork;
using AutoMapper;
using SEFA.Base.Common.LogHelper;
using SEFA.Base.Model;
using SEFA.DFM.Model.ViewModels;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using System.Linq;

namespace SEFA.DFM.Services
{
    public class EquBottleneckHeadServices : BaseServices<EquBottleneckHeadEntity>, IEquBottleneckHeadServices
    {
        private readonly IBaseRepository<EquBottleneckHeadEntity> _dal;
        private readonly IBaseRepository<EquBottleneckDetailsEntity> _detailsdal;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IUser _user;
        private readonly IMapper _mapper;
        public EquBottleneckHeadServices (IBaseRepository<EquBottleneckHeadEntity> dal, IUnitOfWork UnitOfWork, IUser User, IBaseRepository<EquipmentFunctionEntity> equipmentFunctionServices, IMapper mapper, IBaseRepository<EquBottleneckDetailsEntity> detailsdal) {
            this._dal = dal;
            base.BaseDal = dal;
            this._unitOfWork = UnitOfWork;
            this._user = User;
            _mapper = mapper;
            _detailsdal = detailsdal;
        }

        public async Task<MessageModel<string>> SaveEquipmentBottleneck (EquBottleneckModel equBottleneckModel) {

            var returnData = new MessageModel<string>();
            bool edit = false;
            var olddetailsList = new List<EquBottleneckDetailsEntity>();
            var insertdetailsList = new List<EquBottleneckDetailsEntity>();
            if (null == equBottleneckModel)
                return returnData;
            if (equBottleneckModel.EquipmentList.Count == 0)
            {
                returnData.msg = "设备数据为空！";
                return returnData;
            }

            if (!equBottleneckModel.EffectiveStartDate.HasValue)
            {
                returnData.msg = "生效时间不可为空！";
                return returnData;
            }
            if (equBottleneckModel.EffectiveEndDate.HasValue && equBottleneckModel.EffectiveStartDate >= equBottleneckModel.EffectiveEndDate)
            {
                returnData.msg = "生效时间应小于失效时间不可为空！";
                return returnData;
            }

            if (equBottleneckModel.EquipmentList.Sum(a=>a.Split) != 100)
            {
                returnData.msg = "设备分摊百分比总和应为100%！";
                return returnData;
            }


            /*检查生效期*/
            var lineBnList =  await _dal.FindList(a => a.ID != equBottleneckModel.ID && a.LineId == equBottleneckModel.LineId);

            foreach ( var item in lineBnList )
            {
                if(equBottleneckModel.EffectiveEndDate.HasValue)
                {
                    if(item.EffectiveEndDate.HasValue)
                    {
                        if( false ==  (equBottleneckModel.EffectiveStartDate.Value > item.EffectiveEndDate) || (equBottleneckModel.EffectiveEndDate.Value < item.EffectiveStartDate) )
                        { returnData.msg = "已存在生效期重叠的数据！"; return returnData; }
                    }
                    else
                    {
                        if (false == (equBottleneckModel.EffectiveEndDate.Value < item.EffectiveStartDate))
                        { returnData.msg = "已存在生效期重叠的数据！"; return returnData; }
                    }
                }
                else
                {
                    if (item.EffectiveEndDate.HasValue)
                    {
                        if (false == (equBottleneckModel.EffectiveStartDate.Value > item.EffectiveEndDate) )
                        { returnData.msg = "已存在生效期重叠的数据！"; return returnData; }
                    }
                    else
                    {
                        returnData.msg = "不允许设置多个失效期为空的数据，存在生效期重叠的数据！";
                        return returnData;
                    }
                }
            }

            EquBottleneckHeadEntity headEntity = null;

            if(!string.IsNullOrEmpty(equBottleneckModel.ID))
            {
                headEntity = await _dal.FindEntity(equBottleneckModel.ID);
                if(headEntity == null)
                {
                    returnData.msg = "数据异常！";
                    return returnData;
                }
                headEntity = _mapper.Map<EquBottleneckHeadEntity>(equBottleneckModel);
                headEntity.Modify(headEntity.ID,_user.Name);

                olddetailsList = await _detailsdal.FindList(a => a.HeadId == headEntity.ID);
                edit = true;
            }
            else
            {
                headEntity = _mapper.Map<EquBottleneckHeadEntity>(equBottleneckModel);
                headEntity.CreateCustomGuid(_user.Name);
            }

            foreach(var item in equBottleneckModel.EquipmentList)
            {
                EquBottleneckDetailsEntity detailsEntity = new EquBottleneckDetailsEntity();
                detailsEntity.HeadId = headEntity.ID;
                detailsEntity.Split = item.Split;
                detailsEntity.EquipmentId = item.EquipmentId;
                detailsEntity.Deleted = 0;
                detailsEntity.CreateCustomGuid(_user.Name);

                insertdetailsList.Add(detailsEntity);
            }

         
            _unitOfWork.BeginTran();

            try
            {
                if(edit)
                {
                    returnData.success = await _dal.Update(headEntity);
                    if (returnData.success == false)
                    {
                        _unitOfWork.RollbackTran();
                        return returnData;
                    }
                    if (olddetailsList.Count >0)
                    {
                        returnData.success = await _detailsdal.DeleteById(olddetailsList.Select(a=>a.ID).ToArray());
                        if (returnData.success == false)
                        {
                            _unitOfWork.RollbackTran();
                            return returnData;
                        }
                    }
                }
                else
                {
                    returnData.success = await _dal.Add(headEntity) > 0;
                    if (returnData.success == false)
                    {
                        _unitOfWork.RollbackTran();
                        return returnData;
                    }
                }

                if (insertdetailsList.Count > 0)
                {
                    returnData.success = await _detailsdal.Add(insertdetailsList) >0;
                    if (returnData.success == false)
                    {
                        _unitOfWork.RollbackTran();
                        return returnData;
                    }
                }
             

                _unitOfWork.CommitTran();

                returnData.success = true;
                return returnData;
            }
            catch (Exception ex)
            {
                SerilogServer.LogError(ex);
                returnData.msg = ex.Message;
                return returnData;
            }
        }

        public async Task<MessageModel<string>> DeleteEquipmentBottleneck (EquBottleneckModel equBottleneckModel) {

            var returnData = new MessageModel<string>();
          
            if ( string.IsNullOrEmpty(equBottleneckModel.ID))
            {
                returnData.msg = "数据为空！";
                return returnData;
            }

           
      

            var headEntity = await _dal.FindEntity(equBottleneckModel.ID);
            if (null == headEntity)
            {
                returnData.msg = "数据为空！";
                return returnData;
            }
            var olddetailsList = await _detailsdal.FindList(a => a.HeadId == headEntity.ID);

            _unitOfWork.BeginTran();

            try
            {   if(olddetailsList.Count > 0)
                {
                    returnData.success = await _detailsdal.DeleteById(olddetailsList.Select(a => a.ID).ToArray());
                    if (returnData.success == false)
                    {
                        _unitOfWork.RollbackTran();
                        return returnData;
                    }
                }

                returnData.success = await _dal.Delete(headEntity);
                if (returnData.success == false)
                {
                    _unitOfWork.RollbackTran();
                    return returnData;
                }
              

                _unitOfWork.CommitTran();

                returnData.success = true;
                return returnData;
            }
            catch (Exception ex)
            {
                SerilogServer.LogError(ex);
                returnData.msg = ex.Message;
                return returnData;
            }
        }
    }
}