
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;

namespace SEFA.DFM.Services
{
    public class EquipAssocInterlockServices : BaseServices<EquipAssocInterlockEntity>, IEquipAssocInterlockServices
    {
        private readonly IBaseRepository<EquipAssocInterlockEntity> _dal;
        public EquipAssocInterlockServices(IBaseRepository<EquipAssocInterlockEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<EquipAssocInterlockEntity>> GetList(EquipAssocInterlockRequestModel reqModel)
        {
            List<EquipAssocInterlockEntity> result = new List<EquipAssocInterlockEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<EquipAssocInterlockEntity>()
                             .ToExpression();
            var data = await _dal.Db.Queryable<EquipAssocInterlockEntity>()
                .Where(whereExpression).ToListAsync();
            return data;
        }

        public async Task<PageModel<EquipAssocInterlockEntity>> GetPageList(EquipAssocInterlockRequestModel reqModel)
        {
            PageModel<EquipAssocInterlockEntity> result = new PageModel<EquipAssocInterlockEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<EquipAssocInterlockEntity>()
                             .ToExpression();
            var data = await _dal.Db.Queryable<EquipAssocInterlockEntity>()
                .Where(whereExpression)
                .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
            result.dataCount = dataCount;
            result.data = data;
            return result;
        }

        public async Task<bool> SaveForm(EquipAssocInterlockEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}