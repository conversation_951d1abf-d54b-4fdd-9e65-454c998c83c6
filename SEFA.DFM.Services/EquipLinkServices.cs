
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;

namespace SEFA.DFM.Services
{
    public class EquipLinkServices : BaseServices<EquipLinkEntity>, IEquipLinkServices
    {
        private readonly IBaseRepository<EquipLinkEntity> _dal;
        public EquipLinkServices(IBaseRepository<EquipLinkEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }
    }
}