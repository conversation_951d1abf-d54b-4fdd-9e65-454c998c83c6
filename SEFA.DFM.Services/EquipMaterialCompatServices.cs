
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;

namespace SEFA.DFM.Services
{
    public class EquipMaterialCompatServices : BaseServices<EquipMaterialCompatEntity>, IEquipMaterialCompatServices
    {
        private readonly IBaseRepository<EquipMaterialCompatEntity> _dal;
        public EquipMaterialCompatServices(IBaseRepository<EquipMaterialCompatEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<EquipMaterialCompatEntity>> GetList(EquipMaterialCompatRequestModel reqModel)
        {
            List<EquipMaterialCompatEntity> result = new List<EquipMaterialCompatEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<EquipMaterialCompatEntity>()
                             .ToExpression();
            var data = await _dal.Db.Queryable<EquipMaterialCompatEntity>()
                .Where(whereExpression).ToListAsync();
            return data;
        }

        public async Task<PageModel<EquipMaterialCompatEntity>> GetPageList(EquipMaterialCompatRequestModel reqModel)
        {
            PageModel<EquipMaterialCompatEntity> result = new PageModel<EquipMaterialCompatEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<EquipMaterialCompatEntity>()
                             .ToExpression();
            var data = await _dal.Db.Queryable<EquipMaterialCompatEntity>()
                .Where(whereExpression)
                .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
            result.dataCount = dataCount;
            result.data = data;
            return result;
        }

        public async Task<bool> SaveForm(EquipMaterialCompatEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}