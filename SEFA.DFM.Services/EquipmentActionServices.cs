
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Common.HttpContextUser;
using SEFA.Base.Common.LogHelper;
using SEFA.Base.Model;
using SEFA.DFM.Model.ViewModels;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using SEFA.Base.IRepository.UnitOfWork;
using SqlSugar;
using System.Linq;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Text;
using Microsoft.AspNetCore.Mvc.ApplicationModels;

namespace SEFA.DFM.Services
{
    public class EquipmentActionServices : BaseServices<EquipmentActionEntity>, IEquipmentActionServices
    {
        private readonly IBaseRepository<EquipmentActionEntity> _dal;
        private readonly IBaseRepository<EquipmentFunctionEntity> _equipmentFunctionServices;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IUser _user;

        private readonly IBaseRepository<ActionPropertyValueEntity> _equipmentActionPropertyValueServices;
        private readonly IBaseRepository<EquipmentInterlockEntity> _equipmentInterlockServices;
        private readonly IBaseRepository<ActionPropertyEntity> _actionPropertyServices;
        private readonly IBaseRepository<ActionEntity> _actionServices;
        public EquipmentActionServices (IBaseRepository<EquipmentActionEntity> dal, 
                                        IUnitOfWork UnitOfWork, 
                                        IUser User, 
                                        IBaseRepository<EquipmentFunctionEntity> equipmentFunctionServices, 
                                        IBaseRepository<ActionPropertyValueEntity> equipmentActionPropertyValueServices,
                                        IBaseRepository<EquipmentInterlockEntity> equipmentInterlockServices,
                                        IBaseRepository<ActionPropertyEntity> actionPropertyServices,
                                        IBaseRepository<ActionEntity> actionServices) {
            this._dal = dal;
            base.BaseDal = dal;
            this._unitOfWork = UnitOfWork;
            this._user = User;
            _equipmentFunctionServices = equipmentFunctionServices;
            _equipmentActionPropertyValueServices = equipmentActionPropertyValueServices;
            _equipmentInterlockServices = equipmentInterlockServices;
            _actionPropertyServices = actionPropertyServices;
            _actionServices = actionServices;
        }


        /// <summary>
        /// 获取模型已激活Function下的Action信息
        /// </summary>
        /// <param name="EquipmentId"></param>
        /// <returns></returns>
        public async Task<List<EquipmentActionModel>> GetEquipmentActiveFunctionActionList (string EquipmentId) {

            var list = new List<EquipmentActionModel>();
            list = await _dal.Db.Queryable<EquipmentEntity, EquipmentFunctionEntity, FunctionEntity, ActionEntity>((Equ, EquFun, Fun, Act)
                => new object[]
                {
                    JoinType.Inner,Equ.ID == EquFun.EquipmentId,
                    JoinType.Inner,EquFun.FunctionId == Fun.ID && EquFun.Deleted == 0,
                    JoinType.Inner,Fun.ID == Act.FunctionId
                   
                })
            .Where((equ) => equ.ID == EquipmentId)
            .Select((Equ, EquFun, Fun, Act) => new EquipmentActionModel
            {
                EquipmentId = Equ.ID,
                EquipmentCode = Equ.EquipmentCode,
                EquipmentName = Equ.EquipmentName,
                EquipmentLevel = Equ.Level,
                FunctionId = Fun.ID,
                FunctionCode = Fun.FunctionCode,
                FunctionName = Fun.FunctoinName,
                FunctionType = Fun.FunctionType,
                ActionId = Act.ID,
                ActionCode = Act.ActionCode,
                ActionName = Act.ActionName
            })
            .ToListAsync();


            return list.OrderBy(a => a.FunctionId).OrderBy(a => a.ActionId).ToList();
        }

        /// <summary>
        /// 获取模型Action信息
        /// </summary>
        /// <param name="batchno"></param>
        /// <returns></returns>
        public async Task<List<EquipmentActionModel>> GetEquipmentAction (string EquipmentId,string ActionCode ="") {

            var list = new List<EquipmentActionModel>();
            list = await _dal.Db.Queryable<EquipmentEntity, EquipmentFunctionEntity, FunctionEntity, ActionEntity, EquipmentActionEntity>((Equ, EquFun, Fun, Act, EquAct)
                => new object[]
                {
                    JoinType.Inner,Equ.ID == EquFun.EquipmentId,
                    JoinType.Inner,EquFun.FunctionId == Fun.ID && EquFun.Deleted == 0,
                    JoinType.Inner,Fun.ID == Act.FunctionId,
                    JoinType.Inner,EquAct.EquipmentFunctionId == EquFun.ID && Act.ID == EquAct.ActionId
                })
           
            .Select((Equ, EquFun, Fun, Act, EquAct) => new EquipmentActionModel
            {
                ID = EquAct.ID,
                EquipmentId = Equ.ID,
                EquipmentCode = Equ.EquipmentCode,
                EquipmentName = Equ.EquipmentName,
                EquipmentLevel = Equ.Level,
                FunctionId = Fun.ID,
                FunctionCode = Fun.FunctionCode,
                FunctionName = Fun.FunctoinName,
                EquipmentFunctionId = EquFun.ID,
                EquipmentActionId = EquAct.ID,
                FunctionType = Fun.FunctionType,
                ActionId = Act.ID,
                ActionCode = Act.ActionCode,
                ActionName = Act.ActionName
            })
            .MergeTable()
            .Where(a => a.EquipmentId == EquipmentId)
            .WhereIF(!string.IsNullOrEmpty(ActionCode), a => a.ActionCode == ActionCode)
            .ToListAsync();

            return list.OrderBy(a => a.FunctionId).OrderBy(a => a.ActionId).ToList();
        }


        /// <summary>
        /// 批量设置模型Action
        /// </summary>
        /// <param name="requestList"></param>
        /// <returns></returns>
        public async Task<MessageModel<string>> SaveEquipmentAction (string EquipmentId ,List<EquipmentActionModel> requestList) {

            var returnData = new MessageModel<string>();
            var insertList = new List<EquipmentActionEntity>();

            var delActionPropertyValueList = new List<ActionPropertyValueEntity>();
            var delEquipmentInterlockList = new List<EquipmentInterlockEntity>();

            if (string.IsNullOrEmpty(EquipmentId))
                return returnData;

            if (requestList.Select(a => a.EquipmentId).Distinct().Count() > 1)
            {
                returnData.msg = "模型数据异常！";
                return returnData;
            }

            /*获取前端激活状态的Action*/
            var newActiveActionList = requestList.FindAll(a => a.ActionActiveStatus == "1").ToList();

            /*查询模型已存在的Action属性*/
            var oldlist = await _dal.FindList(a => a.EquipmentId == EquipmentId);


            foreach (var newAction in newActiveActionList)
            {
                /*检查模型与Function关系是否存在*/
                var funtionRelation = await _equipmentFunctionServices.FindEntity(newAction.EquipmentFunctionId);
                if (funtionRelation == null)
                    continue;

                /*已包含的Action不处理*/
                if (oldlist.Select(a => a.ActionId).Contains(newAction.ActionId))
                    continue;


                EquipmentActionEntity entity = new EquipmentActionEntity();
                entity.ActionId = newAction.ActionId;
                entity.EquipmentFunctionId = newAction.EquipmentFunctionId;
                entity.CreateCustomGuid(_user.Name);
                insertList.Add(entity);
            }

            /*获取之前为激活状态，但本次需取消激活的数据*/
            var deleteList = oldlist.Where(a => !newActiveActionList.Select(t => t.ActionId).Contains(a.ActionId)).ToList();

            if (insertList.Count == 0 && deleteList.Count == 0)
            {
                returnData.msg = "无数据更新";
                returnData.success = true;
                return returnData;
            }

            if (deleteList.Count > 0)
            {
                foreach (var action in deleteList)
                {
                    delActionPropertyValueList.AddRange(
                        await _equipmentActionPropertyValueServices.FindList(a => a.EquipmentId == action.EquipmentId && a.ActionId == action.ActionId && a.EquipmnetActionId == action.ID));
                    delEquipmentInterlockList.AddRange(
                        await _equipmentInterlockServices.FindList(a => a.EquipmentId == action.EquipmentId && a.ActionId == action.ActionId && a.EquipmentActionId == action.ID));
                }
            }


            _unitOfWork.BeginTran();

            try
            {
                if (insertList.Count > 0)
                {
                    returnData.success = await _dal.Add(insertList) > 0;
                    if (returnData.success == false)
                    {
                        _unitOfWork.RollbackTran();
                        return returnData;
                    }
                }

                if (deleteList.Count > 0)
                {
                    returnData.success = await _dal.DeleteByIds(deleteList.Select(a => a.ID).ToArray());
                    if (returnData.success == false)
                    {
                        _unitOfWork.RollbackTran();
                        return returnData;
                    }
                }
               

                if (delActionPropertyValueList.Count > 0)
                {
                    returnData.success = await _equipmentActionPropertyValueServices.DeleteByIds(delActionPropertyValueList.Select(a => a.ID).ToArray());
                    if (returnData.success == false)
                    {
                        _unitOfWork.RollbackTran();
                        return returnData;
                    }
                }

                if (delEquipmentInterlockList.Count > 0)
                {
                    returnData.success = await _equipmentInterlockServices.DeleteByIds(delEquipmentInterlockList.Select(a => a.ID).ToArray());
                    if (returnData.success == false)
                    {
                        _unitOfWork.RollbackTran();
                        return returnData;
                    }
                }

                _unitOfWork.CommitTran();

                returnData.success = true;
                return returnData;
            }
            catch (Exception ex)
            {
                SerilogServer.LogError(ex);
                returnData.msg = ex.Message;
                return returnData;
            }
        }

        /// <summary>
        /// 新增模型Action
        /// /// </summary>
        /// <param name="requestList"></param>
        /// <returns></returns>
        public async Task<MessageModel<string>> AddEquipmentAction (string EquipmentId, string ActionId) {

            var returnData = new MessageModel<string>();
            var insertList = new List<EquipmentActionEntity>();

            var delActionPropertyValueList = new List<ActionPropertyValueEntity>();
            var delEquipmentInterlockList = new List<EquipmentInterlockEntity>();

            if (string.IsNullOrEmpty(EquipmentId) || string.IsNullOrEmpty(ActionId))
                return returnData;

           
            var actionEntity = await _actionServices.FindEntity(ActionId);
            if(null == actionEntity)
            {
                return returnData;
            }

            /*检查模型与Function关系是否存在*/
            var equipmentFuntionList = await _equipmentFunctionServices.FindList(a=> a.FunctionId == actionEntity.FunctionId && a.EquipmentId == EquipmentId);
            if (equipmentFuntionList.Count != 1)
                return returnData;
            var equipmentFuntionEntity = equipmentFuntionList[0];

            EquipmentActionEntity entity = new EquipmentActionEntity();
            entity.EquipmentId = EquipmentId;
            entity.ActionId = ActionId;
            entity.EquipmentFunctionId = equipmentFuntionEntity.ID;
            entity.CreateCustomGuid(_user.Name);
            
         
            _unitOfWork.BeginTran();

            try
            {


                returnData.success = await _dal.Add(entity) > 0;
                if (returnData.success == false)
                {
                    _unitOfWork.RollbackTran();
                    return returnData;
                }

                _unitOfWork.CommitTran();

                returnData.success = true;
                return returnData;
            }
            catch (Exception ex)
            {
                SerilogServer.LogError(ex);
                returnData.msg = ex.Message;
                return returnData;
            }
        }

        /// <summary>
        /// 删除模型Action
        /// /// </summary>
        /// <param name="requestList"></param>
        /// <returns></returns>
        public async Task<MessageModel<string>> DelEquipmentAction (string EquipmentActionId) {

            var returnData = new MessageModel<string>();
            var insertList = new List<EquipmentActionEntity>();

            var delActionPropertyValueList = new List<ActionPropertyValueEntity>();
            var delEquipmentInterlockList = new List<EquipmentInterlockEntity>();


            var equipmentActionEntity = await _dal.FindEntity(EquipmentActionId);
            if (null == equipmentActionEntity)
            {
                return returnData;
            }

            delActionPropertyValueList.AddRange(
                                   await _equipmentActionPropertyValueServices.FindList(a => a.EquipmentId == equipmentActionEntity.EquipmentId && a.ActionId == equipmentActionEntity.ActionId && a.EquipmnetActionId == equipmentActionEntity.ID));
            delEquipmentInterlockList.AddRange(
                await _equipmentInterlockServices.FindList(a => a.EquipmentId == equipmentActionEntity.EquipmentId && a.ActionId == equipmentActionEntity.ActionId && a.EquipmentActionId == equipmentActionEntity.ID));

            _unitOfWork.BeginTran();

            try
            {

                if (delActionPropertyValueList.Count > 0)
                {
                    returnData.success = await _equipmentActionPropertyValueServices.DeleteByIds(delActionPropertyValueList.Select(a => a.ID).ToArray());
                    if (returnData.success == false)
                    {
                        _unitOfWork.RollbackTran();
                        return returnData;
                    }
                }

                if (delEquipmentInterlockList.Count > 0)
                {
                    returnData.success = await _equipmentInterlockServices.DeleteByIds(delEquipmentInterlockList.Select(a => a.ID).ToArray());
                    if (returnData.success == false)
                    {
                        _unitOfWork.RollbackTran();
                        return returnData;
                    }
                }

                returnData.success = await _dal.Delete(equipmentActionEntity);
                if (returnData.success == false)
                {
                    _unitOfWork.RollbackTran();
                    return returnData;
                }

                _unitOfWork.CommitTran();

                returnData.success = true;
                return returnData;
            }
            catch (Exception ex)
            {
                SerilogServer.LogError(ex);
                returnData.msg = ex.Message;
                return returnData;
            }
        }
    }
}