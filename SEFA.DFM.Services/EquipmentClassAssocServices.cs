
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;

namespace SEFA.DFM.Services
{
    public class EquipmentClassAssocServices : BaseServices<EquipmentClassAssocEntity>, IEquipmentClassAssocServices
    {
        private readonly IBaseRepository<EquipmentClassAssocEntity> _dal;
        public EquipmentClassAssocServices(IBaseRepository<EquipmentClassAssocEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<EquipmentClassAssocEntity>> GetList(EquipmentClassAssocRequestModel reqModel)
        {
            List<EquipmentClassAssocEntity> result = new List<EquipmentClassAssocEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<EquipmentClassAssocEntity>()
                             .ToExpression();
            var data = await _dal.Db.Queryable<EquipmentClassAssocEntity>()
                .Where(whereExpression).ToListAsync();
            return data;
        }

        public async Task<PageModel<EquipmentClassAssocEntity>> GetPageList(EquipmentClassAssocRequestModel reqModel)
        {
            PageModel<EquipmentClassAssocEntity> result = new PageModel<EquipmentClassAssocEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<EquipmentClassAssocEntity>()
                             .ToExpression();
            var data = await _dal.Db.Queryable<EquipmentClassAssocEntity>()
                .Where(whereExpression)
                .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
            result.dataCount = dataCount;
            result.data = data;
            return result;
        }

        public async Task<bool> SaveForm(EquipmentClassAssocEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}