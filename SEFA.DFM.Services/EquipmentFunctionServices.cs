
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SEFA.DFM.Model.ViewModels;
using SqlSugar;
using System.Collections.Generic;
using System.Linq;
using SEFA.Base.Common.HttpContextUser;
using SEFA.Base.Common.LogHelper;
using System.Threading.Tasks;
using System;
using SEFA.Base.IRepository.UnitOfWork;
using Microsoft.Data.SqlClient;

namespace SEFA.DFM.Services
{
    public class EquipmentFunctionServices : BaseServices<EquipmentFunctionEntity>, IEquipmentFunctionServices
    {
        private readonly IBaseRepository<EquipmentFunctionEntity> _dal;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IUser _user;

        private readonly IBaseRepository<FunctionPropertyValueEntity>  _equipmentFunctionPropertyValueServices;
        private readonly IBaseRepository<EquipmentActionEntity> _equipmentActionServices;
        private readonly IBaseRepository<ActionPropertyValueEntity> _equipmentActionPropertyValueServices;
        private readonly IBaseRepository<EquipmentInterlockEntity> _equipmentInterlockServices;
        private readonly IBaseRepository<FunctionPropertyEntity> _functionPropertyServices;
        public EquipmentFunctionServices (IBaseRepository<EquipmentFunctionEntity> dal,
                                          IUnitOfWork UnitOfWork,
                                          IUser User,
                                          IBaseRepository<FunctionPropertyValueEntity> equipmentFunctionPropertyValueServices,
                                          IBaseRepository<EquipmentActionEntity> equipmentActionServices,
                                          IBaseRepository<ActionPropertyValueEntity> equipmentActionPropertyValueServices,
                                          IBaseRepository<EquipmentInterlockEntity> equipmentInterlockServices,
                                          IBaseRepository<FunctionPropertyEntity> functionPropertyServices) {
            this._dal = dal;
            base.BaseDal = dal;
            this._unitOfWork = UnitOfWork;
            this._user = User;
            _equipmentFunctionPropertyValueServices = equipmentFunctionPropertyValueServices;
            _equipmentActionServices = equipmentActionServices;
            _equipmentActionPropertyValueServices = equipmentActionPropertyValueServices;
            _equipmentInterlockServices = equipmentInterlockServices;
            _functionPropertyServices = functionPropertyServices;
        }

        public async Task<List<EquipmentFunctionModel>> GetEquipmentActiveFunction (string EquipmentId = "") {
            var list = await GetEquipmentFunction(EquipmentId);
            return list.Where(a => a.FunctionActiveStatus == "1").ToList();
        }

        public async Task<List<EquipmentFunctionModel>> GetEquipmentFunction (string EquipmentId, bool showPropertyCount = false) {
            
            var list = new List<EquipmentFunctionModel>();
            list = await _dal.Db.Queryable<EquipmentEntity, FunctionEntity, EquipmentFunctionEntity>((Equ, Fun, EquFun)
                => new object[]
                {
                    JoinType.Inner,Equ.Level == Fun.Level,
                    JoinType.Left,EquFun.FunctionId == Fun.ID && Equ.ID == EquFun.EquipmentId && EquFun.Deleted == 0,
                })
                .WhereIF(!string.IsNullOrEmpty(EquipmentId), (equ) => equ.ID == EquipmentId)
                .Where((equ) => equ.Deleted == 0)
                .Select((Equ, Fun, EquFun) => new EquipmentFunctionModel
                {
                    EquipmentId = Equ.ID,
                    EquipmentCode = Equ.EquipmentCode,
                    EquipmentName = Equ.EquipmentName,
                    EquipmentLevel = Equ.Level,
                    FunctionId = Fun.ID,
                    FunctionCode = Fun.FunctionCode,
                    FunctionName = Fun.FunctoinName,
                    FunctionType = Fun.FunctionType,
                    FunctionActiveStatus = string.IsNullOrEmpty(EquFun.ID) ? "0" :"1",
                    SortOrder = Fun.SortOrder
                })
                .ToListAsync();

            if(showPropertyCount)
            {
                var list2 = _functionPropertyServices.Db.Queryable<FunctionPropertyEntity>().Where(a => list.Select(t => t.FunctionId).Contains(a.FunctionId)).ToList();

                foreach (var item in list)
                {
                    item.FunctionPropertyCount = list2.Where(a => a.FunctionId == item.FunctionId).Count();
                }
            }
           
            return list.OrderBy(a => a.SortOrder).ThenBy(a=> a.FunctionCode).ToList();
        }

        public List<EquipmentFunctionModel> GetEquipmentFunction2 (string EquipmentId) {

            var list = new List<EquipmentFunctionModel>();
            list = _dal.Db.Queryable<EquipmentEntity, FunctionEntity, EquipmentFunctionEntity>((Equ, Fun, EquFun)
                => new object[]
                {
                    JoinType.Inner,Equ.Level == Fun.Level,
                    JoinType.Left,EquFun.FunctionId == Fun.ID && Equ.ID == EquFun.EquipmentId && EquFun.Deleted == 0,
                })
                .WhereIF(!string.IsNullOrEmpty(EquipmentId), (equ) => equ.ID == EquipmentId)
                .Where((equ) => equ.Deleted == 0)
                .Select((Equ, Fun, EquFun) => new EquipmentFunctionModel
                {
                    EquipmentId = Equ.ID,
                    EquipmentCode = Equ.EquipmentCode,
                    EquipmentName = Equ.EquipmentName,
                    EquipmentLevel = Equ.Level,
                    FunctionId = Fun.ID,
                    FunctionCode = Fun.FunctionCode,
                    FunctionName = Fun.FunctoinName,
                    FunctionType = Fun.FunctionType,
                    FunctionActiveStatus = string.IsNullOrEmpty(EquFun.ID) ? "0" : "1"
                })
                .ToList();

          

            return list.OrderBy(a => a.FunctionId).ToList();
        }

        /// <summary>
        /// 设置模型Function
        /// </summary>
        /// <param name="functionList"></param>
        /// <returns></returns>
        public async Task<MessageModel<string>> SaveEquipmentFunction_old (string EquipmentId, List<EquipmentFunctionModel> requestList) {

            var returnData = new MessageModel<string>();
            var insertList = new List<EquipmentFunctionEntity>();

            var delFunctionPropertyValueList = new List<FunctionPropertyValueEntity>();
            var delEquipmentActionList = new List<EquipmentActionEntity>();
            var delActionPropertyValueList = new List<ActionPropertyValueEntity>();
            var delEquipmentInterlockList = new List<EquipmentInterlockEntity>();

            if (string.IsNullOrEmpty(EquipmentId))
                return returnData;

            if (requestList.Select(a => a.EquipmentId).Distinct().Count() > 1)
            {
                returnData.msg = "模型数据异常！";
                return returnData;
            }
            /*前端传入的已激活的列表*/
            var newActiveFunctionList = requestList.Where(a => a.FunctionActiveStatus == "1").ToList();

            /*查询模型已存在的Function*/
            var oldlist = await _dal.FindList(a => a.EquipmentId == EquipmentId);

            /*获取前端传递的模型Function*/
            foreach (var func in newActiveFunctionList)
            {
                /*已包含的Function不处理*/
                if (oldlist.Select(a => a.FunctionId).Contains(func.FunctionId))
                    continue;

                EquipmentFunctionEntity entity = new EquipmentFunctionEntity();

                entity.FunctionId = func.FunctionId;
                entity.EquipmentId = func.EquipmentId;
                entity.CreateCustomGuid(_user.Name);
                insertList.Add(entity);
            }

            /*获取之前为激活状态，但本次需取消激活的数据*/
            var deleteList = oldlist.Where( a => !newActiveFunctionList.Select(t => t.FunctionId).Contains(a.FunctionId)).ToList();

            if(insertList.Count ==0 &&  deleteList.Count ==0)
            {
                returnData.msg = "无数据更新";
                returnData.success = true;
                return returnData;
            }

            if(deleteList.Count > 0)
            {
                foreach (var func in deleteList)
                {
                    delFunctionPropertyValueList.AddRange(
                        await _equipmentFunctionPropertyValueServices.FindList(a => a.EquipmentId == func.EquipmentId && a.EquipmentFunctionId ==  func.ID));
                    delEquipmentActionList.AddRange(
                        await _equipmentActionServices.FindList(a =>a.EquipmentId == func.EquipmentId && a.EquipmentFunctionId == func.ID));
                    delActionPropertyValueList.AddRange(
                        await _equipmentActionPropertyValueServices.FindList(a => a.EquipmentId == func.EquipmentId && a.FunctionId == func.FunctionId));
                    delEquipmentInterlockList.AddRange(
                        await _equipmentInterlockServices.FindList(a => a.EquipmentId == func.EquipmentId && a.FunctionId == func.FunctionId));
                }
            }


            _unitOfWork.BeginTran();

            try
            {
                if (insertList.Count > 0)
                {
                    returnData.success = await _dal.Add(insertList) > 0;
                    if (returnData.success == false)
                    {
                        _unitOfWork.RollbackTran();
                        return returnData;
                    }
                }

                if (deleteList.Count > 0)
                {
                    returnData.success = await _dal.DeleteByIds(deleteList.Select(a => a.ID).ToArray());
                    if (returnData.success == false)
                    {
                        _unitOfWork.RollbackTran();
                        return returnData;
                    }
                }

                if (delFunctionPropertyValueList.Count > 0)
                {
                    returnData.success = await _equipmentFunctionPropertyValueServices.DeleteByIds(delFunctionPropertyValueList.Select(a => a.ID).ToArray());
                    if (returnData.success == false)
                    {
                        _unitOfWork.RollbackTran();
                        return returnData;
                    }
                }

                if (delEquipmentActionList.Count > 0)
                {
                    returnData.success = await _equipmentActionServices.DeleteByIds(delEquipmentActionList.Select(a => a.ID).ToArray());
                    if (returnData.success == false)
                    {
                        _unitOfWork.RollbackTran();
                        return returnData;
                    }
                }

                if (delActionPropertyValueList.Count > 0)
                {
                    returnData.success = await _equipmentActionPropertyValueServices.DeleteByIds(delActionPropertyValueList.Select(a => a.ID).ToArray());
                    if (returnData.success == false)
                    {
                        _unitOfWork.RollbackTran();
                        return returnData;
                    }
                }

                if (delEquipmentInterlockList.Count > 0)
                {
                    returnData.success = await _equipmentInterlockServices.DeleteByIds(delEquipmentInterlockList.Select(a => a.ID).ToArray());
                    if (returnData.success == false)
                    {
                        _unitOfWork.RollbackTran();
                        return returnData;
                    }
                }

                _unitOfWork.CommitTran();

                returnData.success = true;
                return returnData;
            }
            catch (Exception ex)
            {
                SerilogServer.LogError(ex);
                returnData.msg = ex.Message;
                return returnData;
            }
        }

        /// <summary>
        /// 设置模型Function,逻辑删除
        /// </summary>
        /// <param name="functionList"></param>
        /// <returns></returns>
        public async Task<MessageModel<string>> SaveEquipmentFunction (string EquipmentId, List<EquipmentFunctionModel> requestList) {

            var returnData = new MessageModel<string>();
            var insertList = new List<EquipmentFunctionEntity>();
            var updateList = new List<EquipmentFunctionEntity>();

            var delFunctionPropertyValueList = new List<FunctionPropertyValueEntity>();
            var delEquipmentActionList = new List<EquipmentActionEntity>();
            var delActionPropertyValueList = new List<ActionPropertyValueEntity>();
            var delEquipmentInterlockList = new List<EquipmentInterlockEntity>();

            if (string.IsNullOrEmpty(EquipmentId))
                return returnData;

            if (requestList.Select(a => a.EquipmentId).Distinct().Count() > 1)
            {
                returnData.msg = "模型数据异常！";
                return returnData;
            }
            /*前端传入的已激活的列表*/
            var newActiveFunctionList = requestList.Where(a => a.FunctionActiveStatus == "1").ToList();

            /*查询模型已存在的Function*/
            var oldlist = await _dal.FindList(a => a.EquipmentId == EquipmentId);

            /*获取前端传递的模型Function*/
            foreach (var func in newActiveFunctionList)
            {
                /*已包含的Function不处理*/
                var existFuntion = oldlist.Where(a => a.FunctionId == func.FunctionId).FirstOrDefault();
                if (existFuntion != null)
                { 
                    /*已存在的，查看是否是已经逻辑删除*/
                    if (existFuntion.Deleted == 1)
                    {
                        /*重新激活*/
                        existFuntion.Deleted = 0;
                        existFuntion.Modify(existFuntion.ID, "system");
                        updateList.Add(existFuntion);
                    }
                    else
                    {
                        /*未删除，跳过*/
                        continue;
                    }
                }
                else
                {
                    /*不存在的，新增*/
                    EquipmentFunctionEntity entity = new EquipmentFunctionEntity();

                    entity.FunctionId = func.FunctionId;
                    entity.EquipmentId = func.EquipmentId;
                    entity.CreateCustomGuid(_user.Name);
                    insertList.Add(entity);
                }
            }

            /*获取之前为激活状态，但本次需取消激活的数据*/
            var deleteList = 
                oldlist.Where(a => !newActiveFunctionList.Select(t => t.FunctionId).Contains(a.FunctionId) && a.Deleted == 0).ToList();

            if (insertList.Count == 0 && deleteList.Count == 0 && updateList.Count == 0)
            {
                returnData.msg = "无数据更新";
                returnData.success = true;
                return returnData;
            }

            if (deleteList.Count > 0)
            {
                foreach (var func in deleteList)
                {
                    func.Deleted = 1;
                    func.Modify(func.ID, "system");
                }
            }


            _unitOfWork.BeginTran();

            try
            {
                if (insertList.Count > 0)
                {
                    returnData.success = await _dal.Add(insertList) > 0;
                    if (returnData.success == false)
                    {
                        _unitOfWork.RollbackTran();
                        return returnData;
                    }
                }
                if (updateList.Count > 0)
                {
                    returnData.success = await _dal.Update(updateList);
                    if (returnData.success == false)
                    {
                        _unitOfWork.RollbackTran();
                        return returnData;
                    }
                }
                if (deleteList.Count > 0)
                {
                    returnData.success = await _dal.Update(deleteList);
                    if (returnData.success == false)
                    {
                        _unitOfWork.RollbackTran();
                        return returnData;
                    }
                }

             
                _unitOfWork.CommitTran();

                returnData.success = true;
                return returnData;
            }
            catch (Exception ex)
            {
                SerilogServer.LogError(ex);
                returnData.msg = ex.Message;
                return returnData;
            }
        }





    }
}