
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using SEFA.Base.Common.WebApiClients.HttpApis;
using System;
using StackExchange.Profiling.Internal;

namespace SEFA.DFM.Services
{
    public class EquipmentGroupEquipServices : BaseServices<EquipmentGroupEquipEntity>, IEquipmentGroupEquipServices
    {
        private readonly IBaseRepository<EquipmentGroupEquipEntity> _dal;
        private readonly IBaseRepository<EquipmentEntity> _equipmentDal;
        public EquipmentGroupEquipServices(IBaseRepository<EquipmentGroupEquipEntity> dal, IBaseRepository<EquipmentEntity> equipmentDal)
        {
            this._dal = dal;
            base.BaseDal = dal;
            _equipmentDal = equipmentDal;
        }

        public async Task<List<EquipmentGroupEquipEntity>> GetList(EquipmentGroupEquipRequestModel reqModel)
        {
            List<EquipmentGroupEquipEntity> result = new List<EquipmentGroupEquipEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<EquipmentGroupEquipEntity>()
                             .ToExpression();
            var data = await _dal.Db.Queryable<EquipmentGroupEquipEntity>()
                .Where(whereExpression).ToListAsync();
            return data;
        }

        public async Task<PageModel<EquipmentGroupEquModel>> GetPageList(EquipmentGroupEquipRequestModel reqModel)
        {
            PageModel<EquipmentGroupEquModel> result = new PageModel<EquipmentGroupEquModel>();
            RefAsync<int> dataCount = 0;

            var list = await _dal.Db.Queryable<EquipmentGroupEquipEntity, EquipmentEntity>((ege, e)
              => new object[]
              {
                  JoinType.Inner, ege.EquipmentRowId == e.ID
              })

              .Select((ege, e) => new EquipmentGroupEquModel
              {
                  ID = ege.ID,
                  CreateDate = ege.CreateDate,
                  CreateUserId = ege.CreateUserId,
                  ModifyDate = ege.ModifyDate,
                  ModifyUserId = ege.ModifyUserId,
                  EquipmentGroupRowId = ege.EquipmentGroupRowId,
                  EquipmentRowId = ege.EquipmentRowId,
                  EquipmentCode = e.EquipmentCode,
                  EquipmentName = e.EquipmentName,
                  Type = e.Level,
                  SortOrder = ege.SortOrder,
                  Status = ege.Status,
                  ParentId = e.ParentId
              })
              .MergeTable()
              .WhereIF(!string.IsNullOrEmpty(reqModel.EquipmentGroupRowId), a => a.EquipmentGroupRowId == reqModel.EquipmentGroupRowId)
              .WhereIF(!string.IsNullOrEmpty(reqModel.Key), a => (a.EquipmentCode != null && a.EquipmentCode.Contains(reqModel.Key))|| (a.EquipmentName != null && a.EquipmentName.Contains(reqModel.Key)))
              .ToListAsync();

           

            if (list.Any())
            {
                var equList = await _equipmentDal.FindList(a => a.Deleted == 0 && a.Enabled == 1);

                foreach (var item in list)
                {
                    if (item.Type == DFMCommonModel.Level.Line)
                    {
                        item.LineId = item.ID;
                        item.LineName = item.EquipmentName;
                    }
                    else
                    {
                        EquipmentEntity parentEntity = null;
                        if (!string.IsNullOrEmpty(item.ParentId))
                        {
                            parentEntity = equList.Where(a => a.ID == item.ParentId).FirstOrDefault();
                            if (parentEntity != null)
                            {
                                while (true)
                                {
                                    if (parentEntity == null)
                                        break;
                                    if (parentEntity.Level == DFMCommonModel.Level.Line)
                                        break;
                                    if (string.IsNullOrEmpty(parentEntity.ParentId) || parentEntity.ParentId == "0")
                                        break;
                                    else
                                        parentEntity = equList.Where(a => a.ID == parentEntity.ParentId).FirstOrDefault();
                                }
                            }
                        }
                        if (parentEntity != null)
                        {
                            item.LineId = parentEntity.ID;
                            item.LineName = parentEntity.EquipmentName;
                        }
                    }
                }
            }

            if (!string.IsNullOrEmpty(reqModel.LineId))
            {
                list = list.Where(a => a.LineId == reqModel.LineId).ToList();
            }
            dataCount = list.Count;
            var gList = list.Skip((reqModel.pageIndex - 1) * reqModel.pageSize).Take(reqModel.pageSize).OrderBy(a=>a.SortOrder).ToList();


            result.dataCount = dataCount;
            result.pageSize = reqModel.pageSize;
            result.data = gList;
            return result;
        }

        public async Task<bool> SaveForm(EquipmentGroupEquipEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}