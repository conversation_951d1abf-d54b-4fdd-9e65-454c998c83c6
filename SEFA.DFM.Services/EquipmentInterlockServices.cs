
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SEFA.DFM.Model.ViewModels;
using System.Collections.Generic;
using System.Threading.Tasks;
using OfficeOpenXml.FormulaParsing.Excel.Functions.DateTime;
using SqlSugar;
using Abp.Collections;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.Base.Common.HttpContextUser;
using System.Linq;
using Microsoft.Extensions.Logging;
using SEFA.Base.Common.LogHelper;
using System;
using Microsoft.AspNetCore.Mvc.ApplicationModels;

namespace SEFA.DFM.Services
{
    public class EquipmentInterlockServices : BaseServices<EquipmentInterlockEntity>, IEquipmentInterlockServices
    {
        private readonly IBaseRepository<EquipmentInterlockEntity> _dal;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IUser _user;
       
        public EquipmentInterlockServices(IBaseRepository<EquipmentInterlockEntity> dal, IUnitOfWork UnitOfWork, IUser User)
        {
            this._dal = dal;
            base.BaseDal = dal;
            this._unitOfWork = UnitOfWork;
            this._user = User;
        }


        /// <summary>
        /// 获取模型Interlock信息
        /// </summary>
        /// <param name="batchno"></param>
        /// <returns></returns>
        public async Task<List<EquipmentInterLockModel>> GetEquipmentInterLock (string EquipmentActionId, string ActionCode = "") {
            var list = new List<EquipmentInterLockModel>();
           
            list = await _dal.Db.Queryable<EquipmentEntity, EquipmentFunctionEntity, FunctionEntity, EquipmentActionEntity,ActionEntity,InterlockEntity,EquipmentInterlockEntity>((Equ,EquFun,Fun,EquAct,Act,Interlock,EquInterlock) 
                =>new object[] 
                {
                    JoinType.Inner,Equ.ID == EquFun.EquipmentId,
                    JoinType.Inner,EquFun.FunctionId == Fun.ID && EquFun.Deleted == 0,
                    JoinType.Inner,EquAct.EquipmentFunctionId == EquFun.ID,
                    JoinType.Inner,Act.ID == EquAct.ActionId,
                    JoinType.Inner,Interlock.ActionId == Act.ID,
                    JoinType.Left, EquInterlock.EquipmentId == Equ.ID && Act.ID == EquInterlock.ActionId && EquInterlock.InterlockId == Interlock.ID && EquAct.ID == EquInterlock.EquipmentActionId,
                })
            .Select((Equ, EquFun, Fun, EquAct, Act, Interlock, EquInterlock) => new EquipmentInterLockModel
            {
                ID = EquInterlock.ID,
                EquipmentId = Equ.ID,
                EquipmentCode = Equ.EquipmentCode,
                EquipmentName = Equ.EquipmentName,
                EquipmentLevel = Equ.Level,
                FunctionId =Fun.ID,
                FunctionCode =Fun.FunctionCode,
                FunctionName = Fun.FunctoinName,
                FunctionType = Fun.FunctionType,
                ActionId = Act.ID,
                ActionCode = Act.ActionCode, 
                ActionName = Act.ActionName,
                InterLockId = Interlock.ID,
                InterLockCode = Interlock.Code,
                InterLockName = Interlock.Name,
                DefaultStatus = Interlock.Status,
                ActualStatus = string.IsNullOrEmpty(EquInterlock.Status)? Interlock.Status : EquInterlock.Status,
                EquipmentActionId = EquAct.ID,
                EquipmentFunctionId=EquFun.ID
            })
            .MergeTable()
            .Where((a) => a.EquipmentActionId == EquipmentActionId)
            .WhereIF(!string.IsNullOrEmpty(ActionCode), (a) => a.ActionCode == ActionCode)
            .ToListAsync();

            return list.OrderBy(a => a.FunctionId).OrderBy(a => a.ActionId).ToList();
        }

        /// <summary>
        /// 设置模型Interlock值
        /// </summary>
        /// <param name="interlocklist"></param>
        /// <returns></returns>
        public async Task<MessageModel<string>> SaveEquipmentInterLock(List<EquipmentInterLockModel> interlocklist) {

            var returnData = new MessageModel<string>();
            var insertList = new List<EquipmentInterlockEntity>();
            if (interlocklist.Count <= 0)
                return returnData;

            if(interlocklist.Select(a=>a.EquipmentId).Distinct().Count() >1)
            {
                returnData.msg = "模型数据异常！";
                return returnData;
            }

            /*获取模型默认值不一致的Interlock属性*/
            var changeList = interlocklist.FindAll(a => a.DefaultStatus != a.ActualStatus).ToList();

            if(changeList.Count > 0)
            {
                foreach(var change in changeList)
                {
                    EquipmentInterlockEntity entity = new EquipmentInterlockEntity();
                    entity.InterlockId = change.InterLockId;
                    entity.EquipmentId = change.EquipmentId;
                    entity.FunctionId = change.FunctionId;
                    entity.ActionId = change.ActionId;
                    entity.Code = change.InterLockCode;
                    entity.Name = change.InterLockName;
                    entity.EquipmentActionId = change.EquipmentActionId;
                    entity.Status = change.ActualStatus;
                    entity.CreateCustomGuid(_user.Name);
                    insertList.Add(entity);
                }
            }

            /*查询模型已存在的Interlock属性*/
            string EquipmentId = interlocklist[0].EquipmentId;
            var oldlist = await _dal.FindList(a=>a.EquipmentId == EquipmentId);

            _unitOfWork.BeginTran();

            try
            {
                if (oldlist.Count > 0)
                {
                    returnData.success = await _dal.DeleteByIds(oldlist.Select(a => a.ID).ToArray());
                    if (returnData.success == false)
                    {
                        _unitOfWork.RollbackTran();
                        return returnData;
                    }
                }
                if (insertList.Count > 0)
                {
                    returnData.success = await _dal.Add(insertList) > 0;
                    if (returnData.success == false)
                    {
                        _unitOfWork.RollbackTran();
                        return returnData;
                    }
                }

                _unitOfWork.CommitTran();

                returnData.success = true;
                return returnData;
            }
            catch(Exception ex)
            {
                SerilogServer.LogError(ex);
                returnData.msg = ex.Message;
                return returnData;
            }
        }


        /// <summary>
        /// 设置模型Interlock值
        /// </summary>
        /// <param name="interlockEntity"></param>
        /// <returns></returns>
        public async Task<MessageModel<string>> SaveEquipmentInterLock (EquipmentInterLockModel interlockEntity) {

            var returnData = new MessageModel<string>();
            var insertList = new List<EquipmentInterlockEntity>();
            var oldList = new List<EquipmentInterlockEntity>();

            if (string.IsNullOrEmpty(interlockEntity.ID))
            {
                if(interlockEntity.ActualStatus != interlockEntity.DefaultStatus)
                {
                    EquipmentInterlockEntity entity = new EquipmentInterlockEntity();
                    entity.InterlockId = interlockEntity.InterLockId;
                    entity.EquipmentId = interlockEntity.EquipmentId;
                    entity.FunctionId = interlockEntity.FunctionId;
                    entity.ActionId = interlockEntity.ActionId;
                    entity.Code = interlockEntity.InterLockCode;
                    entity.Name = interlockEntity.InterLockName;
                    entity.EquipmentActionId = interlockEntity.EquipmentActionId;
                    entity.Status = interlockEntity.ActualStatus;
                    entity.CreateCustomGuid(_user.Name);

                    insertList.Add(entity);
                }
            }
            else
            {
                if (interlockEntity.ActualStatus == interlockEntity.DefaultStatus)
                {
                    oldList = await _dal.FindList(a => a.ID == interlockEntity.ID);
                }
            }
        
            _unitOfWork.BeginTran();

            try
            {
                if (oldList.Count > 0)
                {
                    returnData.success = await _dal.DeleteByIds(oldList.Select(a => a.ID).ToArray());
                    if (returnData.success == false)
                    {
                        _unitOfWork.RollbackTran();
                        return returnData;
                    }
                }
                if (insertList.Count > 0)
                {
                    returnData.success = await _dal.Add(insertList) > 0;
                    if (returnData.success == false)
                    {
                        _unitOfWork.RollbackTran();
                        return returnData;
                    }
                }

                _unitOfWork.CommitTran();

                returnData.success = true;
                return returnData;
            }
            catch (Exception ex)
            {
                SerilogServer.LogError(ex);
                returnData.msg = ex.Message;
                return returnData;
            }
        }

    }
}