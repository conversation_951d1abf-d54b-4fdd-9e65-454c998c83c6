
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;

namespace SEFA.DFM.Services
{
    public class EquipmentRequirementServices : BaseServices<EquipmentRequirementEntity>, IEquipmentRequirementServices
    {
        private readonly IBaseRepository<EquipmentRequirementEntity> _dal;
        public EquipmentRequirementServices(IBaseRepository<EquipmentRequirementEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<EquipmentRequirementEntity>> GetList(EquipmentRequirementRequestModel reqModel)
        {
            List<EquipmentRequirementEntity> result = new List<EquipmentRequirementEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<EquipmentRequirementEntity>()
                             .ToExpression();
            var data = await _dal.Db.Queryable<EquipmentRequirementEntity>()
                .Where(whereExpression).ToListAsync();
            return data;
        }

        public async Task<PageModel<EquipmentRequirementEntity>> GetPageList(EquipmentRequirementRequestModel reqModel)
        {
            PageModel<EquipmentRequirementEntity> result = new PageModel<EquipmentRequirementEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<EquipmentRequirementEntity>()
                             .ToExpression();
            var data = await _dal.Db.Queryable<EquipmentRequirementEntity>()
                .Where(whereExpression)
                .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
            result.dataCount = dataCount;
            result.data = data;
            return result;
        }

        public async Task<bool> SaveForm(EquipmentRequirementEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}