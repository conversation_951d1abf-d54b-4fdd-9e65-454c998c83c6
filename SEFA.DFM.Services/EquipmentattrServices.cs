
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;

namespace SEFA.DFM.Services
{
    public class EquipmentattrServices : BaseServices<EquipmentattrEntity>, IEquipmentattrServices
    {
        private readonly IBaseRepository<EquipmentattrEntity> _dal;
        public EquipmentattrServices(IBaseRepository<EquipmentattrEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }
    }
}