using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;

namespace SEFA.DFM.Services
{
    public class FactorySiteServices : BaseServices<FactorySiteEntity>, IFactorySiteServices
    {
        private readonly IBaseRepository<FactorySiteEntity> _dal;
        public FactorySiteServices(IBaseRepository<FactorySiteEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }
    }
}