
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using System.Threading.Tasks;
using SEFA.Base.Common.WebApiClients.HttpApis;
using SqlSugar;

namespace SEFA.DFM.Services
{
    public class FulllineGoalServices : BaseServices<FulllineGoalEntity>, IFulllineGoalServices
    {
        private readonly IBaseRepository<FulllineGoalEntity> _dal;
        public FulllineGoalServices(IBaseRepository<FulllineGoalEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        /// <summary>
        /// 同步KPI
        /// </summary>
        /// <param name="linecode"></param>
        /// <returns></returns>
        public async Task<int> Synchronization(string linecode, string SegmentCode, string LineIndexCode, string id)
        {
            var Param = new SugarParameter("@Product_Line", linecode);
            var Segment = new SugarParameter("@Segment", SegmentCode);
            var LineIndexCODE = new SugarParameter("@LineIndexCODE", LineIndexCode);
            var data = await _dal.Db.Ado.ExecuteCommandAsync("sp_SaveProductLineTargetByRPT @Product_Line,@Segment,@LineIndexCODE", Param, Segment, LineIndexCODE);//同步KPI

            if (data > 0)
            {
                var updata = await _dal.Db.Updateable<FulllineGoalEntity>()
               .SetColumns(it => new FulllineGoalEntity() { Operated = 1 })
               .Where(it => it.ID == id)
               .ExecuteCommandAsync();
            }

            return data;
        }


    }
}