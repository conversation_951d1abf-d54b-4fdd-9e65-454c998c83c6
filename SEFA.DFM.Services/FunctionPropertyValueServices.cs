
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Common.HttpContextUser;
using SEFA.Base.Common.LogHelper;
using SEFA.Base.Model;
using SEFA.DFM.Model.ViewModels;
using SqlSugar;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System;
using SEFA.Base.IRepository.UnitOfWork;
using System.DirectoryServices.ActiveDirectory;
using SEFA.DFM.Model;

namespace SEFA.DFM.Services
{
    public class FunctionPropertyValueServices : BaseServices<FunctionPropertyValueEntity>, IFunctionPropertyValueServices
    {
        private readonly IBaseRepository<FunctionPropertyValueEntity> _dal;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IUser _user;
        public FunctionPropertyValueServices (IBaseRepository<FunctionPropertyValueEntity> dal, IUnitOfWork UnitOfWork, IUser User) {
            this._dal = dal;
            base.BaseDal = dal;
            this._unitOfWork = UnitOfWork;
            this._user = User;
        }


        /// <summary>
        /// 获取模型Action属性信息
        /// </summary>
        /// <param name="batchno"></param>
        /// <returns></returns>
        public async Task<List<EquipmentFunctionPropertyModel>> GetEquipmentActiveFunctionProperty (EquipmentFunctionModel model) {
           
            var list = new List<EquipmentFunctionPropertyModel>();

            try
            {
                list = await _dal.Db.Queryable<EquipmentEntity, EquipmentFunctionEntity, FunctionEntity,FunctionPropertyEntity, FunctionPropertyValueEntity>((Equ, EquFun, Fun , FunP ,FunPv)
                => new object[]
                {
                    JoinType.Inner,Equ.ID == EquFun.EquipmentId,
                    JoinType.Inner,EquFun.FunctionId == Fun.ID && EquFun.Deleted == 0,
                    JoinType.Inner,FunP.FunctionId == Fun.ID,
                    JoinType.Left, FunPv.EquipmentId == Equ.ID && FunPv.FunctionId == Fun.ID && FunPv.PropertyId == FunP.ID && FunPv.EquipmentFunctionId == EquFun.ID,
                })
                .Select((Equ, EquFun, Fun, FunP, FunPv) => new EquipmentFunctionPropertyModel
                {
                    EquipmentId = Equ.ID,
                    EquipmentCode = Equ.EquipmentCode,
                    EquipmentName = Equ.EquipmentName,
                    EquipmentLevel = Equ.Level,
                    FunctionId = Fun.ID,
                    FunctionCode = Fun.FunctionCode,
                    FunctionName = Fun.FunctoinName,
                    FunctionType = Fun.FunctionType,
                    PropertyId = FunP.ID,
                    PropertyCode = FunP.PropertyCode,
                    PropertyName = FunP.PropertyName,
                    PropertyType = FunP.PropertyType,
                    DefaultValue = FunP.DefaultValue,
                    ActualValue = string.IsNullOrEmpty(FunPv.PropertyValue) ? FunP.DefaultValue : FunPv.PropertyValue,
                    EquipmentFunctionId = EquFun.ID
                })
                .MergeTable()
                .WhereIF(!string.IsNullOrEmpty(model.EquipmentId), (a) => a.EquipmentId == model.EquipmentId)
                .WhereIF(!string.IsNullOrEmpty(model.FunctionCode), (a) => a.FunctionCode == model.FunctionCode)
                .WhereIF(!string.IsNullOrEmpty(model.FunPropertyCode), (a) => a.PropertyCode == model.FunPropertyCode)
                .ToListAsync();
            }
            catch (Exception ex)
            {
                return list;
            }

            foreach (var item in list.Where(a => a.PropertyType == EnumClass.PropertyType.Type_StringList))
            {
                item.PropertyValueList = _dal.Db.Queryable<PropertyValueListEntity>().Where(a => a.PropertyId == item.PropertyId).Select(a => a.ItemValue).ToList();
            }

            return list.OrderBy(a => a.FunctionId).OrderBy(a => a.PropertyId).ToList();
        }


    

        /// <summary>
        /// 获取模型Function属性信息
        /// </summary>
        /// <param name="batchno"></param>
        /// <returns></returns>
        public async Task<List<EquFunctionPropertyModel>> GetEquActiveFunctionPropertyValueList (EquipmentFunctionModel reqModel) {

            var returnList = new List<EquFunctionPropertyModel>();

            var list = await GetEquipmentActiveFunctionProperty(reqModel);

            var funList = list.Select(a => a.FunctionId).Distinct();

            foreach (var funId in funList)
            {
                EquFunctionPropertyModel model = new EquFunctionPropertyModel();
                model.EquipmentId = reqModel.EquipmentId;
                model.EquipmentFunctionId = list.Where(a => a.FunctionId == funId).FirstOrDefault().EquipmentFunctionId;
                model.FunctionId = list.Where(a => a.FunctionId == funId).FirstOrDefault().FunctionId;
                model.FunctionCode = list.Where(a => a.FunctionId == funId).FirstOrDefault().FunctionCode;
                model.FunctionName = list.Where(a => a.FunctionId == funId).FirstOrDefault().FunctionName;
                model.ActiveFunctionPropertyList = list.Where(a => a.FunctionId == funId).ToList();

                returnList.Add(model);
            }
            return returnList;
        }

        /// <summary>
        /// 设置模型Function属性值
        /// </summary>
        /// <param name="functionProrertyValueList"></param>
        /// <returns></returns>
        public async Task<MessageModel<string>> SaveEquActiveFunctionPropertyValueList (List<EquipmentFunctionPropertyModel> functionProrertyValueList) {

            var returnData = new MessageModel<string>();
            var insertList = new List<FunctionPropertyValueEntity>();
            if (functionProrertyValueList.Count <= 0)
                return returnData;

            if (functionProrertyValueList.Select(a => a.EquipmentId).Distinct().Count() > 1)
            {
                returnData.msg = "传入模型数据异常！";
                return returnData;
            }

            if (functionProrertyValueList.GroupBy(a => new { a.EquipmentId,a.FunctionId }).Count() > 1)
            {
                returnData.msg = "传入模型FunctionProperty数据异常！";
                return returnData;
            }

            /*获取模型默认值不一致的Function属性*/
            var changeList = functionProrertyValueList.FindAll(a => a.DefaultValue != a.ActualValue).ToList();

            if (changeList.Count > 0)
            {
                foreach (var change in changeList)
                {
                    FunctionPropertyValueEntity entity = new FunctionPropertyValueEntity();
                    entity.PropertyId = change.PropertyId;
                    entity.EquipmentId = change.EquipmentId;
                    entity.FunctionId = change.FunctionId;
                    entity.EquipmentFunctionId = change.EquipmentFunctionId;
                    entity.PropertyValue = change.ActualValue;
                    entity.CreateCustomGuid(_user.Name);
                    insertList.Add(entity);
                }
            }

            /*查询模型已存在对应的Function属性*/
            string EquipmentId = functionProrertyValueList[0].EquipmentId;
            string EquipmentFunctionId = functionProrertyValueList[0].EquipmentFunctionId;
            var oldlist = await _dal.FindList(a => a.EquipmentFunctionId == EquipmentFunctionId);

            _unitOfWork.BeginTran();

            try
            {
                if (oldlist.Count > 0)
                {
                    returnData.success = await _dal.DeleteByIds(oldlist.Select(a => a.ID).ToArray());
                    if (returnData.success == false)
                    {
                        _unitOfWork.RollbackTran();
                        return returnData;
                    }
                }
                if (insertList.Count > 0)
                {
                    returnData.success = await _dal.Add(insertList) > 0;
                    if (returnData.success == false)
                    {
                        _unitOfWork.RollbackTran();
                        return returnData;
                    }
                }

                _unitOfWork.CommitTran();

                returnData.success = true;
                return returnData;
            }
            catch (Exception ex)
            {
                SerilogServer.LogError(ex);
                returnData.msg = ex.Message;
                return returnData;
            }
        }
    }
}