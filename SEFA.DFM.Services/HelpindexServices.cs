
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using System.Collections.Generic;
using System.Threading.Tasks;
using SEFA.DFM.Model.ViewModels;
using SEFA.DFM.IRepository;
using System.Linq.Expressions;
using System;

namespace SEFA.DFM.Services
{
    public class HelpindexServices : BaseServices<HelpindexEntity>, IHelpindexServices
    {
        private readonly IHelpindexRepository _dal;
        public HelpindexServices(IHelpindexRepository dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }


        public async Task<List<TreeModel>> GetHelpindexTree()
        {
            Expression<Func<HelpindexEntity, bool>> whereExpression = a => true;

            List<HelpindexEntity> helpindices = await _dal.FindList(whereExpression);

            //获取树形结构
            List<TreeModel> treeList = new List<TreeModel>();
            foreach (var item in helpindices)
            {

                TreeModel node = new TreeModel
                {
                    id = item.ID,
                    name = item.Name,
                    value = "1",
                    showcheck = false,
                    checkstate = 0,
                    isexpand = false,
                    parentId = item.Parentid
                };
                treeList.Add(node);
            }

            return treeList;
        }


    }
}