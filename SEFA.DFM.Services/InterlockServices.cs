
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;

namespace SEFA.DFM.Services
{
    public class InterlockServices : BaseServices<InterlockEntity>, IInterlockServices
    {
        private readonly IBaseRepository<InterlockEntity> _dal;
        public InterlockServices(IBaseRepository<InterlockEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }
    }
}