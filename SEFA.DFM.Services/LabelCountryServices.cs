
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.DFM.IRepository;

namespace SEFA.DFM.Services
{
    public class LabelCountryServices : BaseServices<LabelCountryEntity>, ILabelCountryServices
    {
        private readonly IBaseRepository<LabelCountryEntity> _dal;
        private readonly ILabelCountryMOverrideRepository _labelCountryMOverrideRepository;
        private readonly IUnitOfWork _unitOfWork;
        public LabelCountryServices(IBaseRepository<LabelCountryEntity> dal,
            ILabelCountryMOverrideRepository labelCountryMOverrideRepository,
            IUnitOfWork unitOfWork)
        {
            this._dal = dal;
            this._labelCountryMOverrideRepository = labelCountryMOverrideRepository;
            this._unitOfWork = unitOfWork;
            base.BaseDal = dal;
        }

        public async Task<List<LabelCountryEntity>> GetList(LabelCountryRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<LabelCountryEntity>()
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }

        public async Task<PageModel<LabelCountryEntity>> GetPageList(LabelCountryRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<LabelCountryEntity>()
                             .ToExpression();
            var data = await _dal.QueryPage(whereExpression,reqModel.pageIndex,reqModel.pageSize);
              
            return data;
        }


        public async Task<bool> DeleteLabelCountryByIds(string[] labelCountryIds)
        {
            bool result = false;
            if (labelCountryIds.Length > 0)
            {
                List<LabelCountryMOverrideEntity> labelCountryMOverrideEntities = await _labelCountryMOverrideRepository
                    .FindList(a => labelCountryIds.Contains(a.CountryId));
                if (labelCountryMOverrideEntities.Any())
                {
                    _unitOfWork.BeginTran();
                    result = await _labelCountryMOverrideRepository.DeleteByIds(labelCountryMOverrideEntities.Select(a=>a.ID).ToList().ToArray());
                    if (result)
                    {
                        result = await _dal.DeleteByIds(labelCountryIds);
                    }
                    else
                    {
                        _unitOfWork.RollbackTran();
                        return result;
                    }
                    _unitOfWork.CommitTran();
                }
                else
                {
                    result = await _dal.DeleteByIds(labelCountryIds);
                }
            }
            return result;
        }
    }
}