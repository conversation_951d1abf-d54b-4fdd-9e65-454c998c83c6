
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;

namespace SEFA.DFM.Services
{
    public class LabelFormatServices : BaseServices<LabelFormatEntity>, ILabelFormatServices
    {
        private readonly IBaseRepository<LabelFormatEntity> _dal;
        public LabelFormatServices(IBaseRepository<LabelFormatEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<LabelFormatEntity>> GetList(LabelFormatRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<LabelFormatEntity>()
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }

        public async Task<PageModel<LabelFormatEntity>> GetPageList(LabelFormatRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<LabelFormatEntity>()
                             .ToExpression();
            var data = await _dal.QueryPage(whereExpression,reqModel.pageIndex,reqModel.pageSize);
              
            return data;
        }

    }
}