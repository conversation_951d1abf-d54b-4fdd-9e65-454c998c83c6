
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;

namespace SEFA.DFM.Services
{
    public class LabelPrinterClassServices : BaseServices<LabelPrinterClassEntity>, ILabelPrinterClassServices
    {
        private readonly IBaseRepository<LabelPrinterClassEntity> _dal;
        public LabelPrinterClassServices(IBaseRepository<LabelPrinterClassEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<LabelPrinterClassEntity>> GetList(LabelPrinterClassRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<LabelPrinterClassEntity>()
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }

        public async Task<PageModel<LabelPrinterClassEntity>> GetPageList(LabelPrinterClassRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<LabelPrinterClassEntity>()
                             .ToExpression();
            var data = await _dal.QueryPage(whereExpression,reqModel.pageIndex,reqModel.pageSize);
              
            return data;
        }

    }
}