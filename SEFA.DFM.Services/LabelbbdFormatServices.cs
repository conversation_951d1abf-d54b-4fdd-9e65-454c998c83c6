
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;

namespace SEFA.DFM.Services
{
    public class LabelbbdFormatServices : BaseServices<LabelbbdFormatEntity>, ILabelbbdFormatServices
    {
        private readonly IBaseRepository<LabelbbdFormatEntity> _dal;
        public LabelbbdFormatServices(IBaseRepository<LabelbbdFormatEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<LabelbbdFormatEntity>> GetList(LabelbbdFormatRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<LabelbbdFormatEntity>()
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }

        public async Task<PageModel<LabelbbdFormatEntity>> GetPageList(LabelbbdFormatRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<LabelbbdFormatEntity>()
                             .ToExpression();
            var data = await _dal.QueryPage(whereExpression,reqModel.pageIndex,reqModel.pageSize);
              
            return data;
        }
    }
}