
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using System.Threading.Tasks;
using System.Collections.Generic;
using SqlSugar;
using System.Linq;

namespace SEFA.DFM.Services
{
    public class MaterialBomServices : BaseServices<MaterialBomEntity>, IMaterialBomServices
    {
        private readonly IBaseRepository<MaterialBomEntity> _dal;
        public MaterialBomServices(IBaseRepository<MaterialBomEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<bool> isExist(MaterialBomEntity entity, bool mod)
        {
            if (mod)
            {
                MaterialBomEntity old = await this.QueryById(entity.ID);
                if (old == null || old.MaterialCode.Equals(entity.MaterialCode))
                {
                    return false;
                }
            }
            //where = where.And(n => n.PROC_CODE == model.PROC_CODE && n.ROUTING_ID.Equals(model.ROUTING_ID));
            var whereExpression = Expressionable.Create<MaterialBomEntity>().And(a => a.Deleted == 0)
                .And(a => a.MaterialCode.Equals(entity.MaterialCode))
                .ToExpression();
            List<MaterialBomEntity> list = await FindList(whereExpression);

            return list.Any();
        }

        /// <summary>
        /// 修改状态
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<int> UpdateStuts(MaterialBomEntity request)
        {
            var data = await _dal.Db.Updateable<MaterialBomEntity>()
                .SetColumns(it => new MaterialBomEntity() { Status = request.Status })//类只能在表达示里面不能提取
                .Where(it => it.ID == request.ID)
                .ExecuteCommandAsync();

            return data;
        }

    }
}