
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;

namespace SEFA.DFM.Services
{
    public class MaterialProcessDataServices : BaseServices<MaterialProcessDataEntity>, IMaterialProcessDataServices
    {
        private readonly IBaseRepository<MaterialProcessDataEntity> _dal;
        public MaterialProcessDataServices(IBaseRepository<MaterialProcessDataEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<MaterialProcessDataEntity>> GetList(MaterialProcessDataRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<MaterialProcessDataEntity>()
                             .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }

        public async Task<PageModel<MaterialProcessDataEntity>> GetPageList(MaterialProcessDataRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<MaterialProcessDataEntity>()
                             .ToExpression();
            var data = await _dal.QueryPage(whereExpression,reqModel.pageIndex,reqModel.pageSize);
              
            return data;
        }

        public async Task<bool> SaveForm(MaterialProcessDataEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}