
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.DFM.Model.ViewModels;
using SqlSugar;

namespace SEFA.DFM.Services
{
    public class MaterialTypeRelServices : BaseServices<MaterialTypeRelEntity>, IMaterialTypeRelServices
    {
        private readonly IBaseRepository<MaterialTypeRelEntity> _dal;
        public MaterialTypeRelServices(IBaseRepository<MaterialTypeRelEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<RelMaterialTypeCalssModel>> GetMaterialTypeRels()
        {
            var relModel=await _dal.QueryTwoTables<MaterialTypeRelEntity,CategoryEntity,RelMaterialTypeCalssModel>(
                (r, m) => new object[] {
                    JoinType.Right, r.Categorycode == m.Code
                },

                (r, m) => new RelMaterialTypeCalssModel()
                {
                    Categorycode = r.Categorycode,
                    CategoryName = m.Name,
                    Type = r.Type

                },

                (r, m) => r.Deleted == 0 && m.Deleted == 0);
            return relModel;
        }
    }
}