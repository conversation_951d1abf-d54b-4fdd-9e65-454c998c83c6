
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;

namespace SEFA.DFM.Services
{
    public class OpcActionClassServices : BaseServices<OpcActionClassEntity>, IOpcActionClassServices
    {
        private readonly IBaseRepository<OpcActionClassEntity> _dal;
        public OpcActionClassServices(IBaseRepository<OpcActionClassEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }
    }
}