
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;

namespace SEFA.DFM.Services
{
    public class OpcFuncExecPropServices : BaseServices<OpcFuncExecPropEntity>, IOpcFuncExecPropServices
    {
        private readonly IBaseRepository<OpcFuncExecPropEntity> _dal;
        public OpcFuncExecPropServices(IBaseRepository<OpcFuncExecPropEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }
    }
}