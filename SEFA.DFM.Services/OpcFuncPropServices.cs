
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;

namespace SEFA.DFM.Services
{
    public class OpcFuncPropServices : BaseServices<OpcFuncPropEntity>, IOpcFuncPropServices
    {
        private readonly IBaseRepository<OpcFuncPropEntity> _dal;
        public OpcFuncPropServices(IBaseRepository<OpcFuncPropEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }
    }
}