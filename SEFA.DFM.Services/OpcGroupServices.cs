
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;

namespace SEFA.DFM.Services
{
    public class OpcGroupServices : BaseServices<OpcGroupEntity>, IOpcGroupServices
    {
        private readonly IBaseRepository<OpcGroupEntity> _dal;
        public OpcGroupServices(IBaseRepository<OpcGroupEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }
    }
}