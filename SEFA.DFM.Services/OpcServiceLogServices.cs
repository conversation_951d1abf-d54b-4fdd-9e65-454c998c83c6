
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;

namespace SEFA.DFM.Services
{
    public class OpcServiceLogServices : BaseServices<OpcServiceLogEntity>, IOpcServiceLogServices
    {
        private readonly IBaseRepository<OpcServiceLogEntity> _dal;
        public OpcServiceLogServices(IBaseRepository<OpcServiceLogEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }
    }
}