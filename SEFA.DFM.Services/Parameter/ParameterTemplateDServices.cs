
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;

namespace SEFA.DFM.Services
{
    public class ParameterTemplateDServices : BaseServices<ParameterTemplateDEntity>, IParameterTemplateDServices
    {
        private readonly IBaseRepository<ParameterTemplateDEntity> _dal;
        public ParameterTemplateDServices(IBaseRepository<ParameterTemplateDEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<ParameterTemplateDEntity>> GetList(ParameterTemplateDRequestModel reqModel)
        {
            List<ParameterTemplateDEntity> result = new List<ParameterTemplateDEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<ParameterTemplateDEntity>()
                .And(a => a.ParameterTemplateId == reqModel.ParameterTemplateId)
                             .ToExpression();
            var data = await _dal.Db.Queryable<ParameterTemplateDEntity>()
                .Where(whereExpression)
                .OrderBy(a=>a.SortOrder).ToListAsync();
            return data;
        }

        public async Task<PageModel<ParameterTemplateDEntity>> GetPageList(ParameterTemplateDRequestModel reqModel)
        {
            PageModel<ParameterTemplateDEntity> result = new PageModel<ParameterTemplateDEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<ParameterTemplateDEntity>()
                             .ToExpression();
            var data = await _dal.Db.Queryable<ParameterTemplateDEntity>()
                .Where(whereExpression)
                .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
            result.dataCount = dataCount;
            result.data = data;
            return result;
        }

        public async Task<bool> SaveForm(ParameterTemplateDEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}