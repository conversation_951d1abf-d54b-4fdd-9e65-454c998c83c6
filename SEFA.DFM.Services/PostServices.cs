
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;

namespace SEFA.DFM.Services
{
    public class PostServices : BaseServices<PostEntity>, IPostServices
    {
        private readonly IBaseRepository<PostEntity> _dal;
        public PostServices(IBaseRepository<PostEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }
    }
}