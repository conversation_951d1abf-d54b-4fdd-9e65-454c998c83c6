
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;

namespace SEFA.DFM.Services
{
    public class ProcAuxiliaryServices : BaseServices<ProcAuxiliaryEntity>, IProcAuxiliaryServices
    {
        private readonly IBaseRepository<ProcAuxiliaryEntity> _dal;
        public ProcAuxiliaryServices(IBaseRepository<ProcAuxiliaryEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }


        public async Task<bool> isExist(ProcAuxiliaryEntity request, bool ismod)
        {
            if (ismod)
            {
                ProcAuxiliaryEntity old = await this.QueryById(request.ID);
                if (old == null || old.MaterialCode.Equals(request.MaterialCode))
                {
                    return false;
                }
            }
            var whereExpression = Expressionable.Create<ProcAuxiliaryEntity>().And(a => a.Deleted == 0)
                .And(a => a.RoutingDetailId.Equals(request.RoutingDetailId) && a.MaterialCode.Equals(request.MaterialCode))
                .ToExpression();
            List<ProcAuxiliaryEntity> list = await FindList(whereExpression);

            return list.Any();
        }
    }
}