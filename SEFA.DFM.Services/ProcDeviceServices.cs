
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;

namespace SEFA.DFM.Services
{
    public class ProcDeviceServices : BaseServices<ProcDeviceEntity>, IProcDeviceServices
    {
        private readonly IBaseRepository<ProcDeviceEntity> _dal;
        public ProcDeviceServices(IBaseRepository<ProcDeviceEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }
    }
}