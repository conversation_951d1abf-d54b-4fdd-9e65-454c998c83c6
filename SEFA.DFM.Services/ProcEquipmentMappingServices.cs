
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using System.Threading.Tasks;
using SEFA.Base.Model;
using System.Linq.Expressions;
using System;
using SEFA.DFM.Model.ViewModels;
using System.Collections.Generic;
using SqlSugar;
using System.Linq;

namespace SEFA.DFM.Services
{
    public class ProcEquipmentMappingServices : BaseServices<ProcEquipmentMappingEntity>, IProcEquipmentMappingServices
    {
        private readonly IBaseRepository<ProcEquipmentMappingEntity> _dal;
        public ProcEquipmentMappingServices(IBaseRepository<ProcEquipmentMappingEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public List<ProcEquipmentMappingViewModels> QueryList()
        {

            var proceqtable = _dal.Db.Queryable<ProcEquipmentMappingEntity>()
            .LeftJoin<ProcessEntity>((o, proce) => o.ProcId == proce.ID)//多个条件用&&
            .LeftJoin<EquipmentEntity>((o, proce, eq) => o.EquipmentId == eq.ID)
            .Where(o => o.Deleted == 0)
            .Select((o, proce, eq) => new ProcEquipmentMappingViewModels { Id = o.ID, ProcName = proce.ProcName, EquipmentName = eq.EquipmentName })
            .ToList();

            return proceqtable;
        }

        public async Task<List<ProcEquipmentMappingViewModels>> QueryEquipmentList(string procid,string routingid)
        {
            List<String> equList = new List<string>();
            if (!string.IsNullOrEmpty(routingid))
            {
               var equips = await _dal.QueryTwoTables<ProductlineResEntity, EquipmentEntity, string>((r, m) => new object[] {
                    JoinType.Inner, r.ProdctlineId == m.ParentId
                },

               (r, m) => m.ID,

                (r, m) => r.Deleted == 0 && m.Deleted == 0 && r.RoutingId == routingid);
                equList = equips.ToList();
            }
            var proceqtable = await _dal.Db.Queryable<ProcEquipmentMappingEntity>()
            .LeftJoin<ProcessEntity>((o, proce) => o.ProcId == proce.ID)//多个条件用&&
            .LeftJoin<EquipmentEntity>((o, proce, eq) => o.EquipmentId == eq.ID)
            .Where(o => o.Deleted == 0 && equList.Contains(o.EquipmentId) && o.ProcId == procid)
            .Select((o, proce, eq) => new ProcEquipmentMappingViewModels
            {
                Id = o.ID,
                ProcName = proce.ProcName,
                EquipmentName = eq.EquipmentName,
                EquipmentCode = eq.EquipmentCode,
                Level = eq.Level,
                Remark = eq.Remark
            }).ToListAsync();
           

            return proceqtable;
        }


        public PageModel<ProcEquipmentMappingViewModels> QueryPageList(int pageIndex, int pageSize, string procname, string eqname)
        {
            int totalCount = 0;
            var proceqtable = _dal.Db.Queryable<ProcEquipmentMappingEntity>()
            .LeftJoin<ProcessEntity>((o, proce) => o.ProcId == proce.ID)//多个条件用&&
            .LeftJoin<EquipmentEntity>((o, proce, eq) => o.EquipmentId == eq.ID)
            .Where(o => o.Deleted == 0)
            .Select((o, proce, eq) => new ProcEquipmentMappingViewModels { Id = o.ID, ProcName = proce.ProcName, EquipmentName = eq.EquipmentName })
            .ToPageList(pageIndex, pageSize, ref totalCount);
            return new PageModel<ProcEquipmentMappingViewModels>
            {
                page = pageIndex,
                dataCount = proceqtable.Count,
                pageSize = pageSize,
                data = proceqtable
            };

        }

        public PageModel<ProcEquipmentMappingViewModels> QueryPageListByEqCode(int pageIndex, int pageSize, string procname, string eqcode)
        {
            int totalCount = 0;
            var proceqtable = _dal.Db.Queryable<ProcEquipmentMappingEntity>()
            .LeftJoin<ProcessEntity>((o, proce) => o.ProcId == proce.ID)//多个条件用&&
            .LeftJoin<EquipmentEntity>((o, proce, eq) => o.EquipmentId == eq.ID)
            .Where((o, proce, eq) => o.Deleted == 0 && eq.EquipmentCode.Equals(eqcode))
            .Select((o, proce, eq) => new ProcEquipmentMappingViewModels { Id = o.ID, ProcName = proce.ProcName, EquipmentName = eq.EquipmentName, EquipmentCode = eq.EquipmentCode })
            .ToPageList(pageIndex, pageSize, ref totalCount);
            return new PageModel<ProcEquipmentMappingViewModels>
            {
                page = pageIndex,
                dataCount = proceqtable.Count,
                pageSize = pageSize,
                data = proceqtable
            };

        }

        public async Task<bool> isExist(List<ProcEquipmentMappingEntity> request)
        {
            var eqlist = request.Select(a => a.EquipmentId).ToArray();
            var whereExpression = Expressionable.Create<ProcEquipmentMappingEntity>().And(a => a.Deleted == 0)
                .And(a => eqlist.Contains(a.EquipmentId))
                .ToExpression();
            List<ProcEquipmentMappingEntity> list = await FindList(whereExpression);

            return list.Any();
        }
    }
}