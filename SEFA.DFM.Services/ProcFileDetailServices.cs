
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;

namespace SEFA.DFM.Services
{
    public class ProcFileDetailServices : BaseServices<ProcFileDetailEntity>, IProcFileDetailServices
    {
        private readonly IBaseRepository<ProcFileDetailEntity> _dal;
        public ProcFileDetailServices(IBaseRepository<ProcFileDetailEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }
    }
}