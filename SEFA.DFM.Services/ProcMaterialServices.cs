
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;

namespace SEFA.DFM.Services
{
    public class ProcMaterialServices : BaseServices<ProcMaterialEntity>, IProcMaterialServices
    {
        private readonly IBaseRepository<ProcMaterialEntity> _dal;
        public ProcMaterialServices(IBaseRepository<ProcMaterialEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }
    }
}