
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;

namespace SEFA.DFM.Services
{
    public class ProcParamServices : BaseServices<ProcParamEntity>, IProcParamServices
    {
        private readonly IBaseRepository<ProcParamEntity> _dal;
        public ProcParamServices(IBaseRepository<ProcParamEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }
    }
}