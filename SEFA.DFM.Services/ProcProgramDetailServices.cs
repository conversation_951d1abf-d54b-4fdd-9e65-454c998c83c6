
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using System.Threading.Tasks;
using SqlSugar;
using System.Collections.Generic;
using System.Linq;

namespace SEFA.DFM.Services
{
    public class ProcProgramDetailServices : BaseServices<ProcProgramDetailEntity>, IProcProgramDetailServices
    {
        private readonly IBaseRepository<ProcProgramDetailEntity> _dal;
        public ProcProgramDetailServices(IBaseRepository<ProcProgramDetailEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<bool> isExist(ProcProgramDetailEntity request, bool ismod)
        {
            if (ismod)
            {
                ProcProgramDetailEntity old = await this.QueryById(request.ID);
                if (old == null || old.ProgramId.Equals(request.ProgramId))
                {
                    return false;
                }
            }
            var whereExpression = Expressionable.Create<ProcProgramDetailEntity>().And(a => a.Deleted == 0)
                .And(a => a.RoutingDetailId.Equals(request.RoutingDetailId) && a.ProgramId.Equals(request.ProgramId))
                .ToExpression();
            List<ProcProgramDetailEntity> list = await FindList(whereExpression);

            return list.Any();
        }
    }
}