
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using System.Threading.Tasks;
using SqlSugar;
using System.Collections.Generic;
using System.Linq;

namespace SEFA.DFM.Services
{
    public class ProcProgramServices : BaseServices<ProcProgramEntity>, IProcProgramServices
    {
        private readonly IBaseRepository<ProcProgramEntity> _dal;
        public ProcProgramServices(IBaseRepository<ProcProgramEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<bool> isExist(ProcProgramEntity request, bool ismod)
        {
            if (ismod)
            {
                ProcProgramEntity old = await this.QueryById(request.ID);
                if (old == null || (old.Factory.Equals(request.Factory)&& old.Name.Equals(request.Name) && old.Version.Equals(request.Version)))
                {
                    return false;
                }
            }
            var whereExpression = Expressionable.Create<ProcProgramEntity>().And(a => a.Deleted == 0)
                .And(a => a.Factory.Equals(request.Factory) && a.Name.Equals(request.Name) && a.Version.Equals(request.Version))
                .ToExpression();
            List<ProcProgramEntity> list = await FindList(whereExpression);

            return list.Any();
        }
   
    }
}