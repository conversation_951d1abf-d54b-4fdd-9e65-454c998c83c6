
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;

namespace SEFA.DFM.Services
{
    public class ProcResSettingServices : BaseServices<ProcResSettingEntity>, IProcResSettingServices
    {
        private readonly IBaseRepository<ProcResSettingEntity> _dal;
        public ProcResSettingServices(IBaseRepository<ProcResSettingEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }
    }
}