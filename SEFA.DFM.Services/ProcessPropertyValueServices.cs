
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;

namespace SEFA.DFM.Services
{
    public class ProcessPropertyValueServices : BaseServices<ProcessPropertyValueEntity>, IProcessPropertyValueServices
    {
        private readonly IBaseRepository<ProcessPropertyValueEntity> _dal;
        public ProcessPropertyValueServices(IBaseRepository<ProcessPropertyValueEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }
    }
}