
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using System.Threading.Tasks;
using SqlSugar;
using System.Collections.Generic;
using System.Linq;

namespace SEFA.DFM.Services
{
    public class ProcessServices : BaseServices<ProcessEntity>, IProcessServices
    {
        private readonly IBaseRepository<ProcessEntity> _dal;
        public ProcessServices(IBaseRepository<ProcessEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<bool> isExist(ProcessEntity entity, bool mod)
        {
            if (mod)
            {
                ProcessEntity old = await this.QueryById(entity.ID);
                if (old == null || old.ProcCode.Equals(entity.ProcCode))
                {
                    return false;
                }
            }
            //where = where.And(n => n.PROC_CODE == model.PROC_CODE && n.ROUTING_ID.Equals(model.ROUTING_ID));
            var whereExpression = Expressionable.Create<ProcessEntity>().And(a => a.Deleted == 0)
                .And(a => a.ProcCode.Equals(entity.ProcCode) || a.ProcName.Equals(entity.ProcName))
                .ToExpression();
            List<ProcessEntity> list = await FindList(whereExpression);

            return list.Any();
        }
    }
}