
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;

namespace SEFA.DFM.Services
{
    public class PropertyServices : BaseServices<PropertyEntity>, IPropertyServices
    {
        private readonly IBaseRepository<PropertyEntity> _dal;
        public PropertyServices(IBaseRepository<PropertyEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }
    }
}