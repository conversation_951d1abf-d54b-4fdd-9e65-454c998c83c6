
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;

namespace SEFA.DFM.Services
{
    public class RackingBinMaterialServices : BaseServices<RackingBinMaterialEntity>, IRackingBinMaterialServices
    {
        private readonly IBaseRepository<RackingBinMaterialEntity> _dal;
        public RackingBinMaterialServices(IBaseRepository<RackingBinMaterialEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }
    }
}