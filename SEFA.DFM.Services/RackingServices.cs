
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using System.Threading.Tasks;
using SqlSugar;
using System.Collections.Generic;
using System.Linq;
using SEFA.DFM.IRepository;

namespace SEFA.DFM.Services
{
    public class RackingServices : BaseServices<RackingEntity>, IRackingServices
    {
        private readonly IRackingRepository _dal;

        public RackingServices(IRackingRepository dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public Task<List<RackingValueModel>> GetTree(string itemCode)
        {
            return _dal.GetTree(itemCode);
        }
        public async Task<bool> isExist(RackingEntity entity, bool mod)
        {
            if (mod)
            {
                RackingEntity old = await this.QueryById(entity.ID);
                if (old == null || old.RackingCode.Equals(entity.RackingCode))
                {
                    return false;
                }
            }
            //where = where.And(n => n.PROC_CODE == model.PROC_CODE && n.ROUTING_ID.Equals(model.ROUTING_ID));
            var whereExpression = Expressionable.Create<RackingEntity>().And(a => a.Deleted == 0)
                .And(a => a.RackingCode.Equals(entity.RackingCode))
                .ToExpression();
            List<RackingEntity> list = await FindList(whereExpression);

            return list.Any();
            //return true;
        }


       
    }
}