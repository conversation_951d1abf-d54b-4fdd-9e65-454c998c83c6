
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;

namespace SEFA.DFM.Services
{
    public class ReasontreeServices : BaseServices<ReasontreeEntity>, IReasontreeServices
    {
        private readonly IBaseRepository<ReasontreeEntity> _dal;
        public ReasontreeServices(IBaseRepository<ReasontreeEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }
    }
}