
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using System;
using SqlSugar;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base.Model;
using SEFA.DFM.Model.ViewModels;
using System.Linq.Expressions;
using System.Xml.Linq;

namespace SEFA.DFM.Services
{
    public class MaterialVersionServices : BaseServices<MaterialVersionEntity>, IMaterialVersionServices
    {
        private readonly IBaseRepository<MaterialVersionEntity> _dal;
        public MaterialVersionServices(IBaseRepository<MaterialVersionEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<MaterialVersionEntity>> GetMaterialVersionList (string name, int count = 0) {
            return await GetList(name, count);

        }

        public async Task<List<MaterialVersionEntity>> GetList (string name,int count = 0) {
            var data = new List<MaterialVersionEntity>();
            if (count == 0)
            {
                data = await _dal.Db.Queryable<MaterialEntity, MaterialVersionEntity>((m, mv)
               => new object[]
               {
                    JoinType.Inner, m.ID == mv.MaterialId && m.Deleted == 0 && mv.Deleted == 0
               })
               .Select((m, mv) => new MaterialVersionEntity
               {
                   ID = mv.ID,
                   MaterialId = mv.MaterialId,
                   CreateDate = mv.CreateDate,
                   CreateUserId = mv.CreateUserId,
                   ModifyDate = mv.ModifyDate,
                   ModifyUserId = mv.ModifyUserId,
                   Plantcode = mv.Plantcode,
                   Remark = mv.Remark,
                   MaterialVersionNumber = mv.MaterialVersionNumber + "-" + m.Code + "-" + m.NAME,
               })
               .MergeTable()
               .WhereIF(!string.IsNullOrEmpty(name), a => a.MaterialVersionNumber.Contains(name))
                .ToListAsync();
            }
            else
            {
                data = await _dal.Db.Queryable<MaterialEntity, MaterialVersionEntity>((m, mv)
              => new object[]
              {
                    JoinType.Inner, m.ID == mv.MaterialId && m.Deleted == 0 && mv.Deleted == 0
              })
              .Select((m, mv) => new MaterialVersionEntity
              {
                  ID = mv.ID,
                  MaterialId = mv.MaterialId,
                  CreateDate = mv.CreateDate,
                  CreateUserId = mv.CreateUserId,
                  ModifyDate = mv.ModifyDate,
                  ModifyUserId = mv.ModifyUserId,
                  Plantcode = mv.Plantcode,
                  Remark = mv.Remark,
                  MaterialVersionNumber = mv.MaterialVersionNumber + "-" + m.Code + "-" + m.NAME,
              })
              .WhereIF(!string.IsNullOrEmpty(name), a => a.MaterialVersionNumber.Contains(name))
               .ToPageListAsync(1, count);
            }
            return data;
        }

        public async Task<PageModel<MaterialVersionModel>> GetPageList ([FromBody] MaterialVersionRequestModel materialRequest) {

            PageModel<MaterialVersionModel> result = new PageModel<MaterialVersionModel>();
            RefAsync<int> dataCount = 0;
            var data =  await  _dal.Db.Queryable<MaterialEntity, MaterialVersionEntity>((m, mv)
               => new object[]
               {
                    JoinType.Inner, m.ID == mv.MaterialId && m.Deleted == 0 && mv.Deleted == 0
               })
               .Select((m, mv) => new MaterialVersionModel
               {
                   ID = mv.ID,
                   MaterialId = mv.MaterialId,
                   CreateDate = mv.CreateDate,
                   CreateUserId = mv.CreateUserId,
                   ModifyDate = mv.ModifyDate,
                   ModifyUserId = mv.ModifyUserId,
                   MaterialCode = m.Code,
                   MaterialName = m.NAME,
                   FormulaCode = m.Description,
                   Remark = mv.Remark,
                   MaterialVersionNumber = mv.MaterialVersionNumber 
               })
                .MergeTable()
                .WhereIF(!string.IsNullOrEmpty(materialRequest.key), a =>
                      (a.MaterialCode != null && a.MaterialCode.StartsWith(materialRequest.key)) ||
                      (a.MaterialName != null && a.MaterialName.StartsWith(materialRequest.key)) ||
                      (a.FormulaCode != null && a.FormulaCode.StartsWith(materialRequest.key))
                )
                .ToPageListAsync(materialRequest.pageIndex, materialRequest.pageSize, dataCount);

            result.dataCount = dataCount;
            result.data = data;
            return result;

        }

    }
}