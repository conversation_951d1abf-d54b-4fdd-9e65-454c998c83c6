
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using System.Threading.Tasks;
using SEFA.Base.Common.WebApiClients.HttpApis;
using OfficeOpenXml.FormulaParsing.Excel.Functions.DateTime;
using System.Linq;
using SEFA.Base.IRepository.UnitOfWork;
using System;
using AutoMapper;
using SEFA.DFM.Model.ViewModels;
using System.Collections.Generic;

namespace SEFA.DFM.Services
{
    public class ParameterDefinitionServices : BaseServices<ParameterDefinitionEntity>, IParameterDefinitionServices
    {
        private readonly IBaseRepository<ParameterDefinitionEntity> _dal;
        private readonly IBaseRepository<ParameterConfigEntity> _parameterConfigService;
        private readonly IBaseRepository<ParameterGroupEntity> _parameterGroupService;
        private readonly IBaseRepository<RecipeExeutionEntity> _recipeExeutionService;
        private readonly IBaseRepository<RecipeContextVersionEntity> _recipeContextVersionService;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        public ParameterDefinitionServices (IBaseRepository<ParameterDefinitionEntity> dal,
            IBaseRepository<ParameterConfigEntity> parameterConfigService,
            IBaseRepository<ParameterGroupEntity> parameterGroupService,
            IUnitOfWork unitOfWork,
            IMapper mapper,
            IBaseRepository<RecipeExeutionEntity> recipeExeutionService,
            IBaseRepository<RecipeContextVersionEntity> recipeContextVersionService) {
            this._dal = dal;
            base.BaseDal = dal;
            this._parameterConfigService = parameterConfigService;
            this._parameterGroupService = parameterGroupService;
            this._unitOfWork = unitOfWork;
            this._mapper = mapper;
            this._recipeExeutionService = recipeExeutionService;
            this._recipeContextVersionService = recipeContextVersionService;
        }

        public async Task<MessageModel<string>> AddParameter (RecipeParameterModel request) {
            var returnData = new MessageModel<string>();
            returnData.success = false;
            if (string.IsNullOrEmpty(request.ParameterName))
            {
                returnData.msg = $"参数名称不可为空";
                return returnData;
            }
            if (string.IsNullOrEmpty(request.ParameterType))
            {
                returnData.msg = $"需指定参数类型";
                return returnData;
            }

            if (string.IsNullOrEmpty(request.ParameterGroupId))
            {
                returnData.msg = $"GroupId不可为空";
                return returnData;
            }


            var groupEntity = await _parameterGroupService.FindEntity(request.ParameterGroupId);

            if (groupEntity == null)
            {
                returnData.msg = $"GroupId不存在";
                return returnData;
            }

            if (request.IsScadaTag == "1" && string.IsNullOrEmpty(request.TagType))
            {
                returnData.msg = $"数采点位类型不可为空";
                return returnData;
            }

            var recipeList = await _dal.FindList(a => a.ParameterGroupId == request.ParameterGroupId
                                    && a.ParameterName == request.ParameterName);
            recipeList = recipeList.Except(recipeList.Where(a => a.ID == request.ID)).ToList();
            if (recipeList.Any())
            {
                returnData.msg = $"已存在参数[{request.ParameterName}]";
                return returnData;
            }

            var entity = _mapper.Map<ParameterDefinitionEntity>(request);

            List<ParameterConfigEntity> parameterConfigEntities = new List<ParameterConfigEntity>();

            entity.RecipeId = groupEntity.RecipeId;
            entity.RecipeSectionId = groupEntity.RecipeSectionId;
            entity.RecipeSectionVersionId = groupEntity.RecipeSectionVersionId;
            entity.CreateCustomGuid(request.UserId);

            ParameterConfigEntity parameterConfigEntity = new ParameterConfigEntity();
            parameterConfigEntity.ParameterDefinitionId = entity.ID;
            parameterConfigEntity.RecipeId = entity.RecipeId;
            parameterConfigEntity.RecipeSectionId = entity.RecipeSectionId;
            parameterConfigEntity.RecipeSectionVersionId = entity.RecipeSectionVersionId;
            parameterConfigEntity.RecipeContextId = request.RecipeContextId;
            parameterConfigEntity.RecipeContextVersionId = request.RecipeContextVersionId;
            parameterConfigEntity.Status = RecipeDataType.ActiveStatus.Active;
            parameterConfigEntity.CreateCustomGuid(request.UserId);
            parameterConfigEntities.Add(parameterConfigEntity);

            var contextVersionList = await _recipeContextVersionService.FindList(a => a.RecipeSectionVersionId == entity.RecipeSectionVersionId && a.ID != request.RecipeContextVersionId);

            foreach (var item in contextVersionList)
            {

                ParameterConfigEntity otherParameterConfigEntity = new ParameterConfigEntity();
                otherParameterConfigEntity.ParameterDefinitionId = entity.ID;
                otherParameterConfigEntity.RecipeId = entity.RecipeId;
                otherParameterConfigEntity.RecipeSectionId = entity.RecipeSectionId;
                otherParameterConfigEntity.RecipeSectionVersionId = entity.RecipeSectionVersionId;
                otherParameterConfigEntity.Status = "0";
                otherParameterConfigEntity.RecipeContextId = item.RecipeContextId;
                otherParameterConfigEntity.RecipeContextVersionId = item.ID;
                otherParameterConfigEntity.CreateCustomGuid(request.UserId);

                parameterConfigEntities.Add(otherParameterConfigEntity);
            }

            _unitOfWork.BeginTran();
            try
            {
                await _dal.Add(entity);
                await _parameterConfigService.Add(parameterConfigEntities);
                returnData.success = true;
                _unitOfWork.CommitTran();
            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                returnData.msg = ex.Message;
            }

            return returnData;

        }

        public async Task<MessageModel<string>> EditParameter (RecipeParameterModel request) {
            var returnData = new MessageModel<string>();
            returnData.success = false;
            if (string.IsNullOrEmpty(request.ParameterName))
            {
                returnData.msg = $"参数名称不可为空";
                return returnData;
            }
            if (string.IsNullOrEmpty(request.ParameterType))
            {
                returnData.msg = $"需指定参数类型";
                return returnData;
            }

            if (string.IsNullOrEmpty(request.ParameterGroupId))
            {
                returnData.msg = $"GroupId不可为空";
                return returnData;
            }

            if (request.IsScadaTag == "1" && string.IsNullOrEmpty(request.TagType))
            {
                returnData.msg = $"数采点位类型不可为空";
                return returnData;
            }

            var groupEntity = await _parameterGroupService.FindEntity(request.ParameterGroupId);

            if (groupEntity == null)
            {
                returnData.msg = $"GroupId不存在";
                return returnData;
            }

            var parameterDefinitionEntity = await _dal.FindEntity(request.ID);

            if (parameterDefinitionEntity == null)
            {
                returnData.msg = $"参数不存在";
                return returnData;
            }


            var recipeList = await _dal.FindList(a => a.ParameterGroupId == request.ParameterGroupId
                                    && a.ParameterName == request.ParameterName);
            recipeList = recipeList.Except(recipeList.Where(a => a.ID == request.ID)).ToList();
            if (recipeList.Any())
            {
                returnData.msg = $"已存在参数[{request.ParameterName}]";
                return returnData;
            }

            parameterDefinitionEntity.ParameterName = request.ParameterName;
            parameterDefinitionEntity.ParameterType = request.ParameterType;
            if (parameterDefinitionEntity.ParameterType != "1")
            {
                parameterDefinitionEntity.DataType = "";
            }
            parameterDefinitionEntity.SortOrder = request.SortOrder;
            parameterDefinitionEntity.ShortName = request.ShortName;
            parameterDefinitionEntity.Status = request.Status;
            parameterDefinitionEntity.UomId = request.UomId;
            parameterDefinitionEntity.ParameterType = request.ParameterType;
            parameterDefinitionEntity.DataType = request.DataType;
            parameterDefinitionEntity.Precision = request.Precision;
            parameterDefinitionEntity.Tag = request.Tag;
            parameterDefinitionEntity.IsAutoCollection = request.IsAutoCollection;
            parameterDefinitionEntity.TagType = request.TagType;
            parameterDefinitionEntity.IsScadaTag = request.IsScadaTag;

            parameterDefinitionEntity.Modify(parameterDefinitionEntity.ID, request.CreateUserId);


            _unitOfWork.BeginTran();
            try
            {
                await _dal.Update(parameterDefinitionEntity);

                _unitOfWork.CommitTran();
            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
            }


            returnData.success = true;

            return returnData;

        }

        public async Task<MessageModel<string>> DeleteParameter (RecipeParameterModel request) {
            var returnData = new MessageModel<string>();
            returnData.success = false;
            if (string.IsNullOrEmpty(request.ID))
            {
                returnData.msg = $"ID不可为空";
                return returnData;
            }
            var entity = await _dal.FindEntity(request.ID);
            if (entity == null)
            {
                returnData.msg = $"ID不存在";
                return returnData;
            }

            var useList = await _recipeExeutionService.FindList(a => a.RecipeSectionVersionId == request.RecipeSectionVersionId);

            if (useList == null)
            {
                returnData.msg = $"当前版本已被使用,无法删除参数";
                return returnData;
            }

            var list = await _parameterConfigService.FindList(a => a.ParameterDefinitionId == request.ID);

            _unitOfWork.BeginTran();
            try
            {
                await _parameterConfigService.DeleteByIds(list.Select(a => a.ID).ToArray());
                await _dal.Delete(entity);

                _unitOfWork.CommitTran();
            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
            }


            returnData.success = true;

            return returnData;

        }

    
    }


}