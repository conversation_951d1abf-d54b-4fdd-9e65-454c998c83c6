
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base.Model;
using SqlSugar;
using System.Collections.Generic;
using System.Threading.Tasks;
using SEFA.DFM.Model.ViewModels;
using System.Linq;

namespace SEFA.DFM.Services
{
    public class ParameterGroupEquipmentServices : BaseServices<ParameterGroupEquipmentEntity>, IParameterGroupEquipmentServices
    {
        private readonly IBaseRepository<ParameterGroupEquipmentEntity> _dal;
        public ParameterGroupEquipmentServices(IBaseRepository<ParameterGroupEquipmentEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<ParameterGroupEquipmentModel>> GetGroupEquipmentList (string ParameterGroupId) {
            var list = new List<ParameterGroupEquipmentModel>();

            list = await _dal.Db.Queryable<ParameterGroupEquipmentEntity, EquipmentEntity>((pge,e)
                => new object[]
                {
                    JoinType.Inner,pge.EquipmentId == e.ID
                })
            .Where((pge, e) => pge.ParameterGroupId == ParameterGroupId)
            .Select((pge, e) => new ParameterGroupEquipmentModel
            {
                ID = pge.ID,
                AutoTrigger = pge.AutoTrigger,
                FrequencyTagId = pge.FrequencyTagId,
                OpcFunctionInstanceId = pge.OpcFunctionInstanceId,
                EquipmentId = pge.EquipmentId,
                EquipmentName = e.EquipmentName,
                CreateDate = pge.CreateDate, 
                CreateUserId = pge.CreateUserId,
                ModifyDate = pge.ModifyDate,
                ModifyUserId = pge.ModifyUserId,

            })
            .MergeTable()
            .ToListAsync();

            return list.OrderBy(a => a.CreateDate).ToList();
        }
    }
}