
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;

namespace SEFA.DFM.Services
{
    public class ParameterGroupServices : BaseServices<ParameterGroupEntity>, IParameterGroupServices
    {
        private readonly IBaseRepository<ParameterGroupEntity> _dal;
        public ParameterGroupServices(IBaseRepository<ParameterGroupEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }
    }
}