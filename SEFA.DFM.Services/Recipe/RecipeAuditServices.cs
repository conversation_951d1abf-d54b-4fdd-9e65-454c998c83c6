
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;

namespace SEFA.DFM.Services
{
    public class RecipeAuditServices : BaseServices<RecipeAuditEntity>, IRecipeAuditServices
    {
        private readonly IBaseRepository<RecipeAuditEntity> _dal;
        public RecipeAuditServices(IBaseRepository<RecipeAuditEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }
    }
}