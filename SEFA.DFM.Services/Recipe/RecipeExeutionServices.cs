
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;

namespace SEFA.DFM.Services
{
    public class RecipeExeutionServices : BaseServices<RecipeExeutionEntity>, IRecipeExeutionServices
    {
        private readonly IBaseRepository<RecipeExeutionEntity> _dal;
        public RecipeExeutionServices(IBaseRepository<RecipeExeutionEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }
    }
}