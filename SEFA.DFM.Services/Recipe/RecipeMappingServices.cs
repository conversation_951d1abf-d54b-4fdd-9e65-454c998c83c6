
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.DFM.Model.ViewModels;
using SqlSugar;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace SEFA.DFM.Services
{
    public class RecipeMappingServices : BaseServices<RecipeMappingEntity>, IRecipeMappingServices
    {
        private readonly IBaseRepository<RecipeMappingEntity> _dal;
        public RecipeMappingServices(IBaseRepository<RecipeMappingEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<RecipeMappingModel>> GetList(RecipeMappingModel model) {
            var list = new List<RecipeMappingModel>();

            list = await _dal.Db.Queryable<RecipeMappingEntity, MaterialGroupEntity, MaterialEntity, MaterialVersionEntity>((rm, g, m, mv)
                => new object[]
                {
                    JoinType.Left,rm.MaterialGroupId == g.ID,
                    JoinType.Left,rm.MaterialId == m.ID,
                    JoinType.Left,rm.MaterialVersionId == mv.ID
                })
            .Select((rm, g, m, mv) => new RecipeMappingModel
            {
                ID = rm.ID,
                RecipeId = rm.RecipeId,
                MaterialGroupId = rm.MaterialGroupId,
                MaterialId = rm.MaterialId,
                MaterialVersionId = rm.MaterialVersionId,
                MaterialGroupName = g.MaterialGroupName,
                MaterialCode = m.Code,
                MaterialName = m.NAME,
                MaterialVersionNumber = mv.MaterialVersionNumber,
                CreateDate = rm.CreateDate,
                CreateUserId = rm.CreateUserId
            })
            .MergeTable()
            .WhereIF(!string.IsNullOrEmpty(model.RecipeId),a => a.RecipeId == model.RecipeId)
            .WhereIF(!string.IsNullOrEmpty(model.Key), 
                a => a.MaterialGroupName.Contains(model.Key) || a.MaterialCode.Contains(model.Key) || a.MaterialName.Contains(model.Key) || a.MaterialVersionNumber.Contains(model.Key))
           .ToListAsync();

            return list.OrderBy(a => a.CreateDate).ToList();
        }
    }
}