
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;

namespace SEFA.DFM.Services
{
    public class RecipeSectionServices : BaseServices<RecipeSectionEntity>, IRecipeSectionServices
    {
        private readonly IBaseRepository<RecipeSectionEntity> _dal;
        public RecipeSectionServices(IBaseRepository<RecipeSectionEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }
    }
}