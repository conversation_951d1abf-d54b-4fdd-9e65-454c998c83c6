
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using OfficeOpenXml.FormulaParsing.Excel.Functions.DateTime;

namespace SEFA.DFM.Services
{
    public class RecipeServices : BaseServices<RecipeEntity>, IRecipeServices
    {
        private readonly IBaseRepository<RecipeEntity> _dal;
        public RecipeServices(IBaseRepository<RecipeEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }
    }
}