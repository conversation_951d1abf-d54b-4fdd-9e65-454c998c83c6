
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;

namespace SEFA.DFM.Services
{
    public class ReplaceMaterialServices : BaseServices<ReplaceMaterialEntity>, IReplaceMaterialServices
    {
        private readonly IBaseRepository<ReplaceMaterialEntity> _dal;
        public ReplaceMaterialServices(IBaseRepository<ReplaceMaterialEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }
    }
}