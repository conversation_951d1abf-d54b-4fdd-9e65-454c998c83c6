
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using System.Threading.Tasks;
using SqlSugar;
using System.Collections.Generic;
using System.Linq;

namespace SEFA.DFM.Services
{
    public class RoutingDetailServices : BaseServices<RoutingDetailEntity>, IRoutingDetailServices
    {
        private readonly IBaseRepository<RoutingDetailEntity> _dal;
        public RoutingDetailServices(IBaseRepository<RoutingDetailEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<bool> isExist(RoutingDetailEntity entity, bool mod)
        {
            if (mod) 
            {
                RoutingDetailEntity old = await this.QueryById(entity.ID);
                if (old == null||old.ProcCode.Equals(entity.ProcCode))
                {
                    return false;
                }
            }
            //where = where.And(n => n.PROC_CODE == model.PROC_CODE && n.ROUTING_ID.Equals(model.ROUTING_ID));
            var whereExpression = Expressionable.Create<RoutingDetailEntity>().And(a => a.Deleted == 1)
                .And(a => a.ProcCode.Equals(entity.ProcCode) && a.RoutingId.Equals(entity.RoutingId))
                .ToExpression();
            List<RoutingDetailEntity> list =  await FindList(whereExpression);
            
            return  list.Any();
        }
    }
}