
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;

namespace SEFA.DFM.Services
{
    public class SapEquipmentServices : BaseServices<SapEquipmentEntity>, ISapEquipmentServices
    {
        private readonly IBaseRepository<SapEquipmentEntity> _dal;
        public SapEquipmentServices(IBaseRepository<SapEquipmentEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }
    }
}