
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using System.Collections.Generic;
using SqlSugar;
using System.Threading.Tasks;
using System.Linq;
using SEFA.Base.Common.Common;
using SEFA.Base.Model;
using SEFA.DFM.Model.ViewModels;

namespace SEFA.DFM.Services
{
    public class SapSegmentServices : BaseServices<SapSegmentEntity>, ISapSegmentServices
    {
        private readonly IBaseRepository<SapSegmentEntity> _dal;
        private readonly IBaseRepository<SapSegmentEquipmentEntity> _sapSegmentEquipmentServices;
        private readonly IBaseRepository<SapSegmentMaterialEntity> _sapSegmentMaterialServices;
        public SapSegmentServices(IBaseRepository<SapSegmentEntity> dal, IBaseRepository<SapSegmentEquipmentEntity> sapSegmentEquipmentServices,
            IBaseRepository<SapSegmentMaterialEntity> sapSegmentMaterialServices)
        {
            this._dal = dal;
            base.BaseDal = dal;
            _sapSegmentEquipmentServices = sapSegmentEquipmentServices;
            _sapSegmentMaterialServices = sapSegmentMaterialServices;
        }

        public async Task<List<SapSegmentEntity>> GetSegmentList(SapSegmentRequestModel reqModel) {

            return await _dal.Db.Queryable<SapSegmentEntity, SapSegmentEntity,SapEquipmentEntity>((l1, l2,se)
               => new object[]
               {
                    JoinType.Inner, l1.ID == l2.ParentId && l1.Level == 1 && l2.Level == 2,
                    JoinType.Left, l1.SapEquipmentId == se.ID
               })
               .WhereIF(!string.IsNullOrEmpty(reqModel.SapEquipmentId), (l1, l2, se) => l2.SapEquipmentId == reqModel.SapEquipmentId)
               .Select((l1, l2,se) => new SapSegmentEntity
               {
                   ID = l2.ID,
                   Level = l2.Level,
                   SegmentCode = l2.SegmentCode,
                   SegmentName = se.Name + "-" + l1.SegmentName + "-" + l2.SegmentName
               })
               .MergeTable()
               .WhereIF(!string.IsNullOrEmpty(reqModel.key), a => a.SegmentCode != null && a.SegmentCode.StartsWith(reqModel.key))
               .WhereIF(!string.IsNullOrEmpty(reqModel.ParentId), a => a.ParentId == reqModel.ParentId)
               .WhereIF(reqModel.Level > 0, a => a.Level == reqModel.Level)
               .ToListAsync();

        }

        public async Task<MessageModel<string>> Delete (string[] ids) {

            MessageModel<string> result = new MessageModel<string>();
            result.success = false;
            var list = await _dal.FindList(a => ids.Contains(a.SapEquipmentId));
            if (list.Count > 0)
            {
                result.msg = "数据存在子节点，不可删除";
                return result;
            }

            var list1 = await _sapSegmentEquipmentServices.FindList(a => ids.Contains(a.SapSegmentId));
            if (list.Count > 0)
            {
                result.msg = "数据已绑定产品物料数据，不可删除";
                return result;
            }
            var list2 = await _sapSegmentEquipmentServices.FindList(a => ids.Contains(a.SapSegmentId));
            if (list.Count > 0)
            {
                result.msg = "数据已绑定设备数据，不可删除";
                return result;
            }
            result.success = await _dal.DeleteByIds(ids);
            return result;
        }
    }
}