
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;

namespace SEFA.DFM.Services
{
    public class SeriesCategoryServices : BaseServices<SeriesCategoryEntity>, ISeriesCategoryServices
    {
        private readonly IBaseRepository<SeriesCategoryEntity> _dal;
        public SeriesCategoryServices(IBaseRepository<SeriesCategoryEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }
    }
}