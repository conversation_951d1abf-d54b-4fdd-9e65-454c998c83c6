
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;

namespace SEFA.DFM.Services
{
    public class SeriesServices : BaseServices<SeriesEntity>, ISeriesServices
    {
        private readonly IBaseRepository<SeriesEntity> _dal;
        public SeriesServices(IBaseRepository<SeriesEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }
    }
}