
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;

namespace SEFA.DFM.Services
{
    public class ShifttimeServices : BaseServices<ShifttimeEntity>, IShifttimeServices
    {
        private readonly IBaseRepository<ShifttimeEntity> _dal;
        public ShifttimeServices(IBaseRepository<ShifttimeEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }
    }
}