using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;

namespace SEFA.DFM.Services
{
    public class SopPermissionServices : BaseServices<SopPermissionEntity>, ISopPermissionServices
    {
        private readonly IBaseRepository<SopPermissionEntity> _dal;

        public SopPermissionServices(IBaseRepository<SopPermissionEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<SopPermissionEntity>> GetList(SopPermissionRequestModel reqModel)
        {
            // 获取所有角色列表
            var allRoles = await _dal.Db.Queryable<RoleEntity>().ToListAsync();

            // 查询已存在的权限记录
            var existingPermissions = await _dal.Db.Queryable<RoleEntity, SopPermissionEntity>((r, p) =>
                    new JoinQueryInfos(JoinType.Left, r.ID == p.GrantId))
                .Where((r, p) => p.TargetId == reqModel.TargetId)
                .Select((r, p) => new SopPermissionEntity
                {
                    ID = p.ID,
                    GrantId = r.ID,
                    TargetId = p.TargetId,
                    TargetType = p.TargetType,
                    GrantType = p.GrantType,
                    PermLevel = p.PermLevel,
                    RoleName = r.Name,
                    Deleted = p.Deleted
                }).ToListAsync();

            // 构建完整结果集
            var data = allRoles.Select(role =>
            {
                var existing = existingPermissions.FirstOrDefault(p => p.GrantId == role.ID);

                return existing ?? new SopPermissionEntity
                {
                    ID = null,
                    GrantId = role.ID,
                    TargetId = reqModel.TargetId,
                    TargetType = 1,
                    GrantType = 1,
                    PermLevel = "",
                    RoleName = role.Name,
                    Deleted = 0
                };
            }).ToList();
            return data;
        }

        public async Task<PageModel<SopPermissionEntity>> GetPageList(SopPermissionRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<SopPermissionEntity>()
                .ToExpression();
            var data = await _dal.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);

            return data;
        }

        public async Task<bool> SaveForm(SopPermissionEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}