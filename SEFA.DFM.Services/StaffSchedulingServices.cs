using AutoMapper;
using SEFA.Base;
using SEFA.Base.Common.HttpContextUser;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Services.BASE;
using SEFA.DFM.Common;
using SEFA.DFM.IServices;
using SEFA.DFM.Model;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Newtonsoft.Json;
using SEFA.Base.Common;
using SEFA.Base.Common.HttpRestSharp;
using SEFA.Base.Common.LogHelper;

namespace SEFA.DFM.Services
{
    public class StaffSchedulingServices : BaseServices<StaffSchedulingEntity>, IStaffSchedulingServices
    {
        private readonly IBaseRepository<StaffSchedulingEntity> _staffSchedulingRepository;
        private readonly IBaseRepository<StaffDutyLineEntity> _staffDutyLineRepository;
        private readonly IBaseRepository<ShiftEntity> _shiftRepository;

        // private readonly IBaseRepository<StaffChangeLineEntity> _staffChangeLineRepository;
        private readonly IBaseRepository<StaffEntity> _staffRepository;

        //private readonly IDFMServices _dfmServices;
        private readonly IUser _user;

        private readonly IMapper _mapper;
        private readonly IBaseRepository<ProductlineEntity> _productLineRepository;
        private readonly IBaseRepository<PostEntity> _postRepository;
        private readonly ITeamServices _teamServices;
        private readonly IPostServices _postServices;
        private readonly IStaffDutyLineServices _staffDutyLineServices;

        private readonly ICalendarServices _calendarServices;
        //private readonly IUWBServices _uwbServices;

        private readonly string _kqWorkPsnUrl = Appsettings.app("ThirdPartyInterface", "ATT", "kqWorkPsn");

        public StaffSchedulingServices(IBaseRepository<StaffSchedulingEntity> staffSchedulingRepository,
            // IBaseRepository<StaffChangeLineEntity> staffChangeLineRepository,
            IBaseRepository<StaffDutyLineEntity> staffDutyLineRepository,
            IBaseRepository<StaffEntity> staffRepository,
            IBaseRepository<ProductlineEntity> productLineRepository,
            IBaseRepository<PostEntity> postRepository,
            IPostServices postServices,
            IBaseRepository<ShiftEntity> shiftRepository,
            ITeamServices teamServices,
            IStaffDutyLineServices staffDutyLineServices,
            //IUWBServices uwbServices,
            //IDFMServices dfmServices,
            ICalendarServices calendarServices,
            IUser user,
            IMapper mapper)
        {
            //_uwbServices = uwbServices;
            this._staffSchedulingRepository = staffSchedulingRepository;
            base.BaseDal = staffSchedulingRepository;
            _staffRepository = staffRepository;
            //_dfmServices = dfmServices;
            _staffDutyLineRepository = staffDutyLineRepository;
            // _staffChangeLineRepository = staffChangeLineRepository;
            _productLineRepository = productLineRepository;
            _postRepository = postRepository;
            _shiftRepository = shiftRepository;
            _calendarServices = calendarServices;
            _teamServices = teamServices;
            _user = user;
            _mapper = mapper;
            _postServices = postServices;
            _staffDutyLineServices = staffDutyLineServices;
        }

        /// <summary>
        /// 批量删除排班数据
        /// </summary>
        /// <param name="staffSchedulings"></param>
        /// <returns></returns>
        public async Task<bool> DeleteBatchStaffScheduling(List<StaffSchedulingEntity> staffSchedulings)
        {
            bool result = false;
            if (staffSchedulings.Count > 0)
            {
                result = await _staffDutyLineRepository.Db.Deleteable<StaffSchedulingEntity>(staffSchedulings)
                    .PageSize(1000).ExecuteCommandAsync() > 0;
            }

            return result;
        }

        /// <summary>
        /// 生成 人员排班数据
        /// </summary>
        /// <returns></returns>
        public async Task GenerateStaffScheduling(DateTime? inputdate)
        {
            string lineCode = string.Empty;
            DateTime date = inputdate.Value.Date;
            //获取产线数据
            //var allLines = await _dfmServices.GetProductLine(new List<GetProductlineInfoDto>());
            var allLines = await _productLineRepository.Query();
            if (lineCode.IsNotEmptyOrNull())
            {
                allLines = allLines.Where(x => x.ProductlineCode == lineCode).ToList();
            }

            var allSegmentIds = allLines.Select(x => x.ID).ToList(); //工段ID

            var allLineIds = allLines.Select(x => x.ParentId).Distinct().ToList(); //产线ID
            //获取 工段 工厂日历信息
            //var calendarInfos = await _dfmServices.GetCalendarShiftInfo(allSegmentIds, date);
            var calendarInfos =
                _calendarServices.QueryListByModelids(allSegmentIds.ToArray(), date.ToString("yyyy-MM-dd HH24:mi:ss"));

            //获取HR 人员排班的基础信息
            var baseData = await _staffDutyLineRepository.FindList(x => allLineIds.Contains(x.LineId));
            var staffCodes = baseData.Select(x => x.StaffCode).ToList();

            #region 人员基础信息

            var staffInfos = await _staffRepository.FindList(x => staffCodes.Contains(x.Code));

            #endregion 人员基础信息

            var bigData = new List<StaffSchedulingEntity>();

            //获取所有的岗位信息
            var allPost = await _postRepository.Query();

            //循环生成数据
            foreach (var item in allLines)
            {
                //获取工段安排的人员
                var baseStaffs = baseData.Where(x => x.LineId == item.ParentId && x.SegmentId == item.ID).ToList();
                if (baseStaffs.Count == 0)
                {
                    continue;
                }

                //获取  日历信息(产线)
                var calendarInfo = calendarInfos.Where(x => x.LineID == item.ID).ToList();
                if (calendarInfo.Count() == 0)
                {
                    //没有日历不排班
                    continue;
                }

                #region 获取上一个班次的数据

                var lastDate = DateTime.Now.Date;
                var lastTime = calendarInfo.FirstOrDefault()?.LastTime;
                if (!string.IsNullOrEmpty(lastTime))
                {
                    lastDate = DateTime.Parse(lastTime).Date;
                }

                // var lastDate = calendarInfo.FirstOrDefault()?.LastTime?.Date;
                var calTeamIds = calendarInfo.Select(x => x.TeamId).ToList(); //日历的班组ID；

                //工段下昨天所有的数据
                var allYesterdayInfos = await _staffSchedulingRepository.FindList(x =>
                    x.Date < lastDate
                    && x.SegmentId == item.ID
                    && !string.IsNullOrEmpty(x.TeamId)
                    && !string.IsNullOrEmpty(x.ShiftId));

                ////通过今天的班组 信息过滤后的数据 （今天日历只有A1排班，昨日的数据就剩下A1）
                //var yesterdayInfos = _mapper.Map<List<StaffSchedulingDto>>(allYesterdayInfos);
                //yesterdayInfos = yesterdayInfos.Where(x => calTeamIds.Where(y => x.TeamIds.Contains(y)).Any()).ToList();

                #endregion 获取上一个班次的数据

                foreach (var staff in baseStaffs)
                {
                    var exist = bigData.Where(x => x.StaffCode == staff.StaffCode
                                                   && x.LineId == item.ParentId
                                                   && x.SegmentId == item.ID
                    ).ToList();
                    if (exist.Count > 0)
                    {
                        continue;
                    }

                    var entity = new StaffSchedulingEntity();
                    entity.Type = staff?.Type; //分类
                    entity.Type2 = staff?.Type2; //类型
                    entity.Type3 = staff?.Type3; //类别
                    entity.StaffName = staff?.StaffName;
                    entity.StaffCode = staff?.StaffCode;
                    entity.Geonum = item.PropertyValue; //区域编号
                    entity.Line = item.ProductlineName;
                    entity.LineId = item.ParentId;
                    entity.LineCode = item.ProductlineCode;
                    entity.Segment = item.SegmentName;
                    entity.SegmentId = item.ID;
                    entity.SegmentShortName = item.SegmentShortname;
                    entity.Process = staff?.Process;
                    entity.Date = date;
                    entity.PieceworkPost = staff?.PricePost;
                    entity.AndonPost = staff?.AndonPost;

                    var andonPostInfo = allPost.Where(x => x.Name == entity.AndonPost && x.Type == "Andonwork")
                        .FirstOrDefault();
                    if (andonPostInfo != null)
                    {
                        entity.AndonPostRange = andonPostInfo.PostRange;
                    }

                    entity.MaterialCode = staff?.MaterialCode; //物料号  工段产线会有多个物料号

                    //人员基础信息
                    var staffInfo = staffInfos.Where(x => x.Code == staff.StaffCode).FirstOrDefault();
                    entity.StaffUwb = staffInfo?.Uwb;
                    entity.JobTitle = staffInfo?.JobTitle;

                    #region 注释代码

                    ////查找数据中有没有 同产线同工段 不同班次的人
                    //var equalStaff = bigData.Where(x => x.StaffCode == entity.StaffCode
                    //&& x.LineId == entity.LineId
                    //&& x.SegmentId == entity.SegmentId
                    //).ToList();
                    //if (equalStaff.Count>0)
                    //{
                    //    var otherTeam= equalStaff.Select(x=>x.Team).ToList();
                    //    yesterdayInfo= yesterdayInfos.Where(x => x.StaffCode == entity.StaffCode
                    //    && x.LineId == entity.LineId
                    //    && x.SegmentId == entity.SegmentId
                    //    && !otherTeam.Contains(x.Team)
                    //    ).FirstOrDefault();
                    //}

                    #endregion 注释代码

                    //前一天排班数据 (排班数据通过了)
                    var yesterdayInfo = allYesterdayInfos.Where(x =>
                        x.StaffCode == entity.StaffCode
                        && x.LineId == entity.LineId
                        && x.SegmentId == entity.SegmentId
                        && x.Date < entity.Date
                    ).FirstOrDefault();
                    entity.Team = yesterdayInfo?.Team;
                    entity.TeamId = yesterdayInfo?.TeamId;
                    if (entity.Team != null)
                    {
                        //有的记录=会配置多个班组
                        var teamIds = entity.TeamId.Split(',').ToList();
                        var cal = calendarInfo.Where(x => x.LineID == entity.SegmentId && teamIds.Contains(x.TeamId))
                            .OrderBy(x => x.StartTime).FirstOrDefault();
                        entity.Shift = cal?.Shift; //班次
                        entity.ShiftId = cal?.ShiftId;
                        ; //班次ID
                    }

                    entity.State = StaffSchedulingState.Absenteeism;
                    entity.CreateCustomGuid(_user.Name);
                    entity.CreateUserId = _user.Name;
                    bigData.Add(entity);
                }
            }

            var allDate = bigData;
            if (lineCode.IsNotEmptyOrNull())
            {
                //只删除指定产线的数据
                await _staffSchedulingRepository.Delete(x => x.Date == date && x.LineCode == lineCode);
            }
            else
            {
                //删除所有产线的数据
                await _staffSchedulingRepository.Delete(x => x.Date == date);
            }

            #region 人员借调数据

            //var staffChangeLineInfos = await _staffChangeLineRepository.FindList(x => staffCodes.Contains(x.StaffCode)
            // && x.StartTime <= DateTime.Now.Date
            // && x.EndTime >= DateTime.Now.Date
            // );

            ////处理人员借调数据
            //foreach (var staffChange in staffChangeLineInfos)
            //{
            //    var staffNewInfos = bigData.Where(x => x.StaffCode == staffChange.StaffCode
            //     && x.LineId == staffChange.SourceLineId
            //     && x.SegmentId == staffChange.SourceSegmentId
            //     );
            //    foreach (var item in staffNewInfos)
            //    {
            //        item.Team = staffChange.TargetTeam;
            //        item.TeamId = staffChange.TargetTeamId;
            //        var cal = calendarInfos.Where(x => x.MODELID == item.SegmentId && x.TeamId == item.TeamId).FirstOrDefault();
            //        item.Shift = cal?.Shift;
            //        item.ShiftId = cal?.ShiftId;
            //    }
            //}

            #endregion 人员借调数据

            //新增数据
            var result = await _staffSchedulingRepository.Add(bigData);
        }

        /// <summary>
        /// 根据排班生成员工预出勤信息
        /// </summary>
        /// <param name="calendarEntity"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<List<StaffSchedulingEntity>> GenerateStaffSchedulingByCalendar(CalendarEntity calendarEntity)
        {
            List<StaffSchedulingEntity> staffSchedulings = new List<StaffSchedulingEntity>();
            DateRange dateRange = new DateRange(Convert.ToDateTime(calendarEntity.Starttime),
                Convert.ToDateTime(calendarEntity.Endtime));
            List<DateTime> dateList = dateRange.GetDatesInRange();
            List<ShiftEntity> shiftEntities = await _shiftRepository.FindList(a => a.Deleted == 0);
            if (dateList.Count > 0)
            {
                List<StaffDutyLineEntity> staffDutyLines = await GetStaffDutyLineByCalendar(calendarEntity);
                if (staffDutyLines.Count > 0)
                {
                    staffDutyLines.ForEach(a =>
                    {
                        dateList.ForEach(b =>
                        {
                            ShiftEntity shift = shiftEntities.Where(a => a.ID.Equals(calendarEntity.Shiftid))
                                .FirstOrDefault();
                            StaffSchedulingEntity entity = new StaffSchedulingEntity()
                            {
                                Date = b.Date,
                                LineId = a.LineId,
                                LineCode = a.LineCode,
                                Line = a.Line,
                                SegmentId = a.SegmentId,
                                Segment = a.Segment,
                                SegmentShortName = a.SegmentShortName,
                                Team = a.Team,
                                TeamId = a.TeamId,
                                Shift = shift.Name,
                                ShiftId = calendarEntity.Shiftid,
                                CreateUserId = _user.Name,
                                StaffCode = a.StaffCode,
                                StaffName = a.StaffName,
                                ClockInTime = calendarEntity.Starttime,
                                ClockOffTime = calendarEntity.Endtime,
                                AndonPost = a.AndonPost,
                                CreateDate = DateTime.UtcNow,
                                ModifyDate = DateTime.UtcNow,
                                ModifyUserId = _user.Name
                            };
                            staffSchedulings.Add(entity);
                        });
                    });
                }
            }

            return staffSchedulings;
        }

        /// <summary>
        /// 根据排班获取人员数据
        /// </summary>
        /// <param name="calendarEntity"></param>
        /// <returns></returns>
        public async Task<List<StaffDutyLineEntity>> GetStaffDutyLineByCalendar(CalendarEntity calendarEntity)
        {
            List<StaffDutyLineEntity> staffDutyLineList = await _staffDutyLineRepository.Db
                .Queryable<StaffDutyLineEntity>()
                .Where(a => (a.SegmentId.Equals(calendarEntity.MODELID) || a.LineId.Equals(calendarEntity.MODELID)) &&
                            a.TeamId.Equals(calendarEntity.Teamid))
                .ToListAsync();
            return staffDutyLineList;
        }

        public async Task<bool> AddBatchStaffScheduling(List<StaffSchedulingEntity> staffSchedulings)
        {
            var result = false;
            result = await this.AddBigData(staffSchedulings) > 0;
            return result;
        }

        /// <summary>
        /// 根据安灯岗位信息获取人员出勤信息
        /// </summary>
        /// <param name="andonStaffReuqestModel"></param>
        /// <returns></returns>
        public async Task<List<StaffSchedulingEntity>> GetStaffSchedulingFromAndon(
            AndonStaffReuqestModel andonStaffReuqestModel)
        {
            List<StaffSchedulingEntity> staffSchedulingEntities = new List<StaffSchedulingEntity>();

            var teamId = andonStaffReuqestModel.TeamId;

            var postList = andonStaffReuqestModel.AndonPosts;
            if (postList.Count > 0)
            {
                List<PostEntity> postEntityList =
                    await _postServices.FindList(a => postList.Contains(a.Name) && a.PostRange == "1");
                if (postEntityList.Count > 0)
                {
                    var whereExpression = Expressionable.Create<StaffDutyLineEntity>().
                        And(a => postEntityList.Select(b => b.Name).Contains(a.AndonPost))
                        .AndIF(!string.IsNullOrEmpty(andonStaffReuqestModel.Segment),
                    a => a.SegmentId.Equals(andonStaffReuqestModel.Segment) || string.IsNullOrEmpty(a.SegmentId)).ToExpression();
                    List<StaffDutyLineEntity> staffDutyLineList =
                        await _staffDutyLineServices.FindList(whereExpression);
                    if (staffDutyLineList.Count > 0)
                    {
                        List<StaffSchedulingEntity> schedulingList = staffDutyLineList.Select(a =>
                            new StaffSchedulingEntity
                            {
                                StaffCode = a.StaffCode,
                                StaffName = a.StaffName
                            }).ToList();
                        staffSchedulingEntities.AddRange(schedulingList);
                    }
                }
            }

            List<StaffSchedulingEntity> staffSchedulingList = await _staffSchedulingRepository.Db
                .Queryable<StaffSchedulingEntity>()
                .WhereIF(!string.IsNullOrWhiteSpace(teamId), a => a.TeamId == teamId)
                .WhereIF(!string.IsNullOrEmpty(andonStaffReuqestModel.Line),
                    a => a.LineId.Equals(andonStaffReuqestModel.Line))
                .WhereIF(!string.IsNullOrEmpty(andonStaffReuqestModel.Segment),
                    a => a.SegmentId.Equals(andonStaffReuqestModel.Segment))
                .WhereIF(andonStaffReuqestModel.AndonPosts.Count > 0,
                    a => andonStaffReuqestModel.AndonPosts.Contains(a.AndonPost))
                .Where(a => (a.Date == DateTime.Today) && ((DateTime.Now > a.ClockInTime &&
                                                            (DateTime.Now < a.ClockOffTime ||
                                                             a.ClockOffTime == null)) || a.Type == "间接"))
                .ToListAsync();
            if (staffSchedulingList.Count > 0)
            {
                staffSchedulingEntities.AddRange(staffSchedulingList);
            }
            staffSchedulingEntities = staffSchedulingEntities.DistinctBy(a => a.StaffCode).ToList();
            return staffSchedulingEntities;
        }

        public async Task<MessageModel2<string>> PushStaffAttendance()
        {
            var result = new MessageModel2<string>();
            result.success = false;
            List<StaffAttendance> attendances = new List<StaffAttendance>();
            var shift = (DateTime.Now.Hour >= 8 && DateTime.Now.Hour <= 20) ? "白班" : "夜班";
            try
            {
                var attResult = await HttpHelper.PostAsync<List<StaffAttendance>>("ATT", _kqWorkPsnUrl,
                    _user.GetToken(), new
                    {
                        Datetime = DateTime.Today.ToString("yyyy-MM-dd"),
                        Shift = shift
                    });
                if (attResult.response != null)
                {
                    attendances = attResult.response;
                }

                if (attendances.Count == 0)
                {
                    SerilogServer.LogDebug($"考勤数据获取为空", "考勤对接日志");
                    result.success = true;
                    result.msg = "考勤数据获取为空";
                    return result;
                }

                List<string> staffCodeList = attendances.Select(a => a.StaffCode).ToList();
                List<StaffDutyLineEntity> staffDutyLineEntities =
                    await _staffDutyLineRepository.FindList(a => staffCodeList.Contains(a.StaffCode));
                if (staffDutyLineEntities.Count == 0)
                {
                    SerilogServer.LogDebug($"考勤数据中的人员在人员产线中未找到！", "考勤对接日志");
                    result.success = true;
                    result.msg = "考勤数据中的人员在人员产线中未找到";
                    return result;
                }

                var staffSchedulingList = staffDutyLineEntities.Join(attendances,
                    dl => dl.StaffCode,
                    a => a.StaffCode,
                    (dl, a) => new StaffSchedulingEntity
                    {
                        Date = DateTime.Today,
                        Line = dl.Line,
                        LineCode = dl.LineCode,
                        LineId = dl.LineId,
                        Segment = dl.SegmentShortName,
                        SegmentId = dl.SegmentId,
                        SegmentShortName = dl.Segment,
                        Process = dl.Process,
                        Team = dl.Team,
                        TeamId = dl.TeamId,
                        Shift = a.Shift,
                        StaffCode = dl.StaffCode,
                        StaffName = dl.StaffName,
                        AndonPost = dl.AndonPost,
                        Type = a.StaffType,
                        State = StaffSchedulingState.Normal,
                        ClockInTime = a.PlanStartTime,
                        ClockOffTime = a.PlanEndTime,
                        ID = Guid.NewGuid().ToString(),
                        CreateDate = DateTime.Now,
                        CreateUserId = "System",
                        ModifyDate = DateTime.Now,
                        ModifyUserId = "System"
                    }).ToList();
                if (!staffSchedulingList.Any())
                {
                    SerilogServer.LogDebug($"考勤数据未找到人员数据", "考勤对接日志");
                    result.success = true;
                    result.msg = "考勤数据未找到人员数据！";
                    return result;
                }

                var count = await _staffSchedulingRepository.Db.Queryable<StaffSchedulingEntity>()
                    .Where(a => a.Date == DateTime.Today && a.Shift == shift).CountAsync();
                if (count > 0)
                {
                    await _staffSchedulingRepository.Db.Deleteable<StaffSchedulingEntity>()
                        .Where(a => a.Date == DateTime.Today && a.Shift == shift).ExecuteCommandAsync();
                }

                var resultCount = await _staffSchedulingRepository.Db.Fastest<StaffSchedulingEntity>().PageSize(1000)
                    .BulkCopyAsync(staffSchedulingList) > 0;
                if (resultCount)
                {
                    result.success = true;
                    result.msg = "考勤数据同步成功！";
                    return result;
                }
                else
                {
                    result.msg = "考勤数据同步失败！";
                    return result;
                }
            }
            catch (Exception e)
            {
                SerilogServer.LogError(e, $"考勤数据保存失败！原因为：{JsonConvert.SerializeObject(e)}", "考勤对接日志");
                result.success = false;
                result.msg = $"考勤数据保存失败！原因为：{JsonConvert.SerializeObject(e)}!";
                return result;
            }

            return result;
        }

        /// <summary>
        /// 根据排班获取之前的所有人员出勤数据
        /// </summary>
        /// <param name="calendarModel"></param>
        /// <returns></returns>
        public async Task<List<StaffSchedulingEntity>> GetStaffSchedulingByCalendar(CalendarModel calendarModel)
        {
            List<string> equipmentCodeList = calendarModel.EquipmentList.Select(x => x.EquipmentCode).ToList();
            List<StaffSchedulingEntity> staffSchedulingList = await _staffSchedulingRepository.Db
                .Queryable<StaffSchedulingEntity>().Where(a =>
                    a.Date >= calendarModel.Starttime && a.Date <= calendarModel.Endtime &&
                    (equipmentCodeList.Contains(a.LineId) || equipmentCodeList.Contains(a.SegmentId))).ToListAsync();
            return staffSchedulingList;
        }
    }
}