
using SEFA.Base;
using SEFA.Base.Common.HttpRestSharp;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Services.BASE;
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using SqlSugar;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace SEFA.DFM.Services
{
    public class StaffServices : BaseServices<StaffEntity>, IStaffServices
    {
        private readonly IBaseRepository<StaffEntity> _dal;

        public StaffServices(IBaseRepository<StaffEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        /// <summary>
        /// Code 是否存在
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        /// <exception cref="System.NotImplementedException"></exception>
        public async Task<bool> IsExistCode(StaffEntity model)
        {
            var whereExpression = Expressionable.Create<StaffEntity>().And(x => x.Deleted == 0)
            .And(x => x.Code.Equals(model.Code))
            .AndIF(!string.IsNullOrEmpty(model.ID), x => x.ID != model.ID)
            .ToExpression();
            //List<StaffEntity> list = await FindList(whereExpression);
            //return list.Any();
            var entity = await FindEntity(whereExpression);
            return entity != null;

        }

        public Task<List<StaffDto>> GetList(StaffRequestModel reqModel)
        {
            throw new System.NotImplementedException();
        }

        /// <summary>
        /// 获取员工 组织结构
        /// </summary>
        /// <param name="entity"></param>
        /// <param name="OrgId"></param>
        /// <returns></returns>
        /// <exception cref="System.NotImplementedException"></exception>
        public async Task<string> GetOrganization(StaffDto dto)
        {
            //var org = await GetDepartments();
            //var factoryName = org.Where(x => x.ID.Equals(dto.FactoryId)).FirstOrDefault()?.Fullname;
            //var departementName = org.Where(x => x.ID.Equals(dto.DepartementId)).FirstOrDefault()?.Fullname;
            //var jobContentName = org.Where(x => x.ID.Equals(dto.JobContentId)).FirstOrDefault()?.Fullname;
            //return $"{factoryName}-{departementName}-{jobContentName}";
            return "";
        }


        /// <summary>
        /// 获取 组织结构树
        /// </summary>
        /// <returns></returns>
        public async Task<List<DepartmentDto>> GetDepartments()
        {
            var data = await HttpHelper.PostAsync<List<DepartmentDto>>("DFM", "api/Department/GetList", "");
            return data.response;
        }






    }
}