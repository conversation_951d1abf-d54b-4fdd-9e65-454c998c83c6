
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SqlSugar;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using SEFA.Base.Common.HttpContextUser;

namespace SEFA.DFM.Services
{
    public class EquipmentStorageServices : BaseServices<EquipmentStorageEntity>, IEquipmentStorageServices
    {
        private readonly IBaseRepository<EquipmentStorageEntity> _dal;
        private readonly IMapper _mapper;
        private readonly IUser _user;
        public EquipmentStorageServices(IBaseRepository<EquipmentStorageEntity> dal, IMapper mapper, IUser user)
        {
            this._dal = dal;
            base.BaseDal = dal;
            this._mapper = mapper;
            this._user = user;
        }

        public async Task<List<EquipmentStorageEntity>> GetList(EquipmentStorageRequestModel reqModel)
        {
            List<EquipmentStorageEntity> result = new List<EquipmentStorageEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<EquipmentStorageEntity>()
                             .ToExpression();
            var data = await _dal.Db.Queryable<EquipmentStorageEntity>()
                .Where(whereExpression).ToListAsync();
            return data;
        }
        public async Task<EquipmentStorageRequestModel> GetEquipmentStorage (EquipmentStorageRequestModel reqModel) {
            var data = await _dal.Db.Queryable<EquipmentStorageEntity>()
                .Where(a=>a.EquipmentId == reqModel.EquipmentId).ToListAsync();
            return data.FirstOrDefault() == null ? null : _mapper.Map<EquipmentStorageRequestModel>(data.FirstOrDefault());
        }
        

        public async Task<PageModel<EquipmentStorageEntity>> GetPageList(EquipmentStorageRequestModel reqModel)
        {
            PageModel<EquipmentStorageEntity> result = new PageModel<EquipmentStorageEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<EquipmentStorageEntity>()
                             .ToExpression();
            var data = await _dal.Db.Queryable<EquipmentStorageEntity>()
                .Where(whereExpression)
                .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
            result.dataCount = dataCount;
            result.data = data;
            return result;
        }

        public async Task<bool> SaveForm(EquipmentStorageEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                entity.CreateCustomGuid(_user.Name);
                return await this.Add(entity) > 0;
            }
            else
            {
                entity.Modify(entity.ID, _user.Name);
                return await this.Update(entity);
            }
        }
    }
}