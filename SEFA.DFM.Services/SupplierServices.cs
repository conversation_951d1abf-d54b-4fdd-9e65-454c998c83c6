
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;

namespace SEFA.DFM.Services
{
    public class SupplierServices : BaseServices<SupplierEntity>, ISupplierServices
    {
        private readonly IBaseRepository<SupplierEntity> _dal;
        public SupplierServices(IBaseRepository<SupplierEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }
    }
}