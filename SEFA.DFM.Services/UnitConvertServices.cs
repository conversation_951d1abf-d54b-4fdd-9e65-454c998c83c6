
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;

namespace SEFA.DFM.Services
{
    public class UnitConvertServices : BaseServices<UnitConvertEntity>, IUnitConvertServices
    {
        private readonly IBaseRepository<UnitConvertEntity> _dal;
        public UnitConvertServices(IBaseRepository<UnitConvertEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }
    }
}