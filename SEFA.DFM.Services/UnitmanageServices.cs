
using SEFA.DFM.IServices;
using SEFA.DFM.Model.Models;
using SEFA.Base.Services.BASE;
using SEFA.Base.IRepository.Base;
using System.Threading.Tasks;
using SqlSugar;

namespace SEFA.DFM.Services
{
    public class UnitmanageServices : BaseServices<UnitmanageEntity>, IUnitmanageServices
    {
        private readonly IBaseRepository<UnitmanageEntity> _dal;
        public UnitmanageServices(IBaseRepository<UnitmanageEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        /// <summary>
        /// 判断编码是否重复
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<bool> isExist(UnitmanageEntity request)
        {
            var whereExpression = Expressionable.Create<UnitmanageEntity>().And(a => a.Deleted == 0 && a.Name == request.Name ).ToExpression();

            var list = await FindList(whereExpression);
            if (list != null && list.Count > 0)
            {
                return false;
            }
            return true;
        }
    }
}