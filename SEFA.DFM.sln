
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.32014.148
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "SEFA.DFM.Api", "SEFA.DFM.Api\SEFA.DFM.Api.csproj", "{6F47A41A-085E-4422-BB73-5A2CBAA07D9F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "SEFA.DFM.Model", "SEFA.DFM.Model\SEFA.DFM.Model.csproj", "{E725F0A1-0B03-406F-B84B-0F486C6137FC}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "SEFA.DFM.Repository", "SEFA.DFM.Repository\SEFA.DFM.Repository.csproj", "{8D651E7F-49D3-4D27-8486-ADCF000BB24D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "SEFA.DFM.IServices", "SEFA.DFM.IServices\SEFA.DFM.IServices.csproj", "{37BB8600-94DA-4A2C-9230-DE93EA1EB0BD}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "SEFA.DFM.Services", "SEFA.DFM.Services\SEFA.DFM.Services.csproj", "{A2EFEFFC-39AD-48D2-8337-E6840B26023B}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "SolutionItems", "SolutionItems", "{D9833F24-7BD0-486F-B028-F1FD098AA1E1}"
	ProjectSection(SolutionItems) = preProject
		.dockerignore = .dockerignore
		.editorconfig = .editorconfig
		.gitignore = .gitignore
		codecov.yml = codecov.yml
		CreateYourProject.bat = CreateYourProject.bat
		DockerBuild.bat = DockerBuild.bat
		Dockerfile = Dockerfile
		nuget.config = nuget.config
		README.md = README.md
		SEFA.DFM.Build.bat = SEFA.DFM.Build.bat
		SEFA.DFM.Publish.bat = SEFA.DFM.Publish.bat
		SEFA.DFM.Publish.Docker.Jenkins.sh = SEFA.DFM.Publish.Docker.Jenkins.sh
		SEFA.DFM.Publish.Docker.sh = SEFA.DFM.Publish.Docker.sh
		SEFA.DFM.Publish.Linux.sh = SEFA.DFM.Publish.Linux.sh
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Gateways", "Gateways", "{E2BD7D4D-9ED5-41CD-8401-C3FB26F203BB}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Ocelot.Provider.Nacos", "Ocelot.Provider.Nacos\Ocelot.Provider.Nacos.csproj", "{6463FB13-5F01-4A1D-8B62-A454FB3812EB}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "SEFA.DFM.Common", "SEFA.DFM.Common\SEFA.DFM.Common.csproj", "{039FD26F-C11F-4A66-AC06-0E9BEF33BC40}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{6F47A41A-085E-4422-BB73-5A2CBAA07D9F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6F47A41A-085E-4422-BB73-5A2CBAA07D9F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6F47A41A-085E-4422-BB73-5A2CBAA07D9F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6F47A41A-085E-4422-BB73-5A2CBAA07D9F}.Release|Any CPU.Build.0 = Release|Any CPU
		{E725F0A1-0B03-406F-B84B-0F486C6137FC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E725F0A1-0B03-406F-B84B-0F486C6137FC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E725F0A1-0B03-406F-B84B-0F486C6137FC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E725F0A1-0B03-406F-B84B-0F486C6137FC}.Release|Any CPU.Build.0 = Release|Any CPU
		{8D651E7F-49D3-4D27-8486-ADCF000BB24D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8D651E7F-49D3-4D27-8486-ADCF000BB24D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8D651E7F-49D3-4D27-8486-ADCF000BB24D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8D651E7F-49D3-4D27-8486-ADCF000BB24D}.Release|Any CPU.Build.0 = Release|Any CPU
		{37BB8600-94DA-4A2C-9230-DE93EA1EB0BD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{37BB8600-94DA-4A2C-9230-DE93EA1EB0BD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{37BB8600-94DA-4A2C-9230-DE93EA1EB0BD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{37BB8600-94DA-4A2C-9230-DE93EA1EB0BD}.Release|Any CPU.Build.0 = Release|Any CPU
		{A2EFEFFC-39AD-48D2-8337-E6840B26023B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A2EFEFFC-39AD-48D2-8337-E6840B26023B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A2EFEFFC-39AD-48D2-8337-E6840B26023B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A2EFEFFC-39AD-48D2-8337-E6840B26023B}.Release|Any CPU.Build.0 = Release|Any CPU
		{6463FB13-5F01-4A1D-8B62-A454FB3812EB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6463FB13-5F01-4A1D-8B62-A454FB3812EB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6463FB13-5F01-4A1D-8B62-A454FB3812EB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6463FB13-5F01-4A1D-8B62-A454FB3812EB}.Release|Any CPU.Build.0 = Release|Any CPU
		{039FD26F-C11F-4A66-AC06-0E9BEF33BC40}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{039FD26F-C11F-4A66-AC06-0E9BEF33BC40}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{039FD26F-C11F-4A66-AC06-0E9BEF33BC40}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{039FD26F-C11F-4A66-AC06-0E9BEF33BC40}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{6463FB13-5F01-4A1D-8B62-A454FB3812EB} = {E2BD7D4D-9ED5-41CD-8401-C3FB26F203BB}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {AB40D0C5-E3EA-4A9B-86C2-38F0BB33FC04}
	EndGlobalSection
EndGlobal
